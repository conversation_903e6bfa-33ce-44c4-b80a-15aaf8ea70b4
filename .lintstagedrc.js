/**
 * Lint-staged configuration for Spheroseg
 * Optimized for performance with targeted file processing
 * 
 * Performance Optimizations:
 * - Process only changed files (lint-staged default)
 * - Use caching for ESLint and Prettier
 * - Consolidated TypeScript checking
 * - Parallel execution where possible
 * - Remove redundant validations
 */

// Cache configuration - use project root cache directories
const CACHE_DIR = '.cache';
const eslintCache = `${CACHE_DIR}/eslint`;
const prettierCache = `${CACHE_DIR}/prettier`;

// Helper functions for consistent command creation
const createESLintCommand = (extraArgs = '') => 
  `npx eslint --fix --cache --cache-location ${eslintCache} ${extraArgs}`;

const createPrettierCommand = (extraArgs = '') => 
  `prettier --write --cache --cache-location ${prettierCache} ${extraArgs}`;

module.exports = {
  // Frontend TypeScript/JavaScript files
  'packages/frontend/src/**/*.{ts,tsx,js,jsx}': [
    createESLintCommand(),
    createPrettierCommand(),
  ],
  
  // Backend TypeScript/JavaScript files
  'packages/backend/src/**/*.{ts,js}': [
    createESLintCommand(),
    createPrettierCommand(),
  ],
  
  // Shared package files
  'packages/shared/src/**/*.{ts,tsx}': [
    createESLintCommand(),
    createPrettierCommand(),
  ],
  
  // Python files (ML package)
  'packages/ml/**/*.py': [
    'python -m black --check --diff',
    'python -m isort --check-only --diff',
    'python -m flake8 --max-line-length=88 --extend-ignore=E203,W503',
  ],
  
  // CSS/SCSS files
  '**/*.{css,scss}': [
    createPrettierCommand(),
  ],
  
  // JSON files
  '**/*.json': [
    createPrettierCommand(),
    // JSON validation
    (filenames) => `node -e "${filenames.map(f => `JSON.parse(require('fs').readFileSync('${f}', 'utf8'))`).join(';')}"`,
  ],
  
  // Markdown files
  '**/*.md': [
    createPrettierCommand(),
  ],
  
  // YAML files
  '**/*.{yml,yaml}': [
    createPrettierCommand(),
  ],
  
  // Package.json files
  '**/package.json': [
    createPrettierCommand(),
  ],
  
  // TypeScript runs once for all files via turbo in CI
  // This ensures proper type checking without per-file overhead
};