{"name": "spheroseg", "version": "1.0.0", "description": "SpheroSeg - Cell Segmentation Application", "private": true, "packageManager": "npm@10.5.0", "type": "module", "workspaces": ["packages/*"], "devDependencies": {"@axe-core/playwright": "^4.10.2", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.25.1", "@playwright/test": "^1.54.1", "@types/bcryptjs": "^2.4.6", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.3", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "detect-secrets": "^1.0.6", "eslint": "^8.57.1", "eslint-config-prettier": "^10.1.5", "eslint-config-recommended": "^4.1.0", "eslint-plugin-react": "^7.37.5", "express": "^5.1.0", "glob": "^11.0.3", "globals": "^16.0.0", "happy-dom": "^17.4.6", "husky": "^9.1.7", "jscpd": "^4.0.0", "jsdom": "^26.1.0", "lint-staged": "^16.1.4", "markdownlint-cli": "^0.45.0", "playwright": "^1.54.1", "prettier": "^3.2.5", "socket.io-client": "^4.8.1", "turbo": "^2.5.3", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.4", "vitest": "^3.1.3", "ws": "^8.18.3"}, "scripts": {"dev": "turbo run dev", "build": "turbo run build", "build:prod": "turbo run build:prod", "start": "turbo run start", "start:prod": "turbo run start:prod", "preview": "turbo run preview", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "format:check": "turbo run format:check", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:coverage": "turbo run test:coverage", "test:ci": "turbo run test:ci", "test:watch": "turbo run test:watch", "coverage:check": "node scripts/coverage-check.cjs", "coverage:report": "npm run test:coverage && npm run coverage:check", "coverage:view": "open coverage/index.html", "code:check": "turbo run code:check", "code:fix": "turbo run code:fix", "type-check": "turbo run type-check", "clean": "turbo run clean", "clean:all": "npm run clean && rimraf node_modules package-lock.json && npm install", "dev:frontend": "turbo run dev --filter=@spheroseg/frontend", "dev:backend": "turbo run dev --filter=@spheroseg/backend", "dev:shared": "turbo run dev --filter=@spheroseg/shared", "build:frontend": "turbo run build --filter=@spheroseg/frontend", "build:backend": "turbo run build --filter=@spheroseg/backend", "build:shared": "turbo run build --filter=@spheroseg/shared", "test:frontend": "turbo run test --filter=@spheroseg/frontend", "test:backend": "turbo run test --filter=@spheroseg/backend", "test:shared": "turbo run test --filter=@spheroseg/shared", "test:ml": "turbo run test --filter=ml", "ml:segmentation": "turbo run segmentation --filter=ml", "ml:extract": "turbo run extract --filter=ml", "e2e": "playwright test", "e2e:open": "playwright test --ui", "e2e:headed": "playwright test --headed", "e2e:debug": "playwright test --debug", "e2e:coverage": "COLLECT_COVERAGE=true playwright test && npm run e2e:coverage:report", "e2e:coverage:report": "tsx scripts/e2e-coverage-report.ts test-results/results.json coverage-report", "e2e:coverage:open": "open coverage-report/index.html", "e2e:security": "playwright test security.spec.ts", "e2e:profile": "playwright test user-profile.spec.ts", "e2e:integration": "playwright test integration.spec.ts", "e2e:resilience": "playwright test resilience.spec.ts", "e2e:gdpr": "playwright test data-management.spec.ts", "e2e:features": "playwright test advanced-features.spec.ts", "e2e:collaboration": "playwright test collaboration.spec.ts", "e2e:monitoring": "bash scripts/run-monitoring-e2e-tests.sh", "duplicates": "npx jscpd . --config ./.jscpd.json", "init:db": "node scripts/init-db.js", "init:db:docker": "node scripts/init-db-docker.js", "db:migrate": "cd packages/backend && npm run migrate", "db:migrate:up": "cd packages/backend && npm run migrate:up", "db:migrate:down": "cd packages/backend && npm run migrate:rollback", "db:migrate:status": "cd packages/backend && npm run migrate:status", "db:create-test-user": "node scripts/create-test-user.js", "test:validation-scripts": "node scripts/test-validation-scripts.js", "validate:imports": "node scripts/validate-imports.js", "test:backup-recovery": "bash scripts/test-backup-recovery.sh", "backup:database": "bash scripts/backup/backup-database.sh", "restore:database": "bash scripts/rollback/restore-database.sh", "test:all": "node test-pipeline/comprehensive-test-system.cjs", "test:pre-commit": "node test-pipeline/comprehensive-test-system.cjs --quick", "test:post-deploy": "node test-pipeline/comprehensive-test-system.cjs --full --videos", "test:monitor": "node test-pipeline/comprehensive-test-system.cjs --monitor", "test:production": "TEST_URL=http://*************** npm run test:all", "audit:all": "turbo run audit", "deps:check": "turbo run deps:check", "deps:update": "turbo run deps:update", "setup": "npm install && npm run build && npm run db:migrate", "quick-start": "npm run build:shared && npm run dev:backend & npm run dev:frontend", "full-check": "npm run code:check && npm run test:unit && npm run test:integration", "pre-commit": "npm run code:fix && npm run test:unit", "pre-deploy": "npm run full-check && npm run build:prod && npm run e2e", "reset": "npm run clean:all && npm run setup", "status": "turbo run type-check && echo 'All packages type-checked successfully'", "prepare": "husky"}, "engines": {"node": ">=18.0.0"}, "dependencies": {"@prisma/client": "^6.13.0", "amqplib": "^0.10.8", "chalk": "^4.1.2", "form-data": "^4.0.4", "ioredis": "^5.7.0", "jimp": "^1.6.0", "nodemailer": "^7.0.5", "prisma": "^6.13.0", "puppeteer": "^24.15.0", "sharp": "^0.34.3"}}