{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build", "type-check"], "outputs": ["dist/**", ".next/**", "build/**"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "tsconfig.json", "package.json"]}, "build:prod": {"dependsOn": ["^build:prod", "type-check"], "outputs": ["dist/**", ".next/**", "build/**"], "env": ["NODE_ENV"]}, "start": {"dependsOn": ["build"], "cache": false}, "start:prod": {"dependsOn": ["build:prod"], "cache": false, "env": ["NODE_ENV"]}, "type-check": {"outputs": [".cache/tsconfig.tsbuildinfo"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "tsconfig.json"], "cache": true}, "dev": {"cache": false, "persistent": true}, "preview": {"dependsOn": ["build"], "cache": false}, "lint": {"outputs": [".eslint<PERSON>che", ".cache/eslint"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".eslintrc*", "eslint.config.*"]}, "lint:fix": {"cache": false, "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".eslintrc*", "eslint.config.*"]}, "test": {"dependsOn": ["type-check"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", "test/**/*.ts", "test/**/*.tsx", "tests/**/*.ts", "tests/**/*.tsx", "__tests__/**/*.ts", "__tests__/**/*.tsx", "jest.config.*", "vitest.config.*", "tsconfig.json"], "outputs": ["coverage/**", "test-results/**"]}, "test:unit": {"dependsOn": ["type-check"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx", "jest.config.*", "vitest.config.*"], "outputs": ["coverage/**"]}, "test:integration": {"dependsOn": ["build"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "**/*integration*.ts", "**/*integration*.tsx", "jest.config.*", "vitest.config.*"], "outputs": ["coverage/**", "test-results/**"]}, "test:watch": {"cache": false, "persistent": true}, "test:coverage": {"dependsOn": ["type-check"], "outputs": ["coverage/**", "reports/**"], "inputs": ["src/**/*.ts", "src/**/*.tsx", "test/**/*.ts", "test/**/*.tsx", "tests/**/*.ts", "tests/**/*.tsx", "__tests__/**/*.ts", "__tests__/**/*.tsx"]}, "test:ci": {"dependsOn": ["build"], "outputs": ["reports/**", "coverage/**", "test-results/**"], "env": ["CI"]}, "format": {"cache": false, "inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".prettierrc*"]}, "format:check": {"inputs": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.js", "src/**/*.jsx", ".prettierrc*"], "outputs": [".cache/prettier"]}, "clean": {"cache": false}, "code:check": {"dependsOn": ["lint", "format:check", "type-check"]}, "code:fix": {"dependsOn": ["lint:fix", "format"], "cache": false}, "audit": {"cache": false}, "deps:check": {"cache": false}, "deps:update": {"cache": false}, "test:ml": {"cache": false, "inputs": ["tests/**/*.py", "*.py"]}, "segmentation": {"cache": false}, "extract": {"cache": false}, "deploy": {"dependsOn": ["build:prod", "test:ci"], "cache": false, "env": ["NODE_ENV"]}, "deploy:dev": {"dependsOn": ["build", "test"], "cache": false}, "migrate": {"cache": false}, "migrate:up": {"cache": false}, "migrate:down": {"cache": false}}}