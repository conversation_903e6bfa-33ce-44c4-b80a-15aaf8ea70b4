# Production Dockerfile for SpheroSeg Backend
FROM node:20-alpine AS base

WORKDIR /app

# Install build dependencies and curl for health checks
RUN apk add --no-cache python3 make g++ curl postgresql-client

# Copy package files including lock file
COPY package.json package-lock.json ./
COPY packages/backend/package*.json ./packages/backend/
COPY packages/shared/package*.json ./packages/shared/ 
COPY packages/types/package*.json ./packages/types/

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy Prisma schema for generation
COPY packages/backend/prisma ./packages/backend/prisma

# Copy source code
COPY packages/backend/src ./packages/backend/src
COPY packages/backend/tsconfig.json ./packages/backend/
COPY packages/shared/src ./packages/shared/src
COPY packages/shared/tsconfig.json ./packages/shared/
COPY packages/types/src ./packages/types/src
COPY packages/types/tsconfig.json ./packages/types/
COPY tsconfig.base.json ./

# Build the application
RUN cd packages/backend && npx tsc --project tsconfig.json

# Generate Prisma client in build stage
RUN cd packages/backend && npx prisma generate

# Production stage
FROM node:20-alpine AS production

WORKDIR /app

# Install runtime dependencies
RUN apk add --no-cache curl postgresql-client

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files including lock file
COPY package.json package-lock.json ./
COPY packages/backend/package*.json ./packages/backend/
COPY packages/backend/prisma ./packages/backend/prisma

# Install only production dependencies
RUN npm ci --omit=dev && npm cache clean --force

# Generate Prisma client for production platform
RUN cd packages/backend && npx prisma generate

# Copy built application and generated Prisma files
COPY --from=base --chown=nodejs:nodejs /app/packages/backend/dist ./packages/backend/dist
COPY --from=base --chown=nodejs:nodejs /app/packages/backend/generated ./packages/backend/generated

# Create necessary directories
RUN mkdir -p uploads logs && \
    chown -R nodejs:nodejs uploads logs

# Switch to non-root user
USER nodejs

# Set production environment
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=768"

EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5001/api/health || exit 1

# Start the production server
CMD ["node", "packages/backend/dist/server.js"]