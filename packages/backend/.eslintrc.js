module.exports = {
  root: false,
  extends: ['../../.eslintrc.cjs'],
  env: {
    node: true,
    browser: false, // Override root config for backend
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js', 'dist/**/*', 'generated/**/*'],
  rules: {
    // Backend-specific rule overrides
    'no-console': 'off', // Allow console in backend services
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    'no-unused-vars': 'off', // Disable base rule as it conflicts with TypeScript version
    'no-undef': 'off', // TypeScript handles this
  },
  overrides: [
    {
      // Database and Prisma files
      files: ['**/prisma/**/*.ts', '**/migrations/**/*.ts'],
      rules: {
        '@typescript-eslint/no-explicit-any': 'off',
        'no-console': 'off',
      },
    },
  ],
};