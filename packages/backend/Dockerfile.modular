# Modular Production Dockerfile for SpheroSeg Backend
# Uses the clean modular architecture instead of monolithic server

FROM node:20-alpine AS builder

WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++ postgresql-client

# Copy root package files
COPY package.json yarn.lock ./

# Copy package.json files for all workspaces
COPY packages/backend/package.json packages/backend/
COPY packages/shared/package.json packages/shared/

# Install all dependencies (including dev dependencies for build)
RUN yarn install --frozen-lockfile --network-timeout 100000

# Copy source code
COPY packages/shared packages/shared
COPY packages/backend packages/backend

# Build shared package first
WORKDIR /app/packages/shared
RUN yarn build

# Build backend
WORKDIR /app/packages/backend
RUN yarn build

# Production stage
FROM node:20-alpine

WORKDIR /app

# Install runtime dependencies only
RUN apk add --no-cache postgresql-client curl

# Copy root package files
COPY package.json yarn.lock ./

# Copy package.json files
COPY packages/backend/package.json packages/backend/
COPY packages/shared/package.json packages/shared/

# Install production dependencies only
RUN yarn install --frozen-lockfile --production --network-timeout 100000

# Copy built files from builder
COPY --from=builder /app/packages/shared/dist packages/shared/dist
COPY --from=builder /app/packages/backend/dist packages/backend/dist

# Copy necessary runtime files
COPY packages/backend/src/services/emailServiceFixed.ts packages/backend/src/services/emailService.ts
COPY packages/backend/src/db/migrations packages/backend/src/db/migrations

# Create necessary directories
RUN mkdir -p uploads logs

# Set environment variables
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=768"

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5001/api/health || exit 1

# Run the modular server
CMD ["node", "packages/backend/dist/server.js"]