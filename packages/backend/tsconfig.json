{"extends": "./tsconfig.base.json", "compilerOptions": {"types": ["node", "jest"], "outDir": "dist", "noEmit": false, "strict": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false}, "include": ["src/**/*.ts", "generated/**/*.ts"], "exclude": ["node_modules", "dist", "test", "**/*.test.ts", "**/*.spec.ts", "**/__tests__/**", "**/tests/**", "src/db/legacy/**/*", "../shared/test-utils/**/*", "src/scripts/**/*"]}