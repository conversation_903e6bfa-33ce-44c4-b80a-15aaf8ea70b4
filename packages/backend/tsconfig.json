{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "types": ["node", "jest"],
    "outDir": "dist",
    "noEmit": false,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "removeComments": false,
    // Incremental compilation for faster builds
    "incremental": true,
    "tsBuildInfoFile": ".cache/tsconfig.tsbuildinfo",
    // Gradual strict mode migration
    "strict": false,
    "noImplicitAny": true
  },
  "include": [
    "src/**/*.ts",
    "generated/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts",
    "**/*.spec.ts",
    "**/__tests__/**",
    "**/tests/**",
    "src/scripts/**/*"
  ]
}