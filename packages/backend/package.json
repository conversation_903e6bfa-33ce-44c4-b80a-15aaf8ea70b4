{"name": "@spheroseg/backend", "version": "1.0.0", "license": "MIT", "main": "dist/server.js", "scripts": {"build": "tsc && tsc-alias", "build:prod": "NODE_ENV=production tsc && tsc-alias", "start": "node dist/server.js", "start:prod": "NODE_ENV=production node dist/server.js", "start:production": "npm run build:prod && npm run start:prod", "dev": "ts-node-dev --inspect=0.0.0.0:9229 --respawn --transpile-only --no-notify --no-deps --ignore-watch node_modules src/server.ts", "test:setup": "node scripts/setup-test-db.js", "test": "npm run test:setup && vitest run", "test:unit": "npm run test:setup && vitest run --exclude='**/*.integration.{test,spec}.ts'", "test:integration": "npm run test:setup && vitest run --include='**/*.integration.{test,spec}.ts'", "test:coverage": "npm run test:setup && vitest run --coverage", "test:ci": "npm run test:setup && vitest run --coverage --reporter=json --reporter=default", "test:watch": "npm run test:setup && vitest watch", "test:ui": "npm run test:setup && vitest --ui", "test:services": "npm run test:setup && vitest run src/services", "test:routes": "npm run test:setup && vitest run src/routes", "test:e2e": "playwright test e2e", "test:e2e:auth": "playwright test e2e --grep='Authentication'", "test:e2e:projects": "playwright test e2e --grep='Projects'", "test:e2e:segmentation": "playwright test e2e --grep='Segmentation'", "test:smoke": "playwright test e2e --grep='health check|login'", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\"", "type-check": "tsc --noEmit --incremental --tsBuildInfoFile .cache/tsconfig.tsbuildinfo", "code:check": "npm run lint && npm run format:check && npm run type-check", "code:fix": "npm run lint:fix && npm run format", "migrate": "ts-node src/db/runMigrations.ts", "migrate:prod": "NODE_ENV=production node dist/db/runMigrations.js", "migrate:status": "ts-node src/db/runMigrations.ts --status", "migrate:rollback": "ts-node src/db/runMigrations.ts --rollback", "migrate:access-requests": "node src/scripts/run-migration.js", "test:emails": "node test-emails.js", "audit": "node scripts/dependency-audit.js", "deps:check": "npm outdated", "deps:update": "node scripts/update-dependencies.js", "rotate-secrets": "ts-node -r tsconfig-paths/register ../../scripts/rotate-secrets.ts", "generate-thumbnails": "ts-node src/scripts/generate-missing-thumbnails.ts"}, "dependencies": {"@prisma/client": "^6.13.0", "@spheroseg/shared": "file:../shared", "@types/handlebars": "^4.0.40", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "amqplib": "^0.10.3", "archiver": "^5.3.2", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "commander": "^12.0.0", "compression": "^1.8.0", "connect-redis": "^7.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "express-session": "^1.18.2", "express-validator": "^7.2.1", "handlebars": "^4.7.8", "helmet": "^7.2.0", "i18next": "^25.3.2", "i18next-fs-backend": "^2.6.0", "i18next-http-middleware": "^3.7.4", "jimp": "^1.6.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwks-rsa": "^3.1.0", "lru-cache": "^11.1.0", "morgan": "^1.10.0", "multer": "^2.0.2", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^7.0.3", "pg": "^8.15.6", "polygon-clipping": "^0.15.7", "prisma": "^6.13.0", "prom-client": "^15.1.2", "rate-limiter-flexible": "^5.0.4", "redis": "^4.7.1", "sharp": "^0.34.3", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "ts-node": "^10.9.2", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.24.3"}, "devDependencies": {"@types/amqplib": "^0.10.7", "@types/archiver": "^6.0.3", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/connect-redis": "^0.0.23", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/express-rate-limit": "^5.1.3", "@types/express-session": "^1.18.2", "@types/helmet": "^0.0.48", "@types/jsonwebtoken": "^9.0.10", "@types/lru-cache": "^7.10.10", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^18.19.122", "@types/node-cache": "^4.2.5", "@types/node-cron": "^3.0.11", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/opossum": "^8.1.9", "@types/pg": "^8.15.4", "@types/redis": "^4.0.10", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^5.0.0", "adm-zip": "^0.5.10", "csv-parse": "^5.5.3", "eslint": "^8.57.1", "exceljs": "^4.4.0", "node-fetch": "^3.3.2", "opossum": "^9.0.0", "pg-mem": "^3.0.5", "prettier": "^3.2.5", "rimraf": "^5.0.5", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.6", "supertest": "^7.1.0", "ts-node-dev": "^2.0.0", "tsc-alias": "^1.8.10", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0", "vitest": "^3.1.3", "@vitest/coverage-v8": "^3.1.2", "@vitest/ui": "^3.1.3", "xlsx": "^0.18.5"}, "keywords": [], "author": "", "types": "./dist/server.d.ts", "description": ""}