/**
 * Production Server for SpheroSeg
 * Uses modular routes and proper configuration management
 */

import express, { Express } from 'express';
import http from 'http';
import { createLogger } from './utils/logger';
import { productionConfig } from './config/production.config';
import { setupMiddleware } from './setup/middleware.setup';
import { setupDatabase } from './setup/database.setup';
import { registerRoutes } from './routes/index';
import { setupErrorHandling } from './setup/errorHandling.setup';
import { setupGracefulShutdown } from './setup/shutdown.setup';
import { initializeSocketIO } from './socketServiceSimple';
// Email service initialized on demand
import { validateAndExit } from './utils/startupValidation';

const logger = createLogger('ProductionServer');

/**
 * Initialize and start the production server
 */
async function startProductionServer(): Promise<void> {
  try {
    logger.info({ message: 'Starting SpheroSeg Production Server...' });

    // Run startup validation first
    await validateAndExit();
    logger.info({ message: 'Startup validation passed' });

    // Create Express app
    const app: Express = express();
    const server = http.createServer(app);

    // 1. Setup database connection
    const pool = await setupDatabase(productionConfig);
    logger.info({ message: 'Database connected successfully' });

    // 2. Email service initialized on demand - no startup initialization needed
    logger.info({ message: 'Email service will be initialized on first use' });

    // Initialize Socket.IO
    initializeSocketIO(server);
    logger.info({ message: 'Socket.IO initialized' });

    // 3. Setup middleware
    await setupMiddleware(app, productionConfig);
    logger.info({ message: 'Middleware configured' });

    // 4. Setup routes
    registerRoutes({ app });
    logger.info({ message: 'Routes configured' });

    // 5. Setup error handling
    setupErrorHandling(app);
    logger.info({ message: 'Error handling configured' });

    // 6. Setup graceful shutdown
    setupGracefulShutdown(server, pool);
    logger.info({ message: 'Graceful shutdown configured' });

    // Start server
    server.listen(productionConfig.port, () => {
      logger.info(`🚀 Production server running on port ${productionConfig.port}`);
      logger.info(`📍 Environment: ${productionConfig.nodeEnv}`);
      logger.info(`🔗 CORS Origin: ${productionConfig.corsOrigin}`);
      logger.info(`📊 Database: Connected with ${productionConfig.maxConnections} max connections`);
      logger.info(`🔐 Security: JWT authentication enabled`);
      logger.info(`⚡ Performance: Compression level ${productionConfig.compressionLevel}`);
    });
  } catch (error) {
    logger.error('Failed to start production server:', error);
    process.exit(1);
  }
}

// Handle uncaught errors
process.on('uncaughtException', (error) => {
  logger.error({ message: 'Uncaught Exception', error: error as Error });
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error({ message: 'Unhandled Rejection', promise, reason });
  process.exit(1);
});

// Start the server
startProductionServer();
