/**
 * API Documentation Tests
 * Tests for API documentation accuracy, OpenAPI/Swagger compliance, and examples
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import SwaggerParser from '@apidevtools/swagger-parser';
import Ajv from 'ajv';
import addFormats from 'ajv-formats';
import jwt from 'jsonwebtoken';
import config from '../../config';
import testDb from '../helpers/testDb';

// Types for OpenAPI schema
interface OpenAPISchema {
  openapi: string;
  info: {
    title: string;
    version: string;
    description?: string;
  };
  servers: Array<{
    url: string;
    description?: string;
  }>;
  paths: {
    [path: string]: {
      [method: string]: {
        summary?: string;
        description?: string;
        tags?: string[];
        parameters?: Array<{
          name: string;
          in: string;
          required?: boolean;
          schema: any;
        }>;
        requestBody?: {
          required?: boolean;
          content: {
            [contentType: string]: {
              schema: any;
            };
          };
        };
        responses: {
          [statusCode: string]: {
            description: string;
            content?: {
              [contentType: string]: {
                schema: any;
              };
            };
          };
        };
        security?: Array<{ [name: string]: string[] }>;
      };
    };
  };
  components?: {
    schemas?: { [name: string]: any };
    securitySchemes?: { [name: string]: any };
  };
}

describe('API Documentation Tests', () => {
  let app: Express;
  let apiDocs: OpenAPISchema;
  let ajv: Ajv;
  let accessToken: string;
  let testUserId: number;

  beforeAll(async () => {
    app = await createApp();

    // Create test user for authenticated endpoints
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'Docs Test User']
    );
    testUserId = userResult.rows[0].id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Initialize AJV for JSON Schema validation
    ajv = new Ajv({ allErrors: true, strict: false });
    addFormats(ajv);

    // Fetch API documentation
    const docsResponse = await request(app).get('/api/docs/openapi.json');
    if (docsResponse.status === 200) {
      apiDocs = docsResponse.body;
    } else {
      // If no OpenAPI endpoint, create mock documentation
      apiDocs = {
        openapi: '3.0.0',
        info: {
          title: 'SpheroSeg API',
          version: '1.0.0',
          description: 'Cell segmentation application API',
        },
        servers: [
          {
            url: 'http://localhost:5001',
            description: 'Development server',
          },
          {
            url: 'https://spherosegapp.utia.cas.cz',
            description: 'Production server',
          },
        ],
        paths: {
          '/api/health': {
            get: {
              summary: 'Health check endpoint',
              tags: ['System'],
              responses: {
                '200': {
                  description: 'Service is healthy',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          status: { type: 'string', enum: ['ok', 'degraded', 'unhealthy'] },
                          timestamp: { type: 'string', format: 'date-time' },
                          version: { type: 'string' },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          '/api/auth/login': {
            post: {
              summary: 'User login',
              tags: ['Authentication'],
              requestBody: {
                required: true,
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      required: ['email', 'password'],
                      properties: {
                        email: { type: 'string', format: 'email' },
                        password: { type: 'string', minLength: 8 },
                      },
                    },
                  },
                },
              },
              responses: {
                '200': {
                  description: 'Login successful',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          accessToken: { type: 'string' },
                          refreshToken: { type: 'string' },
                          user: {
                            type: 'object',
                            properties: {
                              id: { type: 'integer' },
                              email: { type: 'string' },
                              name: { type: 'string' },
                            },
                          },
                        },
                      },
                    },
                  },
                },
                '401': {
                  description: 'Invalid credentials',
                },
              },
            },
          },
          '/api/projects': {
            get: {
              summary: 'List user projects',
              tags: ['Projects'],
              security: [{ bearerAuth: [] }],
              parameters: [
                {
                  name: 'page',
                  in: 'query',
                  schema: { type: 'integer', minimum: 1 },
                },
                {
                  name: 'limit',
                  in: 'query',
                  schema: { type: 'integer', minimum: 1, maximum: 100 },
                },
              ],
              responses: {
                '200': {
                  description: 'List of projects',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          projects: {
                            type: 'array',
                            items: {
                              type: 'object',
                              properties: {
                                id: { type: 'integer' },
                                name: { type: 'string' },
                                description: { type: 'string' },
                                created_at: { type: 'string', format: 'date-time' },
                                updated_at: { type: 'string', format: 'date-time' },
                              },
                            },
                          },
                          pagination: {
                            type: 'object',
                            properties: {
                              page: { type: 'integer' },
                              limit: { type: 'integer' },
                              total: { type: 'integer' },
                              totalPages: { type: 'integer' },
                            },
                          },
                        },
                      },
                    },
                  },
                },
                '401': {
                  description: 'Unauthorized',
                },
              },
            },
            post: {
              summary: 'Create new project',
              tags: ['Projects'],
              security: [{ bearerAuth: [] }],
              requestBody: {
                required: true,
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      required: ['name'],
                      properties: {
                        name: { type: 'string', minLength: 1, maxLength: 255 },
                        description: { type: 'string', maxLength: 1000 },
                      },
                    },
                  },
                },
              },
              responses: {
                '201': {
                  description: 'Project created',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          project: {
                            type: 'object',
                            properties: {
                              id: { type: 'integer' },
                              name: { type: 'string' },
                              description: { type: 'string' },
                              created_at: { type: 'string', format: 'date-time' },
                            },
                          },
                        },
                      },
                    },
                  },
                },
                '400': {
                  description: 'Invalid input',
                },
                '401': {
                  description: 'Unauthorized',
                },
              },
            },
          },
        },
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT',
            },
          },
        },
      };
    }
  });

  afterAll(async () => {
    await testDb.query('DELETE FROM users WHERE id = $1', [testUserId]);
  });

  describe('OpenAPI Schema Validation', () => {
    it('should have valid OpenAPI 3.0 schema', async () => {
      // Validate OpenAPI schema
      const validatedSchema = await SwaggerParser.validate(apiDocs);
      expect(validatedSchema).toBeDefined();
      expect(validatedSchema.openapi).toMatch(/^3\.\d+\.\d+$/);
    });

    it('should have required API information', () => {
      expect(apiDocs.info).toBeDefined();
      expect(apiDocs.info.title).toBeDefined();
      expect(apiDocs.info.version).toBeDefined();
      expect(apiDocs.servers).toBeDefined();
      expect(apiDocs.servers.length).toBeGreaterThan(0);
    });

    it('should have security schemes defined', () => {
      expect(apiDocs.components?.securitySchemes).toBeDefined();
      expect(apiDocs.components?.securitySchemes?.bearerAuth).toBeDefined();
      expect(apiDocs.components?.securitySchemes?.bearerAuth.type).toBe('http');
      expect(apiDocs.components?.securitySchemes?.bearerAuth.scheme).toBe('bearer');
    });
  });

  describe('Endpoint Documentation Coverage', () => {
    const requiredEndpoints = [
      { path: '/api/health', method: 'get' },
      { path: '/api/auth/login', method: 'post' },
      { path: '/api/auth/register', method: 'post' },
      { path: '/api/auth/logout', method: 'post' },
      { path: '/api/projects', method: 'get' },
      { path: '/api/projects', method: 'post' },
      { path: '/api/projects/{id}', method: 'get' },
      { path: '/api/projects/{id}', method: 'put' },
      { path: '/api/projects/{id}', method: 'delete' },
      { path: '/api/users/me', method: 'get' },
      { path: '/api/users/me', method: 'put' },
    ];

    requiredEndpoints.forEach(({ path, method }) => {
      it(`should document ${method.toUpperCase()} ${path}`, () => {
        // Check if path exists (handle path parameters)
        const pathExists = Object.keys(apiDocs.paths).some((p) => {
          const pathRegex = p.replace(/{[^}]+}/g, '{[^}]+}');
          return new RegExp(`^${pathRegex}$`).test(path);
        });

        expect(pathExists).toBe(true);

        // Find the actual path key
        const actualPath = Object.keys(apiDocs.paths).find((p) => {
          const pathRegex = p.replace(/{[^}]+}/g, '{[^}]+}');
          return new RegExp(`^${pathRegex}$`).test(path);
        });

        if (actualPath && apiDocs.paths[actualPath]) {
          expect(apiDocs.paths[actualPath][method]).toBeDefined();
          const endpoint = apiDocs.paths[actualPath][method];

          // Check required properties
          expect(endpoint.summary).toBeDefined();
          expect(endpoint.responses).toBeDefined();
          expect(Object.keys(endpoint.responses).length).toBeGreaterThan(0);

          // Check for authentication requirement
          if (path !== '/api/health' && !path.includes('auth')) {
            expect(endpoint.security).toBeDefined();
          }
        }
      });
    });
  });

  describe('Request/Response Schema Validation', () => {
    it('should validate login request schema', async () => {
      const loginSchema =
        apiDocs.paths['/api/auth/login']?.post?.requestBody?.content?.['application/json']?.schema;
      expect(loginSchema).toBeDefined();

      const validate = ajv.compile(loginSchema);

      // Valid request
      expect(
        validate({
          email: '<EMAIL>',
          password: 'Password123!',
        })
      ).toBe(true);

      // Invalid request - missing email
      expect(
        validate({
          password: 'Password123!',
        })
      ).toBe(false);

      // Invalid request - invalid email format
      expect(
        validate({
          email: 'not-an-email',
          password: 'Password123!',
        })
      ).toBe(false);
    });

    it('should validate project creation schema', async () => {
      const projectSchema =
        apiDocs.paths['/api/projects']?.post?.requestBody?.content?.['application/json']?.schema;
      expect(projectSchema).toBeDefined();

      const validate = ajv.compile(projectSchema);

      // Valid request
      expect(
        validate({
          name: 'Test Project',
          description: 'A test project description',
        })
      ).toBe(true);

      // Invalid request - missing name
      expect(
        validate({
          description: 'A test project description',
        })
      ).toBe(false);

      // Invalid request - name too long
      expect(
        validate({
          name: 'a'.repeat(256),
        })
      ).toBe(false);
    });

    it('should validate response schemas against actual responses', async () => {
      // Test health endpoint
      const healthResponse = await request(app).get('/api/health');
      const healthSchema =
        apiDocs.paths['/api/health']?.get?.responses?.['200']?.content?.['application/json']
          ?.schema;

      if (healthSchema) {
        const validate = ajv.compile(healthSchema);
        expect(validate(healthResponse.body)).toBe(true);
      }

      // Test projects endpoint
      const projectsResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      const projectsSchema =
        apiDocs.paths['/api/projects']?.get?.responses?.['200']?.content?.['application/json']
          ?.schema;

      if (projectsSchema) {
        const validate = ajv.compile(projectsSchema);
        expect(validate(projectsResponse.body)).toBe(true);
      }
    });
  });

  describe('API Examples and Try-it-out', () => {
    it('should provide valid examples in documentation', () => {
      // Check if examples are provided
      const loginEndpoint = apiDocs.paths['/api/auth/login']?.post;

      if (loginEndpoint?.requestBody?.content?.['application/json']?.examples) {
        const examples = loginEndpoint.requestBody.content['application/json'].examples;

        // Validate each example
        Object.entries(examples).forEach(([_name, example]: [string, any]) => {
          const schema = loginEndpoint.requestBody?.content?.['application/json']?.schema;
          if (schema) {
            const validate = ajv.compile(schema);
            expect(validate(example.value)).toBe(true);
          }
        });
      }
    });

    it('should have consistent error response format', () => {
      // Check all endpoints for error responses
      Object.entries(apiDocs.paths).forEach(([_path, methods]) => {
        Object.entries(methods).forEach(([_method, endpoint]: [string, any]) => {
          // Check 400, 401, 403, 404, 500 responses
          ['400', '401', '403', '404', '500'].forEach((status) => {
            if (endpoint.responses?.[status]) {
              const errorResponse = endpoint.responses[status];

              // Should have description
              expect(errorResponse.description).toBeDefined();

              // If content is defined, check schema
              if (errorResponse.content?.['application/json']?.schema) {
                const schema = errorResponse.content['application/json'].schema;
                expect(schema.properties?.error).toBeDefined();
              }
            }
          });
        });
      });
    });
  });

  describe('Authentication Documentation', () => {
    it('should document authentication flow correctly', () => {
      // Check login endpoint
      const loginEndpoint = apiDocs.paths['/api/auth/login']?.post;
      expect(loginEndpoint).toBeDefined();
      expect(loginEndpoint.security).toBeUndefined(); // Login shouldn't require auth

      // Check protected endpoints
      const protectedEndpoints = ['/api/projects', '/api/users/me'];

      protectedEndpoints.forEach((path) => {
        const endpoint = apiDocs.paths[path]?.get;
        expect(endpoint?.security).toBeDefined();
        expect(endpoint?.security?.[0]).toHaveProperty('bearerAuth');
      });
    });

    it('should document token refresh flow', () => {
      const refreshPath = '/api/auth/refresh';
      if (apiDocs.paths[refreshPath]) {
        const refreshEndpoint = apiDocs.paths[refreshPath].post;
        expect(refreshEndpoint).toBeDefined();
        expect(refreshEndpoint.requestBody).toBeDefined();
        expect(refreshEndpoint.responses['200']).toBeDefined();
      }
    });
  });

  describe('Pagination Documentation', () => {
    it('should document pagination parameters consistently', () => {
      const paginatedEndpoints = ['/api/projects', '/api/projects/{id}/images'];

      paginatedEndpoints.forEach((path) => {
        const actualPath = Object.keys(apiDocs.paths).find((p) => {
          const pathRegex = p.replace(/{[^}]+}/g, '{[^}]+}');
          return new RegExp(`^${pathRegex}$`).test(path);
        });

        if (actualPath && apiDocs.paths[actualPath]?.get) {
          const endpoint = apiDocs.paths[actualPath].get;
          const parameters = endpoint.parameters || [];

          // Should have page parameter
          const pageParam = parameters.find((p: any) => p.name === 'page');
          expect(pageParam).toBeDefined();
          expect(pageParam?.schema?.type).toBe('integer');
          expect(pageParam?.schema?.minimum).toBe(1);

          // Should have limit parameter
          const limitParam = parameters.find((p: any) => p.name === 'limit');
          expect(limitParam).toBeDefined();
          expect(limitParam?.schema?.type).toBe('integer');
          expect(limitParam?.schema?.minimum).toBeGreaterThan(0);
          expect(limitParam?.schema?.maximum).toBeLessThanOrEqual(100);
        }
      });
    });
  });

  describe('File Upload Documentation', () => {
    it('should document file upload endpoints correctly', () => {
      const _uploadPath = '/api/projects/{id}/images';
      const actualPath = Object.keys(apiDocs.paths).find((p) => p.includes('images'));

      if (actualPath && apiDocs.paths[actualPath]?.post) {
        const uploadEndpoint = apiDocs.paths[actualPath].post;

        // Should use multipart/form-data
        expect(uploadEndpoint.requestBody?.content?.['multipart/form-data']).toBeDefined();

        const schema = uploadEndpoint.requestBody?.content?.['multipart/form-data']?.schema;
        expect(schema?.type).toBe('object');

        // Should have file field
        const fileProperty = Object.entries(schema?.properties || {}).find(
          ([_key, value]: [string, any]) => value.type === 'string' && value.format === 'binary'
        );
        expect(fileProperty).toBeDefined();
      }
    });
  });

  describe('API Versioning Documentation', () => {
    it('should document API versioning strategy', () => {
      // Check if versioned paths exist
      const v1Paths = Object.keys(apiDocs.paths).filter((path) => path.includes('/v1/'));

      if (v1Paths.length > 0) {
        // If versioned, should have version in base path
        expect(
          apiDocs.servers.some(
            (server) => server.url.includes('/v1') || server.description?.includes('v1')
          )
        ).toBe(true);
      }

      // API version should be in info
      expect(apiDocs.info.version).toMatch(/^\d+\.\d+\.\d+$/);
    });
  });

  describe('Rate Limiting Documentation', () => {
    it('should document rate limiting headers', () => {
      // Check if rate limiting is documented
      Object.entries(apiDocs.paths).forEach(([_path, methods]) => {
        Object.entries(methods).forEach(([_method, endpoint]: [string, any]) => {
          if (endpoint.responses?.['429']) {
            const rateLimitResponse = endpoint.responses['429'];
            expect(rateLimitResponse.description).toContain('rate limit');

            // Should document rate limit headers
            if (rateLimitResponse.headers) {
              expect(rateLimitResponse.headers['X-RateLimit-Limit']).toBeDefined();
              expect(rateLimitResponse.headers['X-RateLimit-Remaining']).toBeDefined();
              expect(rateLimitResponse.headers['X-RateLimit-Reset']).toBeDefined();
            }
          }
        });
      });
    });
  });

  describe('WebSocket Documentation', () => {
    it('should document WebSocket endpoints if applicable', () => {
      // Check for WebSocket documentation
      if (apiDocs.paths['/ws'] || apiDocs.paths['/socket.io']) {
        // Should have WebSocket-specific documentation
        const wsPath = apiDocs.paths['/ws'] || apiDocs.paths['/socket.io'];
        expect(wsPath).toBeDefined();

        // Should document events
        if (wsPath.get?.description) {
          expect(wsPath.get.description).toContain('WebSocket');
          expect(wsPath.get.description).toContain('event');
        }
      }
    });
  });

  describe('Generated Client SDK Compatibility', () => {
    it('should have schemas suitable for client generation', () => {
      // Check that all request/response bodies have proper schemas
      Object.entries(apiDocs.paths).forEach(([_path, methods]) => {
        Object.entries(methods).forEach(([_method, endpoint]: [string, any]) => {
          // Check request body schemas
          if (endpoint.requestBody?.content) {
            Object.entries(endpoint.requestBody.content).forEach(
              ([_contentType, content]: [string, any]) => {
                expect(content.schema).toBeDefined();

                // Schema should not use undefined types
                const schemaString = JSON.stringify(content.schema);
                expect(schemaString).not.toContain('"type":"undefined"');
              }
            );
          }

          // Check response schemas
          Object.entries(endpoint.responses || {}).forEach(([_status, response]: [string, any]) => {
            if (response.content) {
              Object.entries(response.content).forEach(([_contentType, content]: [string, any]) => {
                expect(content.schema).toBeDefined();
              });
            }
          });
        });
      });
    });

    it('should use consistent naming conventions', () => {
      // Check for consistent property naming (camelCase vs snake_case)
      const propertyNames: string[] = [];

      const extractPropertyNames = (schema: any) => {
        if (schema.properties) {
          propertyNames.push(...Object.keys(schema.properties));
          Object.values(schema.properties).forEach((prop: any) => {
            if (prop.type === 'object') {
              extractPropertyNames(prop);
            }
          });
        }
      };

      // Extract all property names from schemas
      Object.values(apiDocs.paths).forEach((methods) => {
        Object.values(methods).forEach((endpoint: any) => {
          if (endpoint.requestBody?.content) {
            Object.values(endpoint.requestBody.content).forEach((content: any) => {
              extractPropertyNames(content.schema);
            });
          }

          if (endpoint.responses) {
            Object.values(endpoint.responses).forEach((response: any) => {
              if (response.content) {
                Object.values(response.content).forEach((content: any) => {
                  extractPropertyNames(content.schema);
                });
              }
            });
          }
        });
      });

      // Check consistency
      const camelCaseProps = propertyNames.filter((name) => /^[a-z][a-zA-Z]*$/.test(name));
      const snakeCaseProps = propertyNames.filter((name) => /_/.test(name));

      // Should use one convention consistently (allow some mixing for backwards compatibility)
      const camelCaseRatio = camelCaseProps.length / propertyNames.length;
      const snakeCaseRatio = snakeCaseProps.length / propertyNames.length;

      expect(Math.max(camelCaseRatio, snakeCaseRatio)).toBeGreaterThan(0.7);
    });
  });
});
