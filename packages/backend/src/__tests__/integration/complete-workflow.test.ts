/**
 * Complete User Workflow Integration Tests
 * Tests the entire user journey from registration to segmentation
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import path from 'path';
import fs from 'fs/promises';

describe('Complete User Workflow Integration', () => {
  let app: Express;
  let testUserEmail: string;
  let accessToken: string;
  let refreshToken: string;
  let projectId: number;
  let imageId: number;
  let testImagePath: string;

  beforeAll(async () => {
    app = await createApp();

    // Clean up test data
    await testDb.cleanupUsers('%@workflow-test.com');

    // Create test image
    testImagePath = path.join(__dirname, 'test-workflow-image.png');
    const imageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    await fs.writeFile(testImagePath, imageBuffer);
  });

  afterAll(async () => {
    // Clean up
    if (testImagePath) {
      await fs.unlink(testImagePath).catch(() => {});
    }
    await testDb.cleanupUsers('%@workflow-test.com');
  });

  describe('1. User Registration and Onboarding', () => {
    it('should complete full registration flow', async () => {
      testUserEmail = `workflow-${Date.now()}@workflow-test.com`;

      // Step 1: Check email availability
      const checkEmailResponse = await request(app)
        .get('/api/auth/check-email')
        .query({ email: testUserEmail });

      expect(checkEmailResponse.status).toBe(200);
      expect(checkEmailResponse.body.exists).toBe(false);

      // Step 2: Register new user
      const registerResponse = await request(app).post('/api/auth/register').send({
        email: testUserEmail,
        password: 'WorkflowTest123!',
        name: 'Workflow Test User',
      });

      expect(registerResponse.status).toBe(201);
      expect(registerResponse.body).toHaveProperty('accessToken');
      expect(registerResponse.body).toHaveProperty('refreshToken');
      expect(registerResponse.body.user).toHaveProperty('email', testUserEmail);

      accessToken = registerResponse.body.accessToken;
      refreshToken = registerResponse.body.refreshToken;

      // Step 3: Get user profile
      const profileResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(profileResponse.status).toBe(200);
      expect(profileResponse.body).toHaveProperty('email', testUserEmail);

      // Step 4: Update profile information
      const updateProfileResponse = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          bio: 'I am testing the complete workflow',
          location: 'Test Lab',
          website: 'https://workflow-test.example.com',
        });

      expect(updateProfileResponse.status).toBe(200);
      expect(updateProfileResponse.body).toHaveProperty(
        'bio',
        'I am testing the complete workflow'
      );
    });
  });

  describe('2. Project Creation and Management', () => {
    it('should create and manage projects', async () => {
      // Step 1: Create a new project
      const createProjectResponse = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'Workflow Test Project',
          description: 'Testing the complete user workflow',
        });

      expect(createProjectResponse.status).toBe(201);
      expect(createProjectResponse.body.project).toHaveProperty('name', 'Workflow Test Project');
      projectId = createProjectResponse.body.project.id;

      // Step 2: List user projects
      const listProjectsResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(listProjectsResponse.status).toBe(200);
      expect(listProjectsResponse.body.projects).toHaveLength(1);
      expect(listProjectsResponse.body.projects[0]).toHaveProperty('id', projectId);

      // Step 3: Get project details
      const projectDetailsResponse = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(projectDetailsResponse.status).toBe(200);
      expect(projectDetailsResponse.body).toHaveProperty('id', projectId);
      expect(projectDetailsResponse.body).toHaveProperty('name', 'Workflow Test Project');

      // Step 4: Update project
      const updateProjectResponse = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'Updated Workflow Project',
          description: 'Updated description for workflow test',
        });

      expect(updateProjectResponse.status).toBe(200);
      expect(updateProjectResponse.body).toHaveProperty('name', 'Updated Workflow Project');
    });
  });

  describe('3. Image Upload and Processing', () => {
    it('should upload and process images', async () => {
      // Step 1: Upload image to project
      const uploadResponse = await request(app)
        .post(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('images', testImagePath, 'workflow-test.png');

      expect(uploadResponse.status).toBe(201);
      expect(uploadResponse.body.images).toHaveLength(1);
      expect(uploadResponse.body.images[0]).toHaveProperty('filename');
      imageId = uploadResponse.body.images[0].id;

      // Step 2: List project images
      const listImagesResponse = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(listImagesResponse.status).toBe(200);
      expect(listImagesResponse.body.images).toHaveLength(1);
      expect(listImagesResponse.body.images[0]).toHaveProperty('id', imageId);

      // Step 3: Get image details
      const imageDetailsResponse = await request(app)
        .get(`/api/images/${imageId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(imageDetailsResponse.status).toBe(200);
      expect(imageDetailsResponse.body).toHaveProperty('id', imageId);
      expect(imageDetailsResponse.body).toHaveProperty('project_id', projectId);
    });
  });

  describe('4. Segmentation Workflow', () => {
    it('should trigger and monitor segmentation', async () => {
      // Step 1: Trigger segmentation
      const segmentationResponse = await request(app)
        .post(`/api/images/${imageId}/segment`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
          parameters: {
            threshold: 0.5,
            min_size: 10,
          },
        });

      expect(segmentationResponse.status).toBe(202); // Accepted
      expect(segmentationResponse.body).toHaveProperty('message');
      expect(segmentationResponse.body).toHaveProperty('job_id');

      const jobId = segmentationResponse.body.job_id;

      // Step 2: Check segmentation status
      const statusResponse = await request(app)
        .get(`/api/segmentation/jobs/${jobId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body).toHaveProperty('status');
      expect(['pending', 'processing', 'completed', 'failed']).toContain(
        statusResponse.body.status
      );

      // Step 3: Get segmentation results (if completed)
      if (statusResponse.body.status === 'completed') {
        const resultsResponse = await request(app)
          .get(`/api/images/${imageId}/segmentation`)
          .set('Authorization', `Bearer ${accessToken}`);

        expect(resultsResponse.status).toBe(200);
        expect(resultsResponse.body).toHaveProperty('polygons');
        expect(resultsResponse.body).toHaveProperty('metadata');
      }
    });
  });

  describe('5. Export and Sharing', () => {
    it('should export project data', async () => {
      // Step 1: Export project as COCO format
      const exportResponse = await request(app)
        .post(`/api/projects/${projectId}/export`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          format: 'coco',
          include_images: true,
          include_segmentations: true,
        });

      expect(exportResponse.status).toBe(202); // Accepted
      expect(exportResponse.body).toHaveProperty('export_id');

      // Step 2: Check export status
      const exportStatusResponse = await request(app)
        .get(`/api/projects/${projectId}/exports/${exportResponse.body.export_id}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(exportStatusResponse.status).toBe(200);
      expect(exportStatusResponse.body).toHaveProperty('status');
      expect(['pending', 'processing', 'completed', 'failed']).toContain(
        exportStatusResponse.body.status
      );
    });

    it('should share project with another user', async () => {
      // Step 1: Create share link
      const shareLinkResponse = await request(app)
        .post(`/api/projects/${projectId}/share`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          permission: 'view',
          expires_in: '7d',
        });

      expect(shareLinkResponse.status).toBe(201);
      expect(shareLinkResponse.body).toHaveProperty('share_link');
      expect(shareLinkResponse.body).toHaveProperty('expires_at');

      // Step 2: Share with specific user
      const shareUserResponse = await request(app)
        .post(`/api/projects/${projectId}/collaborators`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          email: '<EMAIL>',
          permission: 'edit',
        });

      expect([201, 404]).toContain(shareUserResponse.status); // 404 if user doesn't exist
    });
  });

  describe('6. User Settings and Preferences', () => {
    it('should manage user settings', async () => {
      // Step 1: Update theme settings
      const themeResponse = await request(app)
        .put('/api/user-profile/settings/theme')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          theme: 'dark',
          accentColor: '#4F46E5',
        });

      expect(themeResponse.status).toBe(200);
      expect(themeResponse.body).toHaveProperty('theme', 'dark');

      // Step 2: Update language settings
      const languageResponse = await request(app)
        .put('/api/user-profile/settings/language')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          language: 'en',
          dateFormat: 'MM/DD/YYYY',
          timeFormat: '12h',
        });

      expect(languageResponse.status).toBe(200);
      expect(languageResponse.body).toHaveProperty('language', 'en');

      // Step 3: Update notification preferences
      const notificationResponse = await request(app)
        .put('/api/users/me/settings/notifications')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          email: {
            projectShared: true,
            segmentationComplete: true,
            weeklyReport: false,
          },
        });

      expect(notificationResponse.status).toBe(200);
      expect(notificationResponse.body).toHaveProperty('email');
    });
  });

  describe('7. Statistics and Analytics', () => {
    it('should retrieve user statistics', async () => {
      // Step 1: Get user statistics
      const statsResponse = await request(app)
        .get('/api/users/me/statistics')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(statsResponse.status).toBe(200);
      expect(statsResponse.body).toHaveProperty('total_projects');
      expect(statsResponse.body).toHaveProperty('total_images');
      expect(statsResponse.body).toHaveProperty('total_segmentations');
      expect(statsResponse.body).toHaveProperty('storage_used_mb');

      // Step 2: Get project statistics
      const projectStatsResponse = await request(app)
        .get(`/api/projects/${projectId}/statistics`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(projectStatsResponse.status).toBe(200);
      expect(projectStatsResponse.body).toHaveProperty('total_images');
      expect(projectStatsResponse.body).toHaveProperty('segmented_images');
      expect(projectStatsResponse.body).toHaveProperty('total_polygons');
    });
  });

  describe('8. Clean Up and Logout', () => {
    it('should clean up and logout properly', async () => {
      // Step 1: Delete image
      const deleteImageResponse = await request(app)
        .delete(`/api/images/${imageId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(deleteImageResponse.status).toBe(200);

      // Step 2: Delete project
      const deleteProjectResponse = await request(app)
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(deleteProjectResponse.status).toBe(200);

      // Step 3: Logout
      const logoutResponse = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ refreshToken });

      expect(logoutResponse.status).toBe(200);
      expect(logoutResponse.body).toHaveProperty('message');

      // Step 4: Verify token is invalidated
      const invalidTokenResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(invalidTokenResponse.status).toBe(401);
    });
  });
});
