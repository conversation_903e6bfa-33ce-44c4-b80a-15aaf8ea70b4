/**
 * Monitoring and Health Check Tests
 * Tests for system monitoring, health checks, metrics collection, and alerting
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import Redis from 'ioredis';
import axios from 'axios';

// Mock external services
jest.mock('ioredis');
jest.mock('axios');

const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Monitoring and Health Check Tests', () => {
  let app: Express;
  let accessToken: string;
  let adminToken: string;
  let testUserId: number;
  let redis: jest.Mocked<Redis>;

  beforeAll(async () => {
    app = await createApp();

    // Create test users
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'Monitor Test User']
    );
    testUserId = userResult.rows[0].id;

    const user = await testDb.createTestUser('<EMAIL>', 'hashed', 'Admin Monitor User', 'admin'.shift(), '<EMAIL>', 'hashed', 'Admin Monitor User', 'admin'.shift(), { name: '<EMAIL>', 'hashed', 'Admin Monitor User', 'admin'.shift() });
    const adminUserId = user.id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    adminToken = jwt.sign(
      { userId: adminUserId, email: '<EMAIL>', role: 'admin' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Setup Redis mock
    redis = new Redis() as jest.Mocked<Redis>;
  });

  afterAll(async () => {
    await testDb.cleanupUsers('%<EMAIL>');
  });

  describe('Basic Health Checks', () => {
    it('should return basic health status', async () => {
      const response = await request(app).get('/api/health');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body.status).toMatch(/ok|healthy/);
    });

    it('should return detailed health status with query parameter', async () => {
      // Mock Redis ping
      redis.ping.mockResolvedValueOnce('PONG');

      const response = await request(app).get('/api/health?details=true');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('services');

      // Check individual service health
      if (response.body.services) {
        expect(response.body.services).toHaveProperty('database');
        expect(response.body.services).toHaveProperty('redis');
        expect(response.body.services.database).toHaveProperty('status');
        expect(response.body.services.redis).toHaveProperty('status');
      }
    });

    it('should return readiness status', async () => {
      const response = await request(app).get('/api/ready');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('ready');
      expect(response.body).toHaveProperty('checks');
    });

    it('should return liveness status', async () => {
      const response = await request(app).get('/api/live');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('alive', true);
    });
  });

  describe('Service Health Checks', () => {
    it('should check database health', async () => {
      const response = await request(app)
        .get('/api/health/database')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('latency');
      expect(response.body).toHaveProperty('connections');
      expect(response.body.latency).toBeLessThan(100); // DB query should be fast
    });

    it('should check Redis health', async () => {
      redis.ping.mockResolvedValueOnce('PONG');
      redis.info.mockResolvedValueOnce(`
# Server
redis_version:7.0.0
uptime_in_seconds:86400
# Memory
used_memory:1048576
used_memory_human:1M
      `);

      const response = await request(app)
        .get('/api/health/redis')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('latency');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('version');
    });

    it('should check ML service health', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          status: 'healthy',
          models_loaded: 2,
          gpu_available: true,
        },
      });

      const response = await request(app)
        .get('/api/health/ml-service')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('models_loaded');
      expect(response.body).toHaveProperty('gpu_available');
    });

    it('should handle service unavailability gracefully', async () => {
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection refused'));

      const response = await request(app)
        .get('/api/health/ml-service')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200); // Still returns 200 but with unhealthy status
      expect(response.body).toHaveProperty('status', 'unhealthy');
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Metrics Collection', () => {
    it('should collect system metrics', async () => {
      const response = await request(app)
        .get('/api/metrics/system')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('cpu');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('disk');
      expect(response.body).toHaveProperty('uptime');

      // Validate metric values
      expect(response.body.cpu).toHaveProperty('usage');
      expect(response.body.cpu.usage).toBeGreaterThanOrEqual(0);
      expect(response.body.cpu.usage).toBeLessThanOrEqual(100);

      expect(response.body.memory).toHaveProperty('total');
      expect(response.body.memory).toHaveProperty('used');
      expect(response.body.memory).toHaveProperty('percentage');
    });

    it('should collect application metrics', async () => {
      const response = await request(app)
        .get('/api/metrics/application')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('requests');
      expect(response.body).toHaveProperty('errors');
      expect(response.body).toHaveProperty('latency');
      expect(response.body).toHaveProperty('active_users');
      expect(response.body).toHaveProperty('queue_size');
    });

    it('should provide Prometheus-compatible metrics', async () => {
      const response = await request(app)
        .get('/api/metrics')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/plain');

      // Check for Prometheus format
      expect(response.text).toContain('# HELP');
      expect(response.text).toContain('# TYPE');
      expect(response.text).toMatch(/http_requests_total\{[^}]*\}\s+\d+/);
    });

    it('should track request metrics', async () => {
      // Make several requests
      await request(app).get('/api/health');
      await request(app).get('/api/health');
      await request(app).get('/api/nonexistent').expect(404);

      const response = await request(app)
        .get('/api/metrics/requests')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('total_requests');
      expect(response.body).toHaveProperty('requests_by_endpoint');
      expect(response.body).toHaveProperty('requests_by_status');
      expect(response.body).toHaveProperty('error_rate');

      expect(response.body.total_requests).toBeGreaterThan(0);
      expect(response.body.requests_by_status['200']).toBeGreaterThan(0);
      expect(response.body.requests_by_status['404']).toBeGreaterThan(0);
    });
  });

  describe('Performance Monitoring', () => {
    it('should track endpoint latencies', async () => {
      // Make requests to generate latency data
      await request(app).get('/api/health');
      await request(app).get('/api/projects').set('Authorization', `Bearer ${accessToken}`);

      const response = await request(app)
        .get('/api/metrics/latency')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('endpoints');
      expect(response.body.endpoints).toHaveProperty('/api/health');
      expect(response.body.endpoints['/api/health']).toHaveProperty('avg');
      expect(response.body.endpoints['/api/health']).toHaveProperty('p50');
      expect(response.body.endpoints['/api/health']).toHaveProperty('p95');
      expect(response.body.endpoints['/api/health']).toHaveProperty('p99');
    });

    it('should track database query performance', async () => {
      const response = await request(app)
        .get('/api/metrics/database-performance')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('slow_queries');
      expect(response.body).toHaveProperty('average_query_time');
      expect(response.body).toHaveProperty('queries_per_second');
      expect(response.body).toHaveProperty('connection_pool');
    });

    it('should identify performance bottlenecks', async () => {
      const response = await request(app)
        .get('/api/metrics/bottlenecks')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('slowest_endpoints');
      expect(response.body).toHaveProperty('memory_intensive_operations');
      expect(response.body).toHaveProperty('recommendations');

      expect(Array.isArray(response.body.slowest_endpoints)).toBe(true);
      expect(Array.isArray(response.body.recommendations)).toBe(true);
    });
  });

  describe('Error Tracking', () => {
    it('should track application errors', async () => {
      // Generate some errors
      await request(app).get('/api/nonexistent').expect(404);
      await request(app).post('/api/auth/login').send({ email: 'invalid' }).expect(400);

      const response = await request(app)
        .get('/api/metrics/errors')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('total_errors');
      expect(response.body).toHaveProperty('errors_by_type');
      expect(response.body).toHaveProperty('recent_errors');
      expect(response.body).toHaveProperty('error_rate');

      expect(response.body.total_errors).toBeGreaterThan(0);
      expect(response.body.errors_by_type).toHaveProperty('404');
      expect(response.body.errors_by_type).toHaveProperty('400');
    });

    it('should provide error details for debugging', async () => {
      const response = await request(app)
        .get('/api/metrics/errors/recent?limit=10')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('errors');
      expect(Array.isArray(response.body.errors)).toBe(true);

      if (response.body.errors.length > 0) {
        const error = response.body.errors[0];
        expect(error).toHaveProperty('timestamp');
        expect(error).toHaveProperty('status_code');
        expect(error).toHaveProperty('endpoint');
        expect(error).toHaveProperty('method');
      }
    });
  });

  describe('Resource Monitoring', () => {
    it('should monitor memory usage', async () => {
      const response = await request(app)
        .get('/api/metrics/memory')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('heap_used');
      expect(response.body).toHaveProperty('heap_total');
      expect(response.body).toHaveProperty('external');
      expect(response.body).toHaveProperty('rss');
      expect(response.body).toHaveProperty('gc_stats');
    });

    it('should monitor file system usage', async () => {
      const response = await request(app)
        .get('/api/metrics/disk')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('uploads_directory');
      expect(response.body).toHaveProperty('temp_directory');
      expect(response.body).toHaveProperty('database_size');

      expect(response.body.uploads_directory).toHaveProperty('total');
      expect(response.body.uploads_directory).toHaveProperty('used');
      expect(response.body.uploads_directory).toHaveProperty('available');
    });

    it('should monitor network connections', async () => {
      const response = await request(app)
        .get('/api/metrics/network')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('active_connections');
      expect(response.body).toHaveProperty('connection_rate');
      expect(response.body).toHaveProperty('bandwidth');
    });
  });

  describe('Custom Metrics', () => {
    it('should track business metrics', async () => {
      const response = await request(app)
        .get('/api/metrics/business')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('active_users');
      expect(response.body).toHaveProperty('projects_created');
      expect(response.body).toHaveProperty('images_processed');
      expect(response.body).toHaveProperty('segmentations_completed');
      expect(response.body).toHaveProperty('average_processing_time');
    });

    it('should track feature usage', async () => {
      const response = await request(app)
        .get('/api/metrics/features')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('feature_usage');
      expect(response.body.feature_usage).toHaveProperty('image_upload');
      expect(response.body.feature_usage).toHaveProperty('segmentation');
      expect(response.body.feature_usage).toHaveProperty('export');
      expect(response.body.feature_usage).toHaveProperty('sharing');
    });
  });

  describe('Alerting System', () => {
    it('should check alert thresholds', async () => {
      const response = await request(app)
        .get('/api/monitoring/alerts/status')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('active_alerts');
      expect(response.body).toHaveProperty('thresholds');
      expect(response.body).toHaveProperty('last_check');
    });

    it('should trigger alerts for high error rates', async () => {
      // Simulate high error rate
      const mockMetrics = {
        error_rate: 0.15, // 15% error rate
        threshold: 0.05, // 5% threshold
      };

      const response = await request(app)
        .post('/api/monitoring/alerts/check')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ metrics: mockMetrics });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('alerts_triggered');
      expect(response.body.alerts_triggered).toContain('high_error_rate');
    });

    it('should manage alert configurations', async () => {
      const alertConfig = {
        name: 'high_memory_usage',
        threshold: 90,
        duration: 300, // 5 minutes
        severity: 'warning',
        notification_channels: ['email', 'slack'],
      };

      const createResponse = await request(app)
        .post('/api/monitoring/alerts/config')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(alertConfig);

      expect(createResponse.status).toBe(201);
      expect(createResponse.body).toHaveProperty('id');
      expect(createResponse.body).toHaveProperty('name', 'high_memory_usage');

      // Get alert config
      const getResponse = await request(app)
        .get(`/api/monitoring/alerts/config/${createResponse.body.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(getResponse.status).toBe(200);
      expect(getResponse.body).toHaveProperty('threshold', 90);
    });
  });

  describe('Health History', () => {
    it('should track health check history', async () => {
      // Make several health checks
      await request(app).get('/api/health');
      await request(app).get('/api/health');
      await request(app).get('/api/health');

      const response = await request(app)
        .get('/api/monitoring/health-history?period=1h')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('checks');
      expect(Array.isArray(response.body.checks)).toBe(true);
      expect(response.body).toHaveProperty('uptime_percentage');
      expect(response.body).toHaveProperty('incidents');
    });

    it('should calculate uptime statistics', async () => {
      const response = await request(app)
        .get('/api/monitoring/uptime?period=30d')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('uptime_percentage');
      expect(response.body).toHaveProperty('total_downtime_minutes');
      expect(response.body).toHaveProperty('incidents_count');
      expect(response.body).toHaveProperty('mtbf'); // Mean time between failures
      expect(response.body).toHaveProperty('mttr'); // Mean time to recovery
    });
  });

  describe('Export and Reporting', () => {
    it('should export metrics in CSV format', async () => {
      const response = await request(app)
        .get('/api/metrics/export?format=csv&period=24h')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('text/csv');
      expect(response.headers['content-disposition']).toContain('metrics-export');

      // Check CSV structure
      const lines = response.text.split('\n');
      expect(lines[0]).toContain('timestamp');
      expect(lines[0]).toContain('metric');
      expect(lines[0]).toContain('value');
    });

    it('should generate health report', async () => {
      const response = await request(app)
        .get('/api/monitoring/report?period=7d')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('summary');
      expect(response.body).toHaveProperty('health_score');
      expect(response.body).toHaveProperty('recommendations');
      expect(response.body).toHaveProperty('trends');

      expect(response.body.health_score).toBeGreaterThanOrEqual(0);
      expect(response.body.health_score).toBeLessThanOrEqual(100);
    });
  });

  describe('Integration with External Monitoring', () => {
    it('should provide data for external monitoring tools', async () => {
      const response = await request(app)
        .get('/api/monitoring/integrations/datadog')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('metrics');
      expect(response.body).toHaveProperty('tags');
      expect(response.body).toHaveProperty('service_checks');
    });

    it('should support webhook notifications', async () => {
      const webhookConfig = {
        url: 'https://hooks.slack.com/services/test',
        events: ['high_error_rate', 'service_down'],
        secret: 'webhook-secret',
      };

      const response = await request(app)
        .post('/api/monitoring/webhooks')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(webhookConfig);

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('url');
      expect(response.body).toHaveProperty('events');
    });
  });
});
