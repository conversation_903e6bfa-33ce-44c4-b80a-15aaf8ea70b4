/**
 * Comprehensive Error Handling and Edge Case Tests
 * Tests various error scenarios and edge cases throughout the application
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import path from 'path';
import fs from 'fs/promises';

describe('Error Handling and Edge Cases', () => {
  let app: Express;
  let validToken: string;
  let expiredToken: string;
  let malformedToken: string;
  let testUserId: number;

  beforeAll(async () => {
    app = await createApp();

    // Create a test user for authenticated tests
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed-password', 'Error Test User']
    );
    testUserId = userResult.rows[0].id;

    // Generate various tokens for testing
    validToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    expiredToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '-1h' } // Already expired
    );

    malformedToken = 'this.is.not.a.valid.jwt.token';
  });

  afterAll(async () => {
    await testDb.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
  });

  describe('Authentication Error Handling', () => {
    it('should handle missing authentication token', async () => {
      const response = await request(app).get('/api/users/me');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('token');
    });

    it('should handle expired authentication token', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${expiredToken}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('expired');
    });

    it('should handle malformed authentication token', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${malformedToken}`);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should handle invalid authentication scheme', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Basic ${validToken}`); // Wrong scheme

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Input Validation Errors', () => {
    it('should handle missing required fields in registration', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        // Missing password
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('required');
    });

    it('should handle invalid email format', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: 'not-an-email',
        password: 'ValidPassword123!',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('email');
    });

    it('should handle weak password', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: '123', // Too weak
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('password');
    });

    it('should handle excessively long input', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: 'a'.repeat(1000), // Too long
          description: 'b'.repeat(10000), // Way too long
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should handle SQL injection attempts', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: "admin' OR '1'='1",
        password: "password' OR '1'='1",
      });

      expect(response.status).toBe(401); // Should fail authentication, not cause SQL error
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should handle XSS attempts in user input', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: '<script>alert("XSS")</script>',
          description: '<img src=x onerror=alert("XSS")>',
        });

      // Should accept but sanitize the input
      expect(response.status).toBe(201);
      expect(response.body.project.name).not.toContain('<script>');
      expect(response.body.project.description).not.toContain('onerror');
    });
  });

  describe('Resource Not Found Errors', () => {
    it('should handle non-existent user', async () => {
      const fakeToken = jwt.sign(
        { userId: 999999, email: '<EMAIL>' },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${fakeToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not found');
    });

    it('should handle non-existent project', async () => {
      const response = await request(app)
        .get('/api/projects/999999')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('not found');
    });

    it('should handle non-existent image', async () => {
      const response = await request(app)
        .get('/api/images/999999')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });

    it('should handle non-existent API endpoint', async () => {
      const response = await request(app)
        .get('/api/this-endpoint-does-not-exist')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
    });
  });

  describe('Permission and Authorization Errors', () => {
    it('should prevent access to other users projects', async () => {
      // Create another user and project
      const user = await testDb.createTestUser('<EMAIL>', 'hashed', 'Other User'.shift(), '<EMAIL>', 'hashed', 'Other User'.shift(), { name: '<EMAIL>', 'hashed', 'Other User'.shift() });
    const otherUserId = user.id;

      const projectResult = await testDb.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['Other User Project', otherUserId]
      );
      const projectId = projectResult.rows[0].id;

      // Try to access with different user's token
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('permission');

      // Clean up
      await testDb.query('DELETE FROM projects WHERE id = $1', [projectId]);
      await testDb.query('DELETE FROM users WHERE id = $1', [otherUserId]);
    });

    it('should prevent modification without proper permissions', async () => {
      // Create a project
      const projectResult = await testDb.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['Test Project', testUserId]
      );
      const projectId = projectResult.rows[0].id;

      // Create a read-only token (simulating limited permissions)
      const readOnlyToken = jwt.sign(
        { userId: testUserId, email: '<EMAIL>', permissions: ['read'] },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '1h' }
      );

      // Try to update with read-only token
      const response = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${readOnlyToken}`)
        .send({ name: 'Updated Name' });

      // Depending on implementation, might be 403 or allow (if permissions not checked)
      expect([200, 403]).toContain(response.status);

      // Clean up
      await testDb.query('DELETE FROM projects WHERE id = $1', [projectId]);
    });
  });

  describe('File Upload Errors', () => {
    it('should handle missing file in upload', async () => {
      const response = await request(app)
        .post('/api/projects/1/images')
        .set('Authorization', `Bearer ${validToken}`)
        .field('description', 'No file attached');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('file');
    });

    it('should handle invalid file type', async () => {
      const textFilePath = path.join(__dirname, 'test.txt');
      await fs.writeFile(textFilePath, 'This is not an image');

      const response = await request(app)
        .post('/api/projects/1/images')
        .set('Authorization', `Bearer ${validToken}`)
        .attach('images', textFilePath, 'test.txt');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('file type');

      await fs.unlink(textFilePath).catch(() => {});
    });

    it('should handle oversized file', async () => {
      // Create a large buffer (simulating large file)
      const largeBuffer = Buffer.alloc(100 * 1024 * 1024); // 100MB
      const largePath = path.join(__dirname, 'large.png');
      await fs.writeFile(largePath, largeBuffer);

      const response = await request(app)
        .post('/api/projects/1/images')
        .set('Authorization', `Bearer ${validToken}`)
        .attach('images', largePath, 'large.png');

      expect(response.status).toBe(413); // Payload too large
      expect(response.body).toHaveProperty('error');

      await fs.unlink(largePath).catch(() => {});
    });
  });

  describe('Database and Connection Errors', () => {
    it('should handle database connection timeout gracefully', async () => {
      // This test would require mocking the database connection
      // For now, we'll test that the health check handles DB issues
      const response = await request(app).get('/api/health');

      // Should return status even if some components are unhealthy
      expect([200, 503]).toContain(response.status);
      expect(response.body).toHaveProperty('status');
    });

    it('should handle concurrent request limits', async () => {
      // Send many requests simultaneously
      const promises = Array(50)
        .fill(null)
        .map(() => request(app).get('/api/projects').set('Authorization', `Bearer ${validToken}`));

      const responses = await Promise.all(promises);
      const statusCodes = responses.map((r) => r.status);

      // Some requests might be rate limited
      expect(statusCodes).toContain(200);
      // Rate limiting might kick in
      const rateLimited = statusCodes.filter((s) => s === 429);
      expect(rateLimited.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Business Logic Errors', () => {
    it('should prevent duplicate email registration', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>', // Already exists
        password: 'ValidPassword123!',
      });

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('exists');
    });

    it('should handle project name conflicts', async () => {
      // Create first project
      const _project1 = await testDb.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['Duplicate Project Name', testUserId]
      );

      // Try to create another with same name
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: 'Duplicate Project Name',
        });

      // Depending on implementation, might allow duplicates or reject
      expect([201, 409]).toContain(response.status);

      // Clean up
      await testDb.query('DELETE FROM projects WHERE user_id = $1', [testUserId]);
    });

    it('should handle invalid state transitions', async () => {
      // Try to complete a segmentation that hasn't started
      const response = await request(app)
        .put('/api/segmentation/jobs/fake-job-id/complete')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          result: 'completed',
        });

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Edge Cases and Boundary Conditions', () => {
    it('should handle empty result sets gracefully', async () => {
      // List projects for user with no projects
      const user = await testDb.createTestUser('<EMAIL>', 'hashed', 'Empty User'.shift(), '<EMAIL>', 'hashed', 'Empty User'.shift(), { name: '<EMAIL>', 'hashed', 'Empty User'.shift() });
    const emptyUserId = user.id;

      const emptyToken = jwt.sign(
        { userId: emptyUserId, email: '<EMAIL>' },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${emptyToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');
      expect(response.body.projects).toHaveLength(0);

      // Clean up
      await testDb.query('DELETE FROM users WHERE id = $1', [emptyUserId]);
    });

    it('should handle special characters in input', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: 'Project with émojis 🚀 and spëcial çharacters!',
          description: 'Testing unicode: 你好世界 🌍',
        });

      expect(response.status).toBe(201);
      expect(response.body.project).toHaveProperty('name');
      expect(response.body.project.name).toContain('🚀');
    });

    it('should handle timezone differences correctly', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: 'Timezone Test Project',
          scheduled_at: new Date().toISOString(),
        });

      expect(response.status).toBe(201);
      expect(response.body.project).toHaveProperty('created_at');
      // Check that date is properly formatted
      expect(new Date(response.body.project.created_at)).toBeInstanceOf(Date);
    });
  });
});
