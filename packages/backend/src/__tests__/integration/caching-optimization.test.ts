/**
 * Caching and Optimization Tests
 * Tests for caching strategies, performance optimizations, and resource management
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import Redis from 'ioredis';
import crypto from 'crypto';

// Mock Redis
jest.mock('ioredis');

describe('Caching and Optimization Tests', () => {
  let app: Express;
  let accessToken: string;
  let testUserId: number;
  let projectId: number;
  let redis: jest.Mocked<Redis>;

  beforeAll(async () => {
    app = await createApp();

    // Create test user
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'Cache Test User']
    );
    testUserId = userResult.rows[0].id;

    // Create test project
    const projectResult = await testDb.query(
      'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
      ['Cache Test Project', testUserId]
    );
    projectId = projectResult.rows[0].id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Setup Redis mock
    redis = new Redis() as jest.Mocked<Redis>;
  });

  afterAll(async () => {
    await testDb.query('DELETE FROM projects WHERE user_id = $1', [testUserId]);
    await testDb.query('DELETE FROM users WHERE id = $1', [testUserId]);
  });

  describe('Response Caching', () => {
    it('should cache frequently accessed endpoints', async () => {
      const cacheKey = `projects:user:${testUserId}`;

      // First request - should hit database
      redis.get.mockResolvedValueOnce(null);
      redis.set.mockResolvedValueOnce('OK');

      const response1 = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response1.status).toBe(200);
      expect(response1.headers['x-cache']).toBe('miss');
      expect(redis.get).toHaveBeenCalledWith(cacheKey);
      expect(redis.set).toHaveBeenCalled();

      // Second request - should hit cache
      redis.get.mockResolvedValueOnce(JSON.stringify(response1.body));

      const response2 = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response2.status).toBe(200);
      expect(response2.headers['x-cache']).toBe('hit');
      expect(response2.body).toEqual(response1.body);
    });

    it('should implement cache invalidation on updates', async () => {
      const cacheKey = `projects:user:${testUserId}`;

      // Cache is populated
      redis.get.mockResolvedValueOnce(JSON.stringify({ projects: [] }));

      // Update project
      redis.del.mockResolvedValueOnce(1);

      const updateResponse = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({ name: 'Updated Project Name' });

      expect(updateResponse.status).toBe(200);
      expect(redis.del).toHaveBeenCalledWith(cacheKey);
    });

    it('should implement cache TTL', async () => {
      redis.get.mockResolvedValueOnce(null);
      redis.setex.mockResolvedValueOnce('OK');

      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(redis.setex).toHaveBeenCalledWith(
        expect.any(String),
        300, // 5 minutes TTL
        expect.any(String)
      );
    });

    it('should handle cache errors gracefully', async () => {
      // Redis error
      redis.get.mockRejectedValueOnce(new Error('Redis connection error'));

      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      // Should still work, just without cache
      expect(response.status).toBe(200);
      expect(response.headers['x-cache']).toBe('error');
    });
  });

  describe('Query Optimization', () => {
    it('should use pagination for large datasets', async () => {
      // Create many projects
      const projectIds = [];
      for (let i = 0; i < 25; i++) {
        const result = await testDb.query(
          'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
          [`Project ${i}`, testUserId]
        );
        projectIds.push(result.rows[0].id);
      }

      // Test pagination
      const response = await request(app)
        .get('/api/projects?page=1&limit=10')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.projects).toHaveLength(10);
      expect(response.body.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 26, // 25 + 1 original
        totalPages: 3,
      });

      // Clean up
      await testDb.query('DELETE FROM projects WHERE id = ANY($1)', [projectIds]);
    });

    it('should optimize queries with proper indexing', async () => {
      // Check that queries use indexes
      const explainResult = await testDb.query(
        `
        EXPLAIN (FORMAT JSON) 
        SELECT * FROM projects 
        WHERE user_id = $1 
        ORDER BY created_at DESC 
        LIMIT 10
      `,
        [testUserId]
      );

      const plan = explainResult.rows[0]['QUERY PLAN'][0].Plan;

      // Should use index scan instead of sequential scan
      const hasIndexScan = JSON.stringify(plan).includes('Index Scan');
      expect(hasIndexScan).toBe(true);
    });

    it('should batch database operations', async () => {
      const imageData = [];
      for (let i = 0; i < 10; i++) {
        imageData.push({
          filename: `batch-${i}.png`,
          project_id: projectId,
          size: 1024,
        });
      }

      // Batch insert
      const startTime = Date.now();

      const values = imageData
        .map((img, index) => `($${index * 3 + 1}, $${index * 3 + 2}, $${index * 3 + 3})`)
        .join(', ');

      const params = imageData.flatMap((img) => [img.filename, img.project_id, img.size]);

      await testDb.query(
        `
        INSERT INTO images (filename, project_id, size)
        VALUES ${values}
      `,
        params
      );

      const executionTime = Date.now() - startTime;

      // Batch insert should be fast
      expect(executionTime).toBeLessThan(100);

      // Clean up
      await testDb.query('DELETE FROM images WHERE project_id = $1 AND filename LIKE $2', [
        projectId,
        'batch-%',
      ]);
    });
  });

  describe('Asset Optimization', () => {
    it('should support compressed responses', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`)
        .set('Accept-Encoding', 'gzip, deflate');

      expect(response.status).toBe(200);
      expect(response.headers['content-encoding']).toMatch(/gzip|deflate/);
    });

    it('should implement ETag caching', async () => {
      // First request
      const response1 = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response1.status).toBe(200);
      expect(response1.headers).toHaveProperty('etag');

      const etag = response1.headers.etag;

      // Second request with ETag
      const response2 = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .set('If-None-Match', etag);

      expect(response2.status).toBe(304); // Not Modified
    });

    it('should set appropriate cache headers', async () => {
      // Static assets should have long cache
      const staticResponse = await request(app).get('/api/static/logo.png');

      if (staticResponse.status === 200) {
        expect(staticResponse.headers['cache-control']).toContain('max-age=');
        expect(
          parseInt(staticResponse.headers['cache-control'].match(/max-age=(\d+)/)?.[1] || '0')
        ).toBeGreaterThan(3600); // At least 1 hour
      }

      // Dynamic content should have no-cache
      const dynamicResponse = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(dynamicResponse.headers['cache-control']).toMatch(/no-cache|no-store|private/);
    });
  });

  describe('Memory Optimization', () => {
    it('should stream large file downloads', async () => {
      // Mock large file
      const largeFileId = 'large-file-123';

      const response = await request(app)
        .get(`/api/download/${largeFileId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect('Content-Type', /application\/octet-stream/)
        .expect('Transfer-Encoding', 'chunked')
        .responseType('blob');

      // Should use streaming
      expect(response.headers['transfer-encoding']).toBe('chunked');
    });

    it('should implement connection pooling', async () => {
      // Check database pool configuration
      const poolConfig = pool.options;

      expect(poolConfig.max).toBeGreaterThan(1); // Connection pooling enabled
      expect(poolConfig.idleTimeoutMillis).toBeDefined();
      expect(poolConfig.connectionTimeoutMillis).toBeDefined();
    });

    it('should prevent memory leaks in long-running operations', async () => {
      const initialMemory = process.memoryUsage();

      // Simulate multiple requests
      for (let i = 0; i < 100; i++) {
        await request(app).get('/api/health').expect(200);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();

      // Memory increase should be reasonable
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024); // Less than 50MB
    });
  });

  describe('Search Optimization', () => {
    it('should implement full-text search with indexing', async () => {
      // Create projects with searchable content
      await testDb.query('INSERT INTO projects (name, description, user_id) VALUES ($1, $2, $3)', [
        'Searchable Project',
        'This is a test project for full-text search',
        testUserId,
      ]);

      const response = await request(app)
        .get('/api/projects/search?q=full-text')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.results).toBeDefined();
      expect(response.headers['x-search-time']).toBeDefined();

      const searchTime = parseInt(response.headers['x-search-time']);
      expect(searchTime).toBeLessThan(100); // Search should be fast
    });

    it('should implement search result caching', async () => {
      const searchQuery = 'test project';
      const _cacheKey = `search:${crypto.createHash('md5').update(searchQuery).digest('hex')}`;

      // First search - cache miss
      redis.get.mockResolvedValueOnce(null);
      redis.setex.mockResolvedValueOnce('OK');

      const response1 = await request(app)
        .get(`/api/projects/search?q=${encodeURIComponent(searchQuery)}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response1.status).toBe(200);
      expect(response1.headers['x-cache']).toBe('miss');

      // Second search - cache hit
      redis.get.mockResolvedValueOnce(JSON.stringify(response1.body));

      const response2 = await request(app)
        .get(`/api/projects/search?q=${encodeURIComponent(searchQuery)}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response2.status).toBe(200);
      expect(response2.headers['x-cache']).toBe('hit');
    });
  });

  describe('CDN Integration', () => {
    it('should set CDN-friendly headers for static assets', async () => {
      const response = await request(app).get('/api/static/assets/logo.png');

      if (response.status === 200) {
        expect(response.headers['cache-control']).toContain('public');
        expect(response.headers['cache-control']).toContain('immutable');
        expect(response.headers).toHaveProperty('expires');
        expect(response.headers).toHaveProperty('last-modified');
      }
    });

    it('should support CDN purging', async () => {
      const response = await request(app)
        .post('/api/cdn/purge')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          paths: ['/api/static/assets/*'],
          tags: ['static-assets'],
        });

      if (response.status === 200) {
        expect(response.body).toHaveProperty('purged');
        expect(response.body.purged).toBeGreaterThan(0);
      }
    });
  });

  describe('Lazy Loading and Code Splitting', () => {
    it('should support partial data loading', async () => {
      // Request with field selection
      const response = await request(app)
        .get(`/api/projects/${projectId}?fields=id,name,created_at`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('created_at');
      expect(response.body).not.toHaveProperty('description');
    });

    it('should implement cursor-based pagination', async () => {
      // First page
      const response1 = await request(app)
        .get('/api/projects?cursor=&limit=5')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response1.status).toBe(200);
      expect(response1.body).toHaveProperty('data');
      expect(response1.body).toHaveProperty('next_cursor');

      if (response1.body.next_cursor) {
        // Next page
        const response2 = await request(app)
          .get(`/api/projects?cursor=${response1.body.next_cursor}&limit=5`)
          .set('Authorization', `Bearer ${accessToken}`);

        expect(response2.status).toBe(200);
        expect(response2.body.data[0].id).not.toBe(response1.body.data[0].id);
      }
    });
  });

  describe('Request Deduplication', () => {
    it('should deduplicate concurrent identical requests', async () => {
      redis.get.mockResolvedValue(null);
      redis.set.mockResolvedValue('OK');

      // Make concurrent identical requests
      const promises = Array(5)
        .fill(null)
        .map(() =>
          request(app)
            .get(`/api/projects/${projectId}`)
            .set('Authorization', `Bearer ${accessToken}`)
        );

      const responses = await Promise.all(promises);

      // All should succeed
      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });

      // But database should only be queried once
      // This would be tracked by a request deduplication middleware
      expect(
        responses.filter((r) => r.headers['x-deduplicated'] === 'true').length
      ).toBeGreaterThanOrEqual(4);
    });
  });

  describe('Background Job Optimization', () => {
    it('should queue heavy operations', async () => {
      const response = await request(app)
        .post('/api/export/bulk')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          project_ids: [projectId],
          format: 'csv',
          include_images: true,
        });

      expect(response.status).toBe(202); // Accepted
      expect(response.body).toHaveProperty('job_id');
      expect(response.body).toHaveProperty('status', 'queued');
    });

    it('should implement job result caching', async () => {
      const jobId = 'export-job-123';
      const _cacheKey = `job:result:${jobId}`;

      // Job completed - result in cache
      redis.get.mockResolvedValueOnce(
        JSON.stringify({
          status: 'completed',
          result_url: '/downloads/export-123.zip',
        })
      );

      const response = await request(app)
        .get(`/api/jobs/${jobId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'completed');
      expect(response.headers['x-cache']).toBe('hit');
    });
  });
});
