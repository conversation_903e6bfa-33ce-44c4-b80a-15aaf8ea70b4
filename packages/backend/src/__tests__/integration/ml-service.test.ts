/**
 * ML Service Integration Tests
 * Tests for ML model integration, segmentation pipeline, and model management
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import axios from 'axios';
import path from 'path';
import fs from 'fs/promises';

// Mock axios for ML service calls
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ML Service Integration', () => {
  let app: Express;
  let accessToken: string;
  let testUserId: number;
  let projectId: number;
  let imageId: number;
  let testImagePath: string;

  beforeAll(async () => {
    app = await createApp();

    // Create test user
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'ML Test User']
    );
    testUserId = userResult.rows[0].id;

    // Create test project
    const projectResult = await testDb.query(
      'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
      ['ML Test Project', testUserId]
    );
    projectId = projectResult.rows[0].id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Create test image
    testImagePath = path.join(__dirname, 'ml-test-image.png');
    const imageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    await fs.writeFile(testImagePath, imageBuffer);

    // Create image record
    const imageResult = await testDb.query(
      'INSERT INTO images (filename, project_id, size, width, height) VALUES ($1, $2, $3, $4, $5) RETURNING id',
      ['ml-test.png', projectId, 1024, 800, 600]
    );
    imageId = imageResult.rows[0].id;
  });

  afterAll(async () => {
    // Clean up
    if (testImagePath) {
      await fs.unlink(testImagePath).catch(() => {});
    }
    await testDb.query('DELETE FROM images WHERE project_id = $1', [projectId]);
    await testDb.query('DELETE FROM projects WHERE user_id = $1', [testUserId]);
    await testDb.query('DELETE FROM users WHERE id = $1', [testUserId]);
  });

  describe('ML Service Health Check', () => {
    it('should check ML service availability', async () => {
      // Mock ML service health check response
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          status: 'healthy',
          models: {
            resunet: { status: 'loaded', version: '1.0.0' },
            unet: { status: 'loaded', version: '1.0.0' },
          },
          gpu: {
            available: true,
            memory: { total: 8192, used: 2048, free: 6144 },
          },
        },
      });

      const response = await request(app)
        .get('/api/ml/health')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('models');
      expect(response.body.models).toHaveProperty('resunet');
      expect(response.body).toHaveProperty('gpu');
    });

    it('should handle ML service unavailability', async () => {
      // Mock ML service connection error
      mockedAxios.get.mockRejectedValueOnce(new Error('Connection refused'));

      const response = await request(app)
        .get('/api/ml/health')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(503);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('ML service unavailable');
    });
  });

  describe('Model Management', () => {
    it('should list available models', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          models: [
            {
              name: 'resunet',
              version: '1.0.0',
              description: 'ResUNet model for cell segmentation',
              input_size: [512, 512],
              performance: { accuracy: 0.95, speed: 'fast' },
            },
            {
              name: 'unet',
              version: '1.0.0',
              description: 'U-Net model for cell segmentation',
              input_size: [256, 256],
              performance: { accuracy: 0.92, speed: 'very fast' },
            },
          ],
        },
      });

      const response = await request(app)
        .get('/api/ml/models')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('models');
      expect(response.body.models).toHaveLength(2);
      expect(response.body.models[0]).toHaveProperty('name', 'resunet');
      expect(response.body.models[0]).toHaveProperty('performance');
    });

    it('should get model details', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          name: 'resunet',
          version: '1.0.0',
          description: 'ResUNet model optimized for spheroid cell segmentation',
          architecture: {
            input_size: [512, 512, 3],
            output_size: [512, 512, 1],
            parameters: 23000000,
          },
          training: {
            dataset: 'SpheroidCells-v2',
            epochs: 100,
            accuracy: 0.95,
            loss: 0.05,
          },
          requirements: {
            min_gpu_memory: 4096,
            recommended_gpu_memory: 8192,
          },
        },
      });

      const response = await request(app)
        .get('/api/ml/models/resunet')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', 'resunet');
      expect(response.body).toHaveProperty('architecture');
      expect(response.body).toHaveProperty('training');
      expect(response.body).toHaveProperty('requirements');
    });
  });

  describe('Segmentation Pipeline', () => {
    it('should trigger segmentation with default model', async () => {
      // Mock ML service segmentation response
      mockedAxios.post.mockResolvedValueOnce({
        status: 202,
        data: {
          job_id: 'seg-job-123',
          status: 'queued',
          estimated_time: 30,
        },
      });

      const response = await request(app)
        .post(`/api/images/${imageId}/segment`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
          parameters: {
            threshold: 0.5,
            min_size: 10,
          },
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('job_id');
      expect(response.body).toHaveProperty('status', 'queued');
      expect(response.body).toHaveProperty('estimated_time');
    });

    it('should trigger batch segmentation', async () => {
      // Create multiple images
      const imageIds = [];
      for (let i = 0; i < 5; i++) {
        const result = await testDb.query(
          'INSERT INTO images (filename, project_id, size, width, height) VALUES ($1, $2, $3, $4, $5) RETURNING id',
          [`batch-test-${i}.png`, projectId, 1024, 800, 600]
        );
        imageIds.push(result.rows[0].id);
      }

      mockedAxios.post.mockResolvedValueOnce({
        status: 202,
        data: {
          batch_id: 'batch-123',
          jobs: imageIds.map((id, index) => ({
            image_id: id,
            job_id: `seg-job-batch-${index}`,
            status: 'queued',
          })),
          total: 5,
          estimated_time: 150,
        },
      });

      const response = await request(app)
        .post(`/api/projects/${projectId}/segment-batch`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          image_ids: imageIds,
          model: 'resunet',
          parameters: {
            threshold: 0.5,
            min_size: 10,
          },
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('batch_id');
      expect(response.body).toHaveProperty('jobs');
      expect(response.body.jobs).toHaveLength(5);
      expect(response.body).toHaveProperty('estimated_time');

      // Clean up
      await testDb.query('DELETE FROM images WHERE id = ANY($1)', [imageIds]);
    });

    it('should check segmentation job status', async () => {
      const jobId = 'seg-job-123';

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          job_id: jobId,
          status: 'processing',
          progress: 65,
          current_step: 'Detecting cell boundaries',
          steps_completed: 2,
          total_steps: 4,
        },
      });

      const response = await request(app)
        .get(`/api/segmentation/jobs/${jobId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('job_id', jobId);
      expect(response.body).toHaveProperty('status', 'processing');
      expect(response.body).toHaveProperty('progress', 65);
      expect(response.body).toHaveProperty('current_step');
    });

    it('should get segmentation results', async () => {
      const jobId = 'seg-job-123';

      // Mock completed segmentation
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          job_id: jobId,
          status: 'completed',
          result: {
            polygons: [
              {
                id: 1,
                points: [
                  [100, 100],
                  [200, 100],
                  [200, 200],
                  [100, 200],
                ],
                confidence: 0.95,
                area: 10000,
              },
              {
                id: 2,
                points: [
                  [300, 300],
                  [400, 300],
                  [400, 400],
                  [300, 400],
                ],
                confidence: 0.92,
                area: 10000,
              },
            ],
            metadata: {
              cell_count: 2,
              average_area: 10000,
              processing_time: 25.5,
              model_used: 'resunet',
            },
          },
        },
      });

      const response = await request(app)
        .get(`/api/images/${imageId}/segmentation`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('polygons');
      expect(response.body.polygons).toHaveLength(2);
      expect(response.body.polygons[0]).toHaveProperty('points');
      expect(response.body.polygons[0]).toHaveProperty('confidence');
      expect(response.body).toHaveProperty('metadata');
      expect(response.body.metadata).toHaveProperty('cell_count', 2);
    });

    it('should handle segmentation errors', async () => {
      mockedAxios.post.mockRejectedValueOnce({
        response: {
          status: 400,
          data: {
            error: 'Invalid image format',
            details: 'Image must be PNG or TIFF format',
          },
        },
      });

      const response = await request(app)
        .post(`/api/images/${imageId}/segment`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid image format');
    });
  });

  describe('Model Fine-tuning', () => {
    it('should upload training data for fine-tuning', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 202,
        data: {
          upload_id: 'upload-123',
          status: 'processing',
          files_received: 10,
        },
      });

      const response = await request(app)
        .post('/api/ml/training-data')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('dataset_name', 'CustomCells-v1')
        .field('description', 'Custom cell dataset for fine-tuning')
        .attach('images', testImagePath, 'cell1.png');

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('upload_id');
      expect(response.body).toHaveProperty('status', 'processing');
    });

    it('should start model fine-tuning', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 202,
        data: {
          training_id: 'train-123',
          status: 'queued',
          base_model: 'resunet',
          estimated_time: 3600, // 1 hour
        },
      });

      const response = await request(app)
        .post('/api/ml/fine-tune')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          base_model: 'resunet',
          dataset_id: 'upload-123',
          parameters: {
            epochs: 20,
            learning_rate: 0.001,
            batch_size: 16,
          },
          validation_split: 0.2,
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('training_id');
      expect(response.body).toHaveProperty('status', 'queued');
      expect(response.body).toHaveProperty('estimated_time');
    });

    it('should monitor training progress', async () => {
      const trainingId = 'train-123';

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          training_id: trainingId,
          status: 'training',
          current_epoch: 12,
          total_epochs: 20,
          metrics: {
            loss: 0.08,
            accuracy: 0.93,
            val_loss: 0.1,
            val_accuracy: 0.91,
          },
          estimated_remaining: 800,
        },
      });

      const response = await request(app)
        .get(`/api/ml/training/${trainingId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('training_id', trainingId);
      expect(response.body).toHaveProperty('status', 'training');
      expect(response.body).toHaveProperty('current_epoch', 12);
      expect(response.body).toHaveProperty('metrics');
      expect(response.body.metrics).toHaveProperty('accuracy');
    });
  });

  describe('Model Performance Analysis', () => {
    it('should analyze model performance on test set', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          model: 'resunet',
          metrics: {
            accuracy: 0.94,
            precision: 0.92,
            recall: 0.96,
            f1_score: 0.94,
            iou: 0.88,
          },
          confusion_matrix: {
            true_positive: 850,
            false_positive: 50,
            true_negative: 920,
            false_negative: 30,
          },
          per_class_metrics: {
            cell: { precision: 0.92, recall: 0.96, f1: 0.94 },
            background: { precision: 0.98, recall: 0.97, f1: 0.97 },
          },
        },
      });

      const response = await request(app)
        .post('/api/ml/evaluate')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
          test_dataset_id: 'test-set-123',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('metrics');
      expect(response.body.metrics).toHaveProperty('accuracy');
      expect(response.body.metrics).toHaveProperty('f1_score');
      expect(response.body).toHaveProperty('confusion_matrix');
      expect(response.body).toHaveProperty('per_class_metrics');
    });

    it('should compare multiple models', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          comparison: [
            {
              model: 'resunet',
              accuracy: 0.94,
              speed: 25.5,
              memory_usage: 4096,
            },
            {
              model: 'unet',
              accuracy: 0.91,
              speed: 15.2,
              memory_usage: 2048,
            },
          ],
          recommendation: {
            best_accuracy: 'resunet',
            best_speed: 'unet',
            best_balanced: 'resunet',
          },
        },
      });

      const response = await request(app)
        .post('/api/ml/compare-models')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          models: ['resunet', 'unet'],
          test_dataset_id: 'test-set-123',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('comparison');
      expect(response.body.comparison).toHaveLength(2);
      expect(response.body).toHaveProperty('recommendation');
    });
  });

  describe('GPU Resource Management', () => {
    it('should get GPU utilization', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          gpus: [
            {
              id: 0,
              name: 'NVIDIA Tesla T4',
              memory: { total: 16384, used: 4096, free: 12288 },
              utilization: 25,
              temperature: 45,
              processes: [
                {
                  pid: 1234,
                  name: 'segmentation',
                  memory_usage: 2048,
                },
              ],
            },
          ],
        },
      });

      const response = await request(app)
        .get('/api/ml/gpu-status')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('gpus');
      expect(response.body.gpus[0]).toHaveProperty('utilization');
      expect(response.body.gpus[0]).toHaveProperty('memory');
    });

    it('should handle GPU resource allocation', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 200,
        data: {
          allocated: true,
          gpu_id: 0,
          memory_allocated: 4096,
          reservation_id: 'gpu-res-123',
        },
      });

      const response = await request(app)
        .post('/api/ml/allocate-gpu')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          memory_required: 4096,
          duration: 3600,
          priority: 'high',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('allocated', true);
      expect(response.body).toHaveProperty('gpu_id');
      expect(response.body).toHaveProperty('reservation_id');
    });
  });

  describe('Model Deployment', () => {
    it('should deploy custom model', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        status: 202,
        data: {
          deployment_id: 'deploy-123',
          status: 'deploying',
          model_name: 'custom-resunet-v1',
          endpoint: null,
        },
      });

      const response = await request(app)
        .post('/api/ml/deploy')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model_id: 'train-123-final',
          deployment_name: 'custom-resunet-v1',
          auto_scale: true,
          min_instances: 1,
          max_instances: 5,
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('deployment_id');
      expect(response.body).toHaveProperty('status', 'deploying');
    });

    it('should get deployment status', async () => {
      const deploymentId = 'deploy-123';

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: {
          deployment_id: deploymentId,
          status: 'active',
          model_name: 'custom-resunet-v1',
          endpoint: 'https://ml.spheroseg.com/models/custom-resunet-v1',
          instances: 2,
          requests_per_minute: 150,
          average_latency: 250,
        },
      });

      const response = await request(app)
        .get(`/api/ml/deployments/${deploymentId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'active');
      expect(response.body).toHaveProperty('endpoint');
      expect(response.body).toHaveProperty('requests_per_minute');
    });
  });
});
