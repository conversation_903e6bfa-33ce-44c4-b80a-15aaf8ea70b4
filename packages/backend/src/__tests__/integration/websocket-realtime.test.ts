/**
 * WebSocket and Real-time Communication Tests
 * Tests for WebSocket connections, real-time updates, and event handling
 */

import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { io as SocketIOClient, Socket } from 'socket.io-client';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';

describe('WebSocket and Real-time Communication', () => {
  let app: Express;
  let httpServer: any;
  let io: SocketIOServer;
  let clientSocket: Socket;
  let testUserId: number;
  let accessToken: string;
  let projectId: number;

  beforeAll(async () => {
    // Create Express app
    app = await createApp();
    httpServer = createServer(app);

    // Initialize Socket.IO server
    io = new SocketIOServer(httpServer, {
      cors: {
        origin: '*',
        credentials: true,
      },
    });

    // Set up Socket.IO authentication middleware
    io.use((socket, next) => {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      try {
        const decoded = jwt.verify(token, config.auth.jwtSecret || 'test-secret') as any;
        socket.data.userId = decoded.userId || decoded.id;
        socket.data.email = decoded.email;
        next();
      } catch (err) {
        next(new Error('Authentication error'));
      }
    });

    // Set up Socket.IO event handlers
    io.on('connection', (socket) => {
      console.log(`User ${socket.data.userId} connected`);

      // Join user-specific room
      socket.join(`user:${socket.data.userId}`);

      // Join project rooms
      socket.on('join:project', async (projectId: number) => {
        // Verify user has access to project
        const result = await testDb.query('SELECT id FROM projects WHERE id = $1 AND user_id = $2', [
          projectId,
          socket.data.userId,
        ]);

        if (result.rows.length > 0) {
          socket.join(`project:${projectId}`);
          socket.emit('joined:project', { projectId });
        } else {
          socket.emit('error', { message: 'Access denied to project' });
        }
      });

      // Handle segmentation updates
      socket.on('segmentation:start', (data) => {
        io.to(`project:${data.projectId}`).emit('segmentation:started', {
          imageId: data.imageId,
          jobId: data.jobId,
          startedBy: socket.data.userId,
        });
      });

      socket.on('segmentation:progress', (data) => {
        io.to(`project:${data.projectId}`).emit('segmentation:progress', {
          imageId: data.imageId,
          jobId: data.jobId,
          progress: data.progress,
        });
      });

      socket.on('segmentation:complete', (data) => {
        io.to(`project:${data.projectId}`).emit('segmentation:completed', {
          imageId: data.imageId,
          jobId: data.jobId,
          result: data.result,
        });
      });

      // Handle project updates
      socket.on('project:update', async (data) => {
        // Broadcast to all project members
        io.to(`project:${data.projectId}`).emit('project:updated', {
          projectId: data.projectId,
          changes: data.changes,
          updatedBy: socket.data.userId,
        });
      });

      // Handle collaboration events
      socket.on('cursor:move', (data) => {
        socket.to(`project:${data.projectId}`).emit('cursor:moved', {
          userId: socket.data.userId,
          position: data.position,
          imageId: data.imageId,
        });
      });

      socket.on('annotation:draw', (data) => {
        socket.to(`project:${data.projectId}`).emit('annotation:drawn', {
          userId: socket.data.userId,
          annotation: data.annotation,
          imageId: data.imageId,
        });
      });

      socket.on('disconnect', () => {
        console.log(`User ${socket.data.userId} disconnected`);
      });
    });

    // Start server
    await new Promise<void>((resolve) => {
      httpServer.listen(3001, () => {
        console.log('Test WebSocket server listening on port 3001');
        resolve();
      });
    });

    // Create test user and project
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'WebSocket Test User']
    );
    testUserId = userResult.rows[0].id;

    const projectResult = await testDb.query(
      'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
      ['WebSocket Test Project', testUserId]
    );
    projectId = projectResult.rows[0].id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Clean up
    if (clientSocket) {
      clientSocket.disconnect();
    }
    io.close();
    httpServer.close();

    await testDb.query('DELETE FROM projects WHERE user_id = $1', [testUserId]);
    await testDb.query('DELETE FROM users WHERE id = $1', [testUserId]);
  });

  beforeEach(() => {
    // Create new client for each test
    clientSocket = SocketIOClient('http://localhost:3001', {
      auth: {
        token: accessToken,
      },
    });
  });

  afterEach(() => {
    if (clientSocket) {
      clientSocket.disconnect();
    }
  });

  describe('Connection and Authentication', () => {
    it('should connect with valid authentication', (done) => {
      clientSocket.on('connect', () => {
        expect(clientSocket.connected).toBe(true);
        done();
      });
    });

    it('should reject connection without authentication', (done) => {
      const unauthSocket = SocketIOClient('http://localhost:3001', {
        auth: {},
      });

      unauthSocket.on('connect_error', (error) => {
        expect(error.message).toContain('Authentication');
        unauthSocket.disconnect();
        done();
      });
    });

    it('should reject connection with invalid token', (done) => {
      const invalidSocket = SocketIOClient('http://localhost:3001', {
        auth: {
          token: 'invalid-token',
        },
      });

      invalidSocket.on('connect_error', (error) => {
        expect(error.message).toContain('Authentication');
        invalidSocket.disconnect();
        done();
      });
    });
  });

  describe('Project Room Management', () => {
    it('should join project room with access', (done) => {
      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', (data) => {
          expect(data.projectId).toBe(projectId);
          done();
        });

        clientSocket.emit('join:project', projectId);
      });
    });

    it('should reject joining project without access', (done) => {
      clientSocket.on('connect', () => {
        clientSocket.on('error', (data) => {
          expect(data.message).toContain('Access denied');
          done();
        });

        clientSocket.emit('join:project', 999999); // Non-existent project
      });
    });

    it('should receive updates for joined projects', (done) => {
      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Create another client to send update
          const senderSocket = SocketIOClient('http://localhost:3001', {
            auth: { token: accessToken },
          });

          senderSocket.on('connect', () => {
            senderSocket.emit('join:project', projectId);

            setTimeout(() => {
              senderSocket.emit('project:update', {
                projectId,
                changes: { name: 'Updated Project Name' },
              });
            }, 100);
          });
        });

        clientSocket.on('project:updated', (data) => {
          expect(data.projectId).toBe(projectId);
          expect(data.changes).toHaveProperty('name', 'Updated Project Name');
          expect(data.updatedBy).toBe(testUserId);
          done();
        });

        clientSocket.emit('join:project', projectId);
      });
    });
  });

  describe('Segmentation Real-time Updates', () => {
    it('should broadcast segmentation start event', (done) => {
      const imageId = 123;
      const jobId = 'job-123';

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Send segmentation start event
          clientSocket.emit('segmentation:start', {
            projectId,
            imageId,
            jobId,
          });
        });

        clientSocket.on('segmentation:started', (data) => {
          expect(data.imageId).toBe(imageId);
          expect(data.jobId).toBe(jobId);
          expect(data.startedBy).toBe(testUserId);
          done();
        });

        clientSocket.emit('join:project', projectId);
      });
    });

    it('should broadcast segmentation progress updates', (done) => {
      const imageId = 123;
      const jobId = 'job-123';
      const progressUpdates: number[] = [];

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Send progress updates
          [25, 50, 75, 100].forEach((progress, index) => {
            setTimeout(() => {
              clientSocket.emit('segmentation:progress', {
                projectId,
                imageId,
                jobId,
                progress,
              });
            }, index * 50);
          });
        });

        clientSocket.on('segmentation:progress', (data) => {
          progressUpdates.push(data.progress);

          if (progressUpdates.length === 4) {
            expect(progressUpdates).toEqual([25, 50, 75, 100]);
            done();
          }
        });

        clientSocket.emit('join:project', projectId);
      });
    });

    it('should broadcast segmentation completion', (done) => {
      const imageId = 123;
      const jobId = 'job-123';

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          clientSocket.emit('segmentation:complete', {
            projectId,
            imageId,
            jobId,
            result: {
              polygons: [
                {
                  points: [
                    [0, 0],
                    [10, 0],
                    [10, 10],
                    [0, 10],
                  ],
                },
              ],
              cellCount: 1,
            },
          });
        });

        clientSocket.on('segmentation:completed', (data) => {
          expect(data.imageId).toBe(imageId);
          expect(data.jobId).toBe(jobId);
          expect(data.result).toHaveProperty('polygons');
          expect(data.result).toHaveProperty('cellCount', 1);
          done();
        });

        clientSocket.emit('join:project', projectId);
      });
    });
  });

  describe('Collaboration Features', () => {
    it('should broadcast cursor movements', (done) => {
      const imageId = 456;
      const position = { x: 100, y: 200 };

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Create second client to receive cursor updates
          const observerSocket = SocketIOClient('http://localhost:3001', {
            auth: { token: accessToken },
          });

          observerSocket.on('connect', () => {
            observerSocket.emit('join:project', projectId);

            observerSocket.on('cursor:moved', (data) => {
              expect(data.userId).toBe(testUserId);
              expect(data.position).toEqual(position);
              expect(data.imageId).toBe(imageId);
              observerSocket.disconnect();
              done();
            });

            // Send cursor movement from first client
            setTimeout(() => {
              clientSocket.emit('cursor:move', {
                projectId,
                imageId,
                position,
              });
            }, 100);
          });
        });

        clientSocket.emit('join:project', projectId);
      });
    });

    it('should broadcast annotation drawings', (done) => {
      const imageId = 456;
      const annotation = {
        type: 'polygon',
        points: [
          [0, 0],
          [50, 0],
          [50, 50],
          [0, 50],
        ],
        color: '#FF0000',
      };

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Create second client to receive annotation updates
          const observerSocket = SocketIOClient('http://localhost:3001', {
            auth: { token: accessToken },
          });

          observerSocket.on('connect', () => {
            observerSocket.emit('join:project', projectId);

            observerSocket.on('annotation:drawn', (data) => {
              expect(data.userId).toBe(testUserId);
              expect(data.annotation).toEqual(annotation);
              expect(data.imageId).toBe(imageId);
              observerSocket.disconnect();
              done();
            });

            // Send annotation from first client
            setTimeout(() => {
              clientSocket.emit('annotation:draw', {
                projectId,
                imageId,
                annotation,
              });
            }, 100);
          });
        });

        clientSocket.emit('join:project', projectId);
      });
    });
  });

  describe('Connection Resilience', () => {
    it('should handle reconnection', (done) => {
      let disconnectCount = 0;
      let connectCount = 0;

      clientSocket.on('connect', () => {
        connectCount++;

        if (connectCount === 1) {
          // First connection, force disconnect
          clientSocket.disconnect();
        } else if (connectCount === 2) {
          // Reconnected successfully
          expect(disconnectCount).toBe(1);
          done();
        }
      });

      clientSocket.on('disconnect', () => {
        disconnectCount++;

        // Reconnect after disconnect
        setTimeout(() => {
          clientSocket.connect();
        }, 100);
      });
    });

    it('should queue messages during disconnection', (done) => {
      const messages: any[] = [];

      clientSocket.on('connect', () => {
        clientSocket.emit('join:project', projectId);
      });

      clientSocket.on('joined:project', () => {
        // Set up listener for project updates
        clientSocket.on('project:updated', (data) => {
          messages.push(data);

          if (messages.length === 2) {
            // Should receive both messages after reconnection
            expect(messages[0].changes.name).toBe('Update 1');
            expect(messages[1].changes.name).toBe('Update 2');
            done();
          }
        });

        // Disconnect
        clientSocket.disconnect();

        // Try to send messages while disconnected (should queue)
        clientSocket.emit('project:update', {
          projectId,
          changes: { name: 'Update 1' },
        });

        clientSocket.emit('project:update', {
          projectId,
          changes: { name: 'Update 2' },
        });

        // Reconnect
        setTimeout(() => {
          clientSocket.connect();
        }, 100);
      });
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle high-frequency updates', (done) => {
      const updateCount = 100;
      const receivedUpdates: number[] = [];

      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Send many updates rapidly
          for (let i = 0; i < updateCount; i++) {
            clientSocket.emit('cursor:move', {
              projectId,
              imageId: 1,
              position: { x: i, y: i },
            });
          }
        });

        clientSocket.on('cursor:moved', (data) => {
          receivedUpdates.push(data.position.x);

          if (receivedUpdates.length === updateCount) {
            // Should receive all updates in order
            expect(receivedUpdates.length).toBe(updateCount);
            expect(receivedUpdates[0]).toBe(0);
            expect(receivedUpdates[updateCount - 1]).toBe(updateCount - 1);
            done();
          }
        });

        clientSocket.emit('join:project', projectId);
      });
    });

    it('should handle multiple concurrent connections', (done) => {
      const clientCount = 10;
      const clients: Socket[] = [];
      let connectedCount = 0;

      // Create multiple clients
      for (let i = 0; i < clientCount; i++) {
        const client = SocketIOClient('http://localhost:3001', {
          auth: { token: accessToken },
        });

        client.on('connect', () => {
          connectedCount++;

          if (connectedCount === clientCount) {
            // All clients connected successfully
            expect(clients.every((c) => c.connected)).toBe(true);

            // Clean up
            clients.forEach((c) => c.disconnect());
            done();
          }
        });

        clients.push(client);
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle malformed messages gracefully', (done) => {
      clientSocket.on('connect', () => {
        clientSocket.on('error', (error) => {
          expect(error).toBeDefined();
          done();
        });

        // Send malformed data
        clientSocket.emit('join:project', 'not-a-number');
      });
    });

    it('should handle server errors gracefully', (done) => {
      clientSocket.on('connect', () => {
        clientSocket.on('joined:project', () => {
          // Simulate server error by sending invalid segmentation data
          clientSocket.emit('segmentation:complete', {
            projectId,
            // Missing required fields
          });
        });

        clientSocket.on('error', (error) => {
          expect(error).toBeDefined();
          done();
        });

        clientSocket.emit('join:project', projectId);
      });
    });
  });
});
