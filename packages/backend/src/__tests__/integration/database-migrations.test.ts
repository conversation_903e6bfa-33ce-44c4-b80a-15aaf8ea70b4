/**
 * Database Migration Tests
 * Tests for database migrations, schema changes, and data integrity
 */

import { Pool } from 'pg';
import crypto from 'crypto';

// Migration runner utility
interface Migration {
  id: string;
  name: string;
  up: string;
  down: string;
  checksum: string;
}

class MigrationRunner {
  constructor(private pool: Pool) {}

  async createMigrationsTable(): Promise<void> {
    await this.pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(64) NOT NULL,
        execution_time INTEGER
      )
    `);
  }

  async getAppliedMigrations(): Promise<string[]> {
    const result = await this.pool.query('SELECT id FROM migrations ORDER BY applied_at');
    return result.rows.map((row) => row.id);
  }

  async applyMigration(migration: Migration): Promise<void> {
    const startTime = Date.now();
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Execute migration
      await client.query(migration.up);

      // Record migration
      await client.query(
        'INSERT INTO migrations (id, name, checksum, execution_time) VALUES ($1, $2, $3, $4)',
        [migration.id, migration.name, migration.checksum, Date.now() - startTime]
      );

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  async rollbackMigration(migration: Migration): Promise<void> {
    const client = await this.pool.connect();

    try {
      await client.query('BEGIN');

      // Execute rollback
      await client.query(migration.down);

      // Remove migration record
      await client.query('DELETE FROM migrations WHERE id = $1', [migration.id]);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}

describe('Database Migration Tests', () => {
  let pool: Pool;
  let testDbName: string;
  let migrationRunner: MigrationRunner;

  beforeAll(async () => {
    // Create test database
    testDbName = `test_migrations_${Date.now()}`;

    const adminPool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: 'postgres',
    });

    await adminPool.query(`CREATE DATABASE ${testDbName}`);
    await adminPool.end();

    // Connect to test database
    pool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: testDbName,
    });

    migrationRunner = new MigrationRunner(pool);
    await migrationRunner.createMigrationsTable();
  });

  afterAll(async () => {
    

    // Drop test database
    const adminPool = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      database: 'postgres',
    });

    await adminPool.query(`DROP DATABASE IF EXISTS ${testDbName}`);
    await adminPool.end();
  });

  describe('Migration Infrastructure', () => {
    it('should create migrations table', async () => {
      const result = await testDb.query(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = 'migrations'
        ORDER BY ordinal_position
      `);

      const columns = result.rows.map((row) => row.column_name);
      expect(columns).toContain('id');
      expect(columns).toContain('name');
      expect(columns).toContain('applied_at');
      expect(columns).toContain('checksum');
      expect(columns).toContain('execution_time');
    });

    it('should track applied migrations', async () => {
      const migration: Migration = {
        id: '001_test_migration',
        name: 'create_test_table',
        up: 'CREATE TABLE test_table (id SERIAL PRIMARY KEY, name VARCHAR(255))',
        down: 'DROP TABLE test_table',
        checksum: crypto.createHash('sha256').update('test').digest('hex'),
      };

      await migrationRunner.applyMigration(migration);

      const applied = await migrationRunner.getAppliedMigrations();
      expect(applied).toContain('001_test_migration');

      // Verify table was created
      const tableExists = await testDb.query(`
        SELECT EXISTS (
          SELECT FROM pg_tables 
          WHERE tablename = 'test_table'
        )
      `);
      expect(tableExists.rows[0].exists).toBe(true);

      // Rollback
      await migrationRunner.rollbackMigration(migration);

      const appliedAfterRollback = await migrationRunner.getAppliedMigrations();
      expect(appliedAfterRollback).not.toContain('001_test_migration');
    });
  });

  describe('Schema Migrations', () => {
    it('should handle user table migrations', async () => {
      const userTableMigration: Migration = {
        id: '002_create_users',
        name: 'create_users_table',
        up: `
          CREATE TABLE users (
            id SERIAL PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            name VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          
          CREATE INDEX idx_users_email ON users(email);
          CREATE INDEX idx_users_created_at ON users(created_at);
        `,
        down: `
          DROP INDEX IF EXISTS idx_users_created_at;
          DROP INDEX IF EXISTS idx_users_email;
          DROP TABLE IF EXISTS users;
        `,
        checksum: crypto.createHash('sha256').update('users').digest('hex'),
      };

      await migrationRunner.applyMigration(userTableMigration);

      // Verify table structure
      const columns = await testDb.query(`
        SELECT column_name, data_type, is_nullable
        FROM information_schema.columns
        WHERE table_name = 'users'
        ORDER BY ordinal_position
      `);

      expect(columns.rows).toHaveLength(6);
      expect(columns.rows[0]).toMatchObject({
        column_name: 'id',
        data_type: 'integer',
        is_nullable: 'NO',
      });
      expect(columns.rows[1]).toMatchObject({
        column_name: 'email',
        data_type: 'character varying',
        is_nullable: 'NO',
      });

      // Verify indexes
      const indexes = await testDb.query(`
        SELECT indexname FROM pg_indexes
        WHERE tablename = 'users'
        AND indexname LIKE 'idx_%'
      `);

      expect(indexes.rows.map((r) => r.indexname)).toContain('idx_users_email');
      expect(indexes.rows.map((r) => r.indexname)).toContain('idx_users_created_at');
    });

    it('should handle project table migrations with foreign keys', async () => {
      const projectTableMigration: Migration = {
        id: '003_create_projects',
        name: 'create_projects_table',
        up: `
          CREATE TABLE projects (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          
          CREATE INDEX idx_projects_user_id ON projects(user_id);
          CREATE INDEX idx_projects_created_at ON projects(created_at);
        `,
        down: `
          DROP TABLE IF EXISTS projects;
        `,
        checksum: crypto.createHash('sha256').update('projects').digest('hex'),
      };

      await migrationRunner.applyMigration(projectTableMigration);

      // Verify foreign key constraint
      const foreignKeys = await testDb.query(`
        SELECT
          tc.constraint_name,
          tc.table_name,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name,
          rc.delete_rule
        FROM information_schema.table_constraints AS tc
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
        JOIN information_schema.referential_constraints AS rc
          ON rc.constraint_name = tc.constraint_name
        WHERE tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_name = 'projects'
      `);

      expect(foreignKeys.rows).toHaveLength(1);
      expect(foreignKeys.rows[0]).toMatchObject({
        column_name: 'user_id',
        foreign_table_name: 'users',
        foreign_column_name: 'id',
        delete_rule: 'CASCADE',
      });
    });
  });

  describe('Data Migrations', () => {
    it('should handle data transformation migrations', async () => {
      // Add column migration
      const addColumnMigration: Migration = {
        id: '004_add_user_status',
        name: 'add_status_column_to_users',
        up: `
          ALTER TABLE users ADD COLUMN status VARCHAR(50) DEFAULT 'active';
          UPDATE users SET status = 'active' WHERE status IS NULL;
          ALTER TABLE users ALTER COLUMN status SET NOT NULL;
        `,
        down: `
          ALTER TABLE users DROP COLUMN status;
        `,
        checksum: crypto.createHash('sha256').update('user_status').digest('hex'),
      };

      // Insert test data
      await testDb.query(`
        INSERT INTO users (email, password_hash, name)
        VALUES 
          ('<EMAIL>', 'hash1', 'User 1'),
          ('<EMAIL>', 'hash2', 'User 2')
      `);

      await migrationRunner.applyMigration(addColumnMigration);

      // Verify column exists and data is correct
      const users = await testDb.query('SELECT email, status FROM users');
      expect(users.rows).toHaveLength(2);
      users.rows.forEach((user) => {
        expect(user.status).toBe('active');
      });
    });

    it('should handle complex data migrations with transactions', async () => {
      const complexMigration: Migration = {
        id: '005_normalize_user_profiles',
        name: 'normalize_user_profiles',
        up: `
          -- Create new table for user profiles
          CREATE TABLE user_profiles (
            id SERIAL PRIMARY KEY,
            user_id INTEGER UNIQUE REFERENCES users(id) ON DELETE CASCADE,
            bio TEXT,
            avatar_url VARCHAR(500),
            location VARCHAR(255),
            website VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );

          -- Migrate existing data
          INSERT INTO user_profiles (user_id, bio, created_at)
          SELECT id, name, created_at FROM users;

          -- Add new columns to users table
          ALTER TABLE users ADD COLUMN last_login TIMESTAMP;
          ALTER TABLE users ADD COLUMN login_count INTEGER DEFAULT 0;
        `,
        down: `
          ALTER TABLE users DROP COLUMN IF EXISTS login_count;
          ALTER TABLE users DROP COLUMN IF EXISTS last_login;
          DROP TABLE IF EXISTS user_profiles;
        `,
        checksum: crypto.createHash('sha256').update('user_profiles').digest('hex'),
      };

      await migrationRunner.applyMigration(complexMigration);

      // Verify data migration
      const profiles = await testDb.query(`
        SELECT up.*, u.email 
        FROM user_profiles up
        JOIN users u ON u.id = up.user_id
        ORDER BY up.user_id
      `);

      expect(profiles.rows).toHaveLength(2);
      expect(profiles.rows[0]).toHaveProperty('bio', 'User 1');
      expect(profiles.rows[1]).toHaveProperty('bio', 'User 2');
    });
  });

  describe('Migration Validation', () => {
    it('should validate migration checksums', async () => {
      const originalMigration: Migration = {
        id: '006_add_index',
        name: 'add_email_verification',
        up: 'ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE',
        down: 'ALTER TABLE users DROP COLUMN email_verified',
        checksum: crypto.createHash('sha256').update('original').digest('hex'),
      };

      await migrationRunner.applyMigration(originalMigration);

      // Try to apply same migration with different content
      const modifiedMigration: Migration = {
        ...originalMigration,
        up: 'ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT TRUE', // Changed default
        checksum: crypto.createHash('sha256').update('modified').digest('hex'),
      };

      // Should detect checksum mismatch
      const appliedMigrations = await testDb.query('SELECT checksum FROM migrations WHERE id = $1', [
        originalMigration.id,
      ]);

      expect(appliedMigrations.rows[0].checksum).not.toBe(modifiedMigration.checksum);
    });

    it('should handle migration dependencies', async () => {
      const dependentMigration: Migration = {
        id: '007_add_project_images',
        name: 'create_images_table',
        up: `
          CREATE TABLE images (
            id SERIAL PRIMARY KEY,
            filename VARCHAR(500) NOT NULL,
            project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
            size INTEGER,
            width INTEGER,
            height INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
        `,
        down: 'DROP TABLE IF EXISTS images',
        checksum: crypto.createHash('sha256').update('images').digest('hex'),
      };

      // This should succeed because projects table exists
      await migrationRunner.applyMigration(dependentMigration);

      // Verify cascade delete works
      await testDb.query(`
        INSERT INTO projects (name, user_id) VALUES ('Test Project', 1)
      `);
      const projectResult = await testDb.query('SELECT id FROM projects WHERE name = $1', [
        'Test Project',
      ]);
      const projectId = projectResult.rows[0].id;

      await testDb.query(
        `
        INSERT INTO images (filename, project_id, size)
        VALUES ('test.png', $1, 1024)
      `,
        [projectId]
      );

      // Delete project should cascade to images
      await testDb.query('DELETE FROM projects WHERE id = $1', [projectId]);

      const images = await testDb.query('SELECT * FROM images WHERE project_id = $1', [projectId]);
      expect(images.rows).toHaveLength(0);
    });
  });

  describe('Migration Performance', () => {
    it('should handle large data migrations efficiently', async () => {
      const largeMigration: Migration = {
        id: '008_add_activity_log',
        name: 'create_activity_log_table',
        up: `
          CREATE TABLE activity_logs (
            id BIGSERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES users(id),
            action VARCHAR(100) NOT NULL,
            resource_type VARCHAR(50),
            resource_id INTEGER,
            metadata JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          
          CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
          CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
          CREATE INDEX idx_activity_logs_action ON activity_logs(action);
          CREATE INDEX idx_activity_logs_resource ON activity_logs(resource_type, resource_id);
        `,
        down: 'DROP TABLE IF EXISTS activity_logs',
        checksum: crypto.createHash('sha256').update('activity_logs').digest('hex'),
      };

      const startTime = Date.now();
      await migrationRunner.applyMigration(largeMigration);
      const executionTime = Date.now() - startTime;

      // Migration should complete quickly even with multiple indexes
      expect(executionTime).toBeLessThan(1000); // Less than 1 second

      // Verify indexes were created
      const indexes = await testDb.query(`
        SELECT indexname FROM pg_indexes
        WHERE tablename = 'activity_logs'
        AND indexname LIKE 'idx_%'
      `);

      expect(indexes.rows).toHaveLength(4);
    });

    it('should handle batch data updates efficiently', async () => {
      // Insert many records
      const userCount = 1000;
      const values = [];
      for (let i = 0; i < userCount; i++) {
        values.push(`('user${i}@test.com', 'hash${i}', 'User ${i}')`);
      }

      await testDb.query(`
        INSERT INTO users (email, password_hash, name)
        VALUES ${values.join(', ')}
      `);

      const batchUpdateMigration: Migration = {
        id: '009_update_user_status_batch',
        name: 'batch_update_user_status',
        up: `
          -- Update in batches to avoid locking
          DO $$
          DECLARE
            batch_size INTEGER := 100;
            offset_val INTEGER := 0;
            total_updated INTEGER := 0;
          BEGIN
            LOOP
              WITH batch AS (
                SELECT id FROM users
                WHERE status = 'active'
                ORDER BY id
                LIMIT batch_size
                OFFSET offset_val
              )
              UPDATE users u
              SET 
                status = CASE 
                  WHEN u.email LIKE '%@test.com' THEN 'test'
                  ELSE 'active'
                END,
                updated_at = CURRENT_TIMESTAMP
              FROM batch
              WHERE u.id = batch.id;
              
              GET DIAGNOSTICS total_updated = ROW_COUNT;
              
              EXIT WHEN total_updated < batch_size;
              offset_val := offset_val + batch_size;
            END LOOP;
          END $$;
        `,
        down: `UPDATE users SET status = 'active' WHERE status = 'test'`,
        checksum: crypto.createHash('sha256').update('batch_update').digest('hex'),
      };

      const startTime = Date.now();
      await migrationRunner.applyMigration(batchUpdateMigration);
      const executionTime = Date.now() - startTime;

      // Batch update should be efficient
      expect(executionTime).toBeLessThan(5000); // Less than 5 seconds for 1000 records

      // Verify updates
      const testUsers = await testDb.query("SELECT COUNT(*) FROM users WHERE status = 'test'");
      expect(parseInt(testUsers.rows[0].count)).toBeGreaterThan(0);
    });
  });

  describe('Migration Rollback', () => {
    it('should successfully rollback migrations in reverse order', async () => {
      const migrations: Migration[] = [
        {
          id: '010_add_settings',
          name: 'create_user_settings',
          up: `
            CREATE TABLE user_settings (
              user_id INTEGER PRIMARY KEY REFERENCES users(id),
              theme VARCHAR(50) DEFAULT 'light',
              language VARCHAR(10) DEFAULT 'en',
              notifications JSONB DEFAULT '{}'::jsonb
            )
          `,
          down: 'DROP TABLE user_settings',
          checksum: crypto.createHash('sha256').update('settings').digest('hex'),
        },
        {
          id: '011_add_api_keys',
          name: 'create_api_keys',
          up: `
            CREATE TABLE api_keys (
              id SERIAL PRIMARY KEY,
              user_id INTEGER REFERENCES users(id),
              key_hash VARCHAR(255) UNIQUE NOT NULL,
              name VARCHAR(255),
              last_used TIMESTAMP,
              created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
          `,
          down: 'DROP TABLE api_keys',
          checksum: crypto.createHash('sha256').update('api_keys').digest('hex'),
        },
      ];

      // Apply migrations
      for (const migration of migrations) {
        await migrationRunner.applyMigration(migration);
      }

      // Verify tables exist
      let tables = await testDb.query(`
        SELECT tablename FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename IN ('user_settings', 'api_keys')
      `);
      expect(tables.rows).toHaveLength(2);

      // Rollback in reverse order
      for (const migration of migrations.reverse()) {
        await migrationRunner.rollbackMigration(migration);
      }

      // Verify tables are gone
      tables = await testDb.query(`
        SELECT tablename FROM pg_tables
        WHERE schemaname = 'public'
        AND tablename IN ('user_settings', 'api_keys')
      `);
      expect(tables.rows).toHaveLength(0);
    });
  });
});
