/**
 * E2E Project Management Tests
 * Comprehensive end-to-end tests for project CRUD operations and sharing
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';

describe('E2E Project Management Flow', () => {
  let app: Express;
  let testUserEmail: string;
  let testUserId: number;
  let accessToken: string;
  let projectId: number;
  let secondUserId: number;
  let secondUserToken: string;

  beforeAll(async () => {
    app = await createApp();

    // Clean up test data
    await testDb.query('DELETE FROM projects WHERE name LIKE $1', ['%E2E Test%']);
    await testDb.cleanupUsers('%@e2e-project.com');

    // Create test users
    testUserEmail = `user1-${Date.now()}@e2e-project.com`;
    const user1 = await testDb.createTestUser(testUserEmail, 'hashed', {
      name: 'Project Test User 1'
    });
    testUserId = user1.id;

    const user2Email = `user2-${Date.now()}@e2e-project.com`;
    const user2 = await testDb.createTestUser(user2Email, 'hashed', {
      name: 'Project Test User 2'
    });
    secondUserId = user2.id;

    // Generate tokens
    accessToken = jwt.sign(
      { userId: testUserId, email: testUserEmail },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    secondUserToken = jwt.sign(
      { userId: secondUserId, email: user2Email },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // Clean up
    await testDb.query('DELETE FROM projects WHERE name LIKE $1', ['%E2E Test%']);
    await testDb.cleanupUsers('%@e2e-project.com');
  });

  describe('Project Creation', () => {
    it('should create a new project with valid data', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test Project 1',
          description: 'This is a test project for E2E testing',
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name', 'E2E Test Project 1');
      expect(response.body).toHaveProperty('description', 'This is a test project for E2E testing');
      expect(response.body).toHaveProperty('user_id', testUserId);
      expect(response.body).toHaveProperty('created_at');
      expect(response.body).toHaveProperty('updated_at');

      projectId = response.body.id;
    });

    it('should create project without description', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test Project 2',
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('name', 'E2E Test Project 2');
      expect(response.body.description).toBeNull();
    });

    it('should reject project creation without name', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          description: 'Project without name',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject project creation without authentication', async () => {
      const response = await request(app).post('/api/projects').send({
        name: 'Unauthorized Project',
      });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Project Listing', () => {
    it('should list all user projects', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');
      expect(Array.isArray(response.body.projects)).toBe(true);
      expect(response.body.projects.length).toBeGreaterThanOrEqual(2);

      // Verify project structure
      const project = response.body.projects.find((p: any) => p.id === projectId);
      expect(project).toBeDefined();
      expect(project).toHaveProperty('name', 'E2E Test Project 1');
    });

    it('should paginate project list', async () => {
      const response = await request(app)
        .get('/api/projects')
        .query({ page: 1, limit: 1 })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body.projects.length).toBeLessThanOrEqual(1);
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.pagination).toHaveProperty('total');
      expect(response.body.pagination).toHaveProperty('page', 1);
      expect(response.body.pagination).toHaveProperty('limit', 1);
    });

    it('should not list projects for unauthenticated user', async () => {
      const response = await request(app).get('/api/projects');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should not list other users projects', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body.projects).toHaveLength(0);
    });
  });

  describe('Project Details', () => {
    it('should get project details by id', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', projectId);
      expect(response.body).toHaveProperty('name', 'E2E Test Project 1');
      expect(response.body).toHaveProperty('description');
      expect(response.body).toHaveProperty('user_id', testUserId);
      expect(response.body).toHaveProperty('image_count');
      expect(response.body).toHaveProperty('created_at');
    });

    it('should return 404 for non-existent project', async () => {
      const response = await request(app)
        .get('/api/projects/999999')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('error');
    });

    it('should not allow access to other users project', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Project Update', () => {
    it('should update project name and description', async () => {
      const response = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test Project Updated',
          description: 'Updated description for E2E test',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', 'E2E Test Project Updated');
      expect(response.body).toHaveProperty('description', 'Updated description for E2E test');
      expect(response.body).toHaveProperty('updated_at');
    });

    it('should update only project name', async () => {
      const response = await request(app)
        .patch(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test Project Final Name',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', 'E2E Test Project Final Name');
      expect(response.body).toHaveProperty('description', 'Updated description for E2E test');
    });

    it('should not allow other user to update project', async () => {
      const response = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${secondUserToken}`)
        .send({
          name: 'Hacked Project Name',
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    it('should validate project name length', async () => {
      const response = await request(app)
        .put(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'a'.repeat(256), // Too long
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Project Sharing', () => {
    it('should share project with another user', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/share`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          userId: secondUserId,
          permission: 'read',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('share');
      expect(response.body.share).toHaveProperty('user_id', secondUserId);
      expect(response.body.share).toHaveProperty('permission', 'read');
    });

    it('should allow shared user to view project', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', projectId);
      expect(response.body).toHaveProperty('shared_with_me', true);
    });

    it('should list shared projects', async () => {
      const response = await request(app)
        .get('/api/projects/shared')
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');
      expect(response.body.projects.length).toBeGreaterThan(0);

      const sharedProject = response.body.projects.find((p: any) => p.id === projectId);
      expect(sharedProject).toBeDefined();
      expect(sharedProject).toHaveProperty('permission', 'read');
    });

    it('should update share permission', async () => {
      const response = await request(app)
        .put(`/api/projects/${projectId}/share/${secondUserId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          permission: 'write',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('permission', 'write');
    });

    it('should revoke project share', async () => {
      const response = await request(app)
        .delete(`/api/projects/${projectId}/share/${secondUserId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify access is revoked
      const accessResponse = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(accessResponse.status).toBe(403);
    });

    it('should not allow non-owner to share project', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/share`)
        .set('Authorization', `Bearer ${secondUserToken}`)
        .send({
          userId: 999,
          permission: 'read',
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Project Statistics', () => {
    it('should get project statistics', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/stats`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('total_images');
      expect(response.body).toHaveProperty('segmented_images');
      expect(response.body).toHaveProperty('pending_images');
      expect(response.body).toHaveProperty('failed_images');
      expect(response.body).toHaveProperty('total_size_mb');
      expect(response.body).toHaveProperty('last_activity');
    });
  });

  describe('Project Export', () => {
    it('should export project metadata', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/export`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('project');
      expect(response.body).toHaveProperty('images');
      expect(response.body).toHaveProperty('segmentations');
      expect(response.body).toHaveProperty('export_date');
    });

    it('should export project as zip', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/export`)
        .query({ format: 'zip' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('application/zip');
      expect(response.headers['content-disposition']).toContain('attachment');
    });
  });

  describe('Project Duplication', () => {
    let duplicatedProjectId: number;

    it('should duplicate project', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/duplicate`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test Project Copy',
          includeImages: true,
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name', 'E2E Test Project Copy');
      expect(response.body).toHaveProperty('description', 'Updated description for E2E test');
      expect(response.body).toHaveProperty('source_project_id', projectId);

      duplicatedProjectId = response.body.id;
    });

    it('should not allow duplication by non-owner', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/duplicate`)
        .set('Authorization', `Bearer ${secondUserToken}`)
        .send({
          name: 'Unauthorized Copy',
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    afterAll(async () => {
      if (duplicatedProjectId) {
        await testDb.query('DELETE FROM projects WHERE id = $1', [duplicatedProjectId]);
      }
    });
  });

  describe('Project Deletion', () => {
    let deleteProjectId: number;

    beforeAll(async () => {
      // Create a project specifically for deletion
      const project = await testDb.createTestProject(testUserId.toString(), {
        name: 'E2E Test Delete Project'
      });
      deleteProjectId = project.id;
    });

    it('should soft delete project', async () => {
      const response = await request(app)
        .delete(`/api/projects/${deleteProjectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify project is soft deleted
      const getResponse = await request(app)
        .get(`/api/projects/${deleteProjectId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(getResponse.status).toBe(404);
    });

    it('should permanently delete project with force flag', async () => {
      // Create another project for permanent deletion
      const project = await testDb.createTestProject(testUserId.toString(), {
        name: 'E2E Test Force Delete Project'
      });
      const forceDeleteId = project.id;

      const response = await request(app)
        .delete(`/api/projects/${forceDeleteId}`)
        .query({ force: true })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('permanently');

      // Verify project is completely gone
      const dbResult = await testDb.query('SELECT * FROM projects WHERE id = $1', [forceDeleteId]);
      expect(dbResult.rows).toHaveLength(0);
    });

    it('should not allow deletion by non-owner', async () => {
      const response = await request(app)
        .delete(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${secondUserToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Project Search', () => {
    it('should search projects by name', async () => {
      const response = await request(app)
        .get('/api/projects/search')
        .query({ q: 'E2E Test' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');
      expect(response.body.projects.length).toBeGreaterThan(0);
      expect(response.body.projects.every((p: any) => p.name.includes('E2E Test'))).toBe(true);
    });

    it('should search with filters', async () => {
      const response = await request(app)
        .get('/api/projects/search')
        .query({
          q: 'Test',
          created_after: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          sort: 'created_at',
          order: 'desc',
        })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');

      // Verify sorting
      if (response.body.projects.length > 1) {
        const dates = response.body.projects.map((p: any) => new Date(p.created_at).getTime());
        expect(dates).toEqual([...dates].sort((a, b) => b - a));
      }
    });
  });
});
