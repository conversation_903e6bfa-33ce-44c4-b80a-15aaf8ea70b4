/**
 * Global E2E Test Teardown
 * Runs once after all tests
 */

import { Pool } from 'pg';
import { createClient } from 'redis';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.join(__dirname, '../../../.env.test') });

export default async function globalTeardown() {
  console.log('\n🧹 Cleaning up E2E test environment...\n');

  const databaseUrl =
    process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/spheroseg_test';
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379/1';

  // Clean up database
  const pool = new Pool({ connectionString: databaseUrl });
  try {
    // Clean up test data
    await testDb.query(
      'DELETE FROM images WHERE project_id IN (SELECT id FROM projects WHERE name LIKE $1)',
      ['%E2E Test%']
    );
    await testDb.query('DELETE FROM projects WHERE name LIKE $1', ['%E2E Test%']);
    await testDb.cleanupUsers('%@e2e-%');
    console.log('✅ Database cleaned up');
  } catch (error) {
    console.error('❌ Database cleanup failed:', error);
  } finally {
    
  }

  // Clean up Redis
  const redisClient = createClient({ url: redisUrl });
  try {
    await redisClient.connect();
    // Clear test keys
    const keys = await redisClient.keys('test:*');
    if (keys.length > 0) {
      await redisClient.del(keys);
    }
    console.log('✅ Redis cleaned up');
    await redisClient.disconnect();
  } catch (error) {
    console.warn('⚠️ Redis cleanup failed:', error);
  }

  console.log('\n✨ E2E test cleanup complete!\n');
}
