/**
 * E2E Authentication Tests
 * Comprehensive end-to-end tests for authentication flow including login, register, token refresh, and logout
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';

describe('E2E Authentication Flow', () => {
  let app: Express;
  let testUserEmail: string;
  let testUserPassword: string;
  let testUserId: number;
  let accessToken: string;
  let refreshToken: string;

  beforeAll(async () => {
    app = await createApp();

    // Clean up test data
    await testDb.cleanupUsers('%@e2e-test.com');

    // Set up test data
    testUserEmail = `test-${Date.now()}@e2e-test.com`;
    testUserPassword = 'Test123!@#';
  });

  afterAll(async () => {
    // Clean up
    await testDb.cleanupUsers('%@e2e-test.com');
  });

  describe('User Registration Flow', () => {
    it('should register a new user with valid data', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: testUserEmail,
        password: testUserPassword,
        name: 'E2E Test User',
      });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('message', 'User registered successfully');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id');
      expect(response.body.user).toHaveProperty('email', testUserEmail);
      expect(response.body.user).toHaveProperty('name', 'E2E Test User');
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('tokenType', 'Bearer');

      // Verify tokens are valid JWT
      const decodedAccess = jwt.decode(response.body.accessToken) as any;
      expect(decodedAccess).toHaveProperty('userId');
      expect(decodedAccess).toHaveProperty('email', testUserEmail);

      testUserId = response.body.user.id;
    });

    it('should reject registration with existing email', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: testUserEmail,
        password: 'AnotherPassword123!',
        name: 'Duplicate User',
      });

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('already exists');
    });

    it('should reject registration with invalid email format', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: 'invalid-email',
        password: testUserPassword,
        name: 'Invalid Email User',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject registration with weak password', async () => {
      const response = await request(app).post('/api/auth/register').send({
        email: '<EMAIL>',
        password: '123',
        name: 'Weak Password User',
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('User Login Flow', () => {
    it('should login with valid credentials', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: testUserEmail,
        password: testUserPassword,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Login successful');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user).toHaveProperty('id', testUserId);
      expect(response.body.user).toHaveProperty('email', testUserEmail);
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('tokenType', 'Bearer');

      // Store tokens for further tests
      accessToken = response.body.accessToken;
      refreshToken = response.body.refreshToken;

      // Verify token structure
      const decodedAccess = jwt.decode(accessToken) as any;
      expect(decodedAccess).toHaveProperty('userId', testUserId.toString());
      expect(decodedAccess).toHaveProperty('email', testUserEmail);
      expect(decodedAccess).toHaveProperty('exp');
      expect(decodedAccess.exp).toBeGreaterThan(Date.now() / 1000);
    });

    it('should reject login with invalid password', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: testUserEmail,
        password: 'WrongPassword123!',
      });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid credentials');
    });

    it('should reject login with non-existent email', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: testUserPassword,
      });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toContain('Invalid credentials');
    });

    it('should reject login with missing credentials', async () => {
      const response = await request(app).post('/api/auth/login').send({
        email: testUserEmail,
        // Missing password
      });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Protected Routes Access', () => {
    it('should access protected route with valid access token', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testUserId);
      expect(response.body).toHaveProperty('email', testUserEmail);
      expect(response.body).not.toHaveProperty('password');
    });

    it('should reject access without token', async () => {
      const response = await request(app).get('/api/users/me');

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject access with invalid token', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject access with expired token', async () => {
      // Create an expired token
      const expiredToken = jwt.sign(
        { userId: testUserId, email: testUserEmail },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '-1h' }
      );

      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${expiredToken}`);

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Token Refresh Flow', () => {
    it('should refresh access token with valid refresh token', async () => {
      const response = await request(app).post('/api/auth/refresh').send({
        refreshToken: refreshToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('accessToken');
      expect(response.body).toHaveProperty('refreshToken');
      expect(response.body).toHaveProperty('tokenType', 'Bearer');

      // Verify new access token is different
      expect(response.body.accessToken).not.toBe(accessToken);

      // Verify new access token works
      const meResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${response.body.accessToken}`);

      expect(meResponse.status).toBe(200);
      expect(meResponse.body).toHaveProperty('id', testUserId);

      // Update tokens for further tests
      accessToken = response.body.accessToken;
      refreshToken = response.body.refreshToken;
    });

    it('should reject refresh with invalid refresh token', async () => {
      const response = await request(app).post('/api/auth/refresh').send({
        refreshToken: 'invalid-refresh-token',
      });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject refresh with missing refresh token', async () => {
      const response = await request(app).post('/api/auth/refresh').send({});

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Logout Flow', () => {
    it('should logout and invalidate refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          refreshToken: refreshToken,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify refresh token is invalidated
      const refreshResponse = await request(app).post('/api/auth/refresh').send({
        refreshToken: refreshToken,
      });

      expect(refreshResponse.status).toBe(401);
    });

    it('should logout even without refresh token', async () => {
      // Get new tokens first
      const loginResponse = await request(app).post('/api/auth/login').send({
        email: testUserEmail,
        password: testUserPassword,
      });

      const newAccessToken = loginResponse.body.accessToken;

      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${newAccessToken}`)
        .send({});

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
    });
  });

  describe('Session-based Authentication', () => {
    let sessionCookie: string;

    it('should create session on login', async () => {
      const response = await request(app).post('/api/auth/session/login').send({
        email: testUserEmail,
        password: testUserPassword,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Login successful');
      expect(response.body).toHaveProperty('user');

      // Check for session cookie
      const cookies = response.headers['set-cookie'];
      expect(cookies).toBeDefined();
      expect(cookies[0]).toContain('spheroseg.sid');

      sessionCookie = cookies[0];
    });

    it('should access protected route with session', async () => {
      const response = await request(app).get('/api/users/me/session').set('Cookie', sessionCookie);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testUserId);
      expect(response.body).toHaveProperty('email', testUserEmail);
    });

    it('should logout and destroy session', async () => {
      const response = await request(app)
        .post('/api/auth/session/logout')
        .set('Cookie', sessionCookie);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify session is destroyed
      const meResponse = await request(app)
        .get('/api/users/me/session')
        .set('Cookie', sessionCookie);

      expect(meResponse.status).toBe(401);
    });
  });

  describe('Email Validation', () => {
    it('should check if email exists', async () => {
      const response = await request(app)
        .get('/api/auth/check-email')
        .query({ email: testUserEmail });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('exists', true);
    });

    it('should check if email does not exist', async () => {
      const response = await request(app)
        .get('/api/auth/check-email')
        .query({ email: '<EMAIL>' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('exists', false);
    });

    it('should reject check without email parameter', async () => {
      const response = await request(app).get('/api/auth/check-email');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('CSRF Protection', () => {
    it('should get CSRF token', async () => {
      const response = await request(app).get('/api/auth/csrf-token');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('csrfToken');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits on login attempts', async () => {
      // Make multiple rapid login attempts
      const promises = [];
      for (let i = 0; i < 15; i++) {
        promises.push(
          request(app)
            .post('/api/auth/login')
            .send({
              email: `ratelimit${i}@e2e-test.com`,
              password: 'wrong',
            })
        );
      }

      const responses = await Promise.all(promises);

      // Some requests should be rate limited
      const rateLimited = responses.filter((r) => r.status === 429);
      expect(rateLimited.length).toBeGreaterThan(0);

      if (rateLimited.length > 0) {
        expect(rateLimited[0].body).toHaveProperty('error');
        expect(rateLimited[0].headers).toHaveProperty('retry-after');
      }
    }, 10000); // Increase timeout for rate limit test
  });
});
