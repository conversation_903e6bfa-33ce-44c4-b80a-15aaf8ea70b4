/**
 * Global E2E Test Setup
 * Runs once before all tests
 */

import { Pool } from 'pg';
import { createClient } from 'redis';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config({ path: path.join(__dirname, '../../../.env.test') });

export default async function globalSetup() {
  console.log('\n🚀 Setting up E2E test environment...\n');

  const databaseUrl =
    process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/spheroseg_test';
  const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379/1';

  // Test database connection
  const pool = new Pool({ connectionString: databaseUrl });
  try {
    await testDb.query('SELECT 1');
    console.log('✅ Database connection successful');

    // Create test tables if needed
    await testDb.query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        bio TEXT,
        location VARCHAR(255),
        website VARCHAR(255),
        avatar_url VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deletion_requested_at TIMESTAMP
      )
    `);

    await testDb.query(`
      CREATE TABLE IF NOT EXISTS projects (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP
      )
    `);

    await testDb.query(`
      CREATE TABLE IF NOT EXISTS images (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) NOT NULL,
        project_id INTEGER REFERENCES projects(id) ON DELETE CASCADE,
        url VARCHAR(500),
        thumbnail_url VARCHAR(500),
        size INTEGER,
        mime_type VARCHAR(100),
        metadata JSONB,
        segmentation_status VARCHAR(50) DEFAULT 'pending',
        segmentation_data JSONB,
        segmented_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await testDb.query(`
      CREATE TABLE IF NOT EXISTS refresh_tokens (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
        token_id VARCHAR(255) UNIQUE NOT NULL,
        family_id VARCHAR(255) NOT NULL,
        device_id VARCHAR(255),
        user_agent VARCHAR(255),
        ip_address VARCHAR(45),
        is_revoked BOOLEAN DEFAULT FALSE,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('✅ Database tables ready');
  } catch (error) {
    console.error('❌ Database setup failed:', error);
    throw error;
  } finally {
    
  }

  // Test Redis connection
  const redisClient = createClient({ url: redisUrl });
  try {
    await redisClient.connect();
    await redisClient.ping();
    console.log('✅ Redis connection successful');
    await redisClient.disconnect();
  } catch (error) {
    console.warn('⚠️ Redis connection failed (tests will continue without Redis):', error);
  }

  console.log('\n✨ E2E test environment ready!\n');
}
