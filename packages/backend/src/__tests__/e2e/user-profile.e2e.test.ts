/**
 * E2E User Profile and Settings Tests
 * Comprehensive end-to-end tests for user profile management and application settings
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import bcrypt from 'bcryptjs';
import path from 'path';
import fs from 'fs/promises';
describe('E2E User Profile and Settings Flow', () => {
  let app: Express;
  let testUserId: number;
  let testUserEmail: string;
  let accessToken: string;
  let testAvatarPath: string;

  beforeAll(async () => {
    app = await createApp();

    // Clean up test data
    await testDb.cleanupUsers('%@e2e-profile.com');

    // Create test user
    testUserEmail = `user-${Date.now()}@e2e-profile.com`;
    const passwordHash = await bcrypt.hash('TestPassword123!', 12);
    const user = await testDb.createTestUser(testUserEmail, passwordHash, {
      name: 'Profile Test User',
      bio: 'I am a test user for E2E testing',
      location: 'Test City, TC',
      website: 'https://example.com',
    });
    testUserId = user.id;

    // Generate tokens
    accessToken = jwt.sign(
      { userId: testUserId, email: testUserEmail },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    refreshToken = jwt.sign(
      { userId: testUserId, email: testUserEmail, type: 'refresh' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '7d' }
    );

    // Create test avatar image
    testAvatarPath = path.join(__dirname, 'test-avatar.png');
    const avatarBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    await fs.writeFile(testAvatarPath, avatarBuffer);
  });

  afterAll(async () => {
    // Clean up
    if (testAvatarPath) {
      await fs.unlink(testAvatarPath).catch(() => {});
    }
    await testDb.cleanupUsers('%@e2e-profile.com');
  });

  describe('User Profile Information', () => {
    it('should get current user profile', async () => {
      const response = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', testUserId);
      expect(response.body).toHaveProperty('email', testUserEmail);
      expect(response.body).toHaveProperty('name', 'Profile Test User');
      expect(response.body).toHaveProperty('bio', 'I am a test user for E2E testing');
      expect(response.body).toHaveProperty('location', 'Test City, TC');
      expect(response.body).toHaveProperty('website', 'https://example.com');
      expect(response.body).toHaveProperty('created_at');
      expect(response.body).not.toHaveProperty('password_hash');
    });

    it('should get extended profile with statistics', async () => {
      const response = await request(app)
        .get('/api/users/me/extended')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('profile');
      expect(response.body).toHaveProperty('statistics');
      expect(response.body.statistics).toHaveProperty('total_projects');
      expect(response.body.statistics).toHaveProperty('total_images');
      expect(response.body.statistics).toHaveProperty('total_segmentations');
      expect(response.body.statistics).toHaveProperty('storage_used_mb');
    });

    it('should update user profile', async () => {
      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'Updated Profile User',
          bio: 'Updated bio for testing',
          location: 'New Test City, NTC',
          website: 'https://updated.example.com',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('name', 'Updated Profile User');
      expect(response.body).toHaveProperty('bio', 'Updated bio for testing');
      expect(response.body).toHaveProperty('location', 'New Test City, NTC');
      expect(response.body).toHaveProperty('website', 'https://updated.example.com');
    });

    it('should update partial profile information', async () => {
      const response = await request(app)
        .patch('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          bio: 'Only bio updated',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('bio', 'Only bio updated');
      expect(response.body).toHaveProperty('name', 'Updated Profile User'); // Unchanged
    });

    it('should validate profile field lengths', async () => {
      const response = await request(app)
        .put('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          bio: 'a'.repeat(501), // Too long
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Avatar Management', () => {
    it('should upload user avatar', async () => {
      const response = await request(app)
        .post('/api/users/me/avatar')
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('avatar', testAvatarPath, 'avatar.png');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('avatar_url');
      expect(response.body.avatar_url).toContain('/avatars/');
    });

    it('should update existing avatar', async () => {
      const response = await request(app)
        .post('/api/users/me/avatar')
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('avatar', testAvatarPath, 'new-avatar.png');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('avatar_url');
    });

    it('should delete user avatar', async () => {
      const response = await request(app)
        .delete('/api/users/me/avatar')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify avatar is removed
      const profileResponse = await request(app)
        .get('/api/users/me')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(profileResponse.body.avatar_url).toBeNull();
    });

    it('should reject invalid image formats', async () => {
      const textFile = path.join(__dirname, 'test.txt');
      await fs.writeFile(textFile, 'Not an image');

      const response = await request(app)
        .post('/api/users/me/avatar')
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('avatar', textFile, 'test.txt');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');

      await fs.unlink(textFile).catch(() => {});
    });
  });

  describe('Password Management', () => {
    it('should change password with correct current password', async () => {
      const response = await request(app)
        .post('/api/users/me/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'TestPassword123!',
          newPassword: 'NewTestPassword123!',
          confirmPassword: 'NewTestPassword123!',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('accessToken'); // New token after password change
      expect(response.body).toHaveProperty('refreshToken');

      // Update token for further tests
      accessToken = response.body.accessToken;
    });

    it('should reject password change with incorrect current password', async () => {
      const response = await request(app)
        .post('/api/users/me/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'WrongPassword123!',
          newPassword: 'AnotherNewPassword123!',
          confirmPassword: 'AnotherNewPassword123!',
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject weak new password', async () => {
      const response = await request(app)
        .post('/api/users/me/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'NewTestPassword123!',
          newPassword: '123', // Too weak
          confirmPassword: '123',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject mismatched passwords', async () => {
      const response = await request(app)
        .post('/api/users/me/change-password')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          currentPassword: 'NewTestPassword123!',
          newPassword: 'AnotherNewPassword123!',
          confirmPassword: 'DifferentPassword123!',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Email Management', () => {
    it('should request email change', async () => {
      const newEmail = `newemail-${Date.now()}@e2e-profile.com`;
      const response = await request(app)
        .post('/api/users/me/change-email')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          newEmail: newEmail,
          password: 'NewTestPassword123!',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('verification');
    });

    it('should reject email change without password', async () => {
      const response = await request(app)
        .post('/api/users/me/change-email')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          newEmail: '<EMAIL>',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject email change to existing email', async () => {
      // Create another user
      await testDb.createTestUser('<EMAIL>', 'hash', {
        name: 'Existing User'
      });

      const response = await request(app)
        .post('/api/users/me/change-email')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          newEmail: '<EMAIL>',
          password: 'NewTestPassword123!',
        });

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('error');

      // Clean up
      await testDb.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
    });
  });

  describe('Application Settings', () => {
    it('should get user settings', async () => {
      const response = await request(app)
        .get('/api/users/me/settings')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('theme');
      expect(response.body).toHaveProperty('language');
      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('privacy');
    });

    it('should update theme settings', async () => {
      const response = await request(app)
        .put('/api/user-profile/settings/theme')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          theme: 'dark',
          accentColor: '#3B82F6',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('theme', 'dark');
      expect(response.body).toHaveProperty('accentColor', '#3B82F6');
    });

    it('should update language settings', async () => {
      const response = await request(app)
        .put('/api/user-profile/settings/language')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          language: 'cs',
          dateFormat: 'DD.MM.YYYY',
          timeFormat: '24h',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('language', 'cs');
      expect(response.body).toHaveProperty('dateFormat', 'DD.MM.YYYY');
      expect(response.body).toHaveProperty('timeFormat', '24h');
    });

    it('should update notification settings', async () => {
      const response = await request(app)
        .put('/api/users/me/settings/notifications')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          email: {
            projectShared: true,
            segmentationComplete: true,
            weeklyReport: false,
          },
          push: {
            projectShared: false,
            segmentationComplete: true,
          },
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('email');
      expect(response.body.email).toHaveProperty('projectShared', true);
      expect(response.body).toHaveProperty('push');
    });

    it('should update privacy settings', async () => {
      const response = await request(app)
        .put('/api/users/me/settings/privacy')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          profileVisibility: 'private',
          showEmail: false,
          allowProjectInvites: true,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('profileVisibility', 'private');
      expect(response.body).toHaveProperty('showEmail', false);
      expect(response.body).toHaveProperty('allowProjectInvites', true);
    });
  });

  describe('API Keys Management', () => {
    let apiKeyId: number;
    let apiKeyToken: string;

    it('should create API key', async () => {
      const response = await request(app)
        .post('/api/users/me/api-keys')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          name: 'E2E Test API Key',
          permissions: ['read:projects', 'write:projects', 'read:images'],
          expiresIn: '30d',
        });

      expect(response.status).toBe(201);
      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('name', 'E2E Test API Key');
      expect(response.body).toHaveProperty('key');
      expect(response.body).toHaveProperty('permissions');
      expect(response.body).toHaveProperty('expires_at');

      apiKeyId = response.body.id;
      apiKeyToken = response.body.key;
    });

    it('should list API keys', async () => {
      const response = await request(app)
        .get('/api/users/me/api-keys')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('api_keys');
      expect(Array.isArray(response.body.api_keys)).toBe(true);
      expect(response.body.api_keys.length).toBeGreaterThan(0);

      const key = response.body.api_keys.find((k: any) => k.id === apiKeyId);
      expect(key).toBeDefined();
      expect(key).not.toHaveProperty('key'); // Key should not be returned in list
    });

    it('should use API key for authentication', async () => {
      const response = await request(app).get('/api/projects').set('X-API-Key', apiKeyToken);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('projects');
    });

    it('should update API key permissions', async () => {
      const response = await request(app)
        .put(`/api/users/me/api-keys/${apiKeyId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          permissions: ['read:projects'], // Reduced permissions
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('permissions');
      expect(response.body.permissions).toEqual(['read:projects']);
    });

    it('should revoke API key', async () => {
      const response = await request(app)
        .delete(`/api/users/me/api-keys/${apiKeyId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Verify key is revoked
      const projectResponse = await request(app).get('/api/projects').set('X-API-Key', apiKeyToken);

      expect(projectResponse.status).toBe(401);
    });
  });

  describe('Activity Log', () => {
    it('should get user activity log', async () => {
      const response = await request(app)
        .get('/api/users/me/activity')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('activities');
      expect(Array.isArray(response.body.activities)).toBe(true);

      if (response.body.activities.length > 0) {
        const activity = response.body.activities[0];
        expect(activity).toHaveProperty('type');
        expect(activity).toHaveProperty('action');
        expect(activity).toHaveProperty('timestamp');
      }
    });

    it('should filter activity by type', async () => {
      const response = await request(app)
        .get('/api/users/me/activity')
        .query({ type: 'auth' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('activities');
      response.body.activities.forEach((activity: any) => {
        expect(activity.type).toBe('auth');
      });
    });

    it('should paginate activity log', async () => {
      const response = await request(app)
        .get('/api/users/me/activity')
        .query({ page: 1, limit: 5 })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('activities');
      expect(response.body.activities.length).toBeLessThanOrEqual(5);
      expect(response.body).toHaveProperty('pagination');
    });
  });

  describe('Account Deletion', () => {
    it('should request account deletion', async () => {
      // Create a user specifically for deletion
      const deleteUserEmail = `delete-${Date.now()}@e2e-profile.com`;
      const deleteUser = await testDb.createTestUser(
        deleteUserEmail, 
        await bcrypt.hash('DeleteMe123!', 12), 
        { name: 'Delete Me User' }
      );
      const deleteUserId = deleteUser.id;

      const deleteToken = jwt.sign(
        { userId: deleteUserId, email: deleteUserEmail },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .post('/api/users/me/delete-account')
        .set('Authorization', `Bearer ${deleteToken}`)
        .send({
          password: 'DeleteMe123!',
          reason: 'E2E testing account deletion',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('scheduled_deletion_date');
    });

    it('should cancel account deletion', async () => {
      // Create another user for cancellation test
      const cancelUserEmail = `cancel-${Date.now()}@e2e-profile.com`;
      const cancelUser = await testDb.createTestUser(
        cancelUserEmail,
        await bcrypt.hash('CancelMe123!', 12),
        { 
          name: 'Cancel Me User',
          deletionRequestedAt: new Date()
        }
      );
      const cancelUserId = cancelUser.id;

      const cancelToken = jwt.sign(
        { userId: cancelUserId, email: cancelUserEmail },
        config.auth.jwtSecret || 'test-secret',
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .post('/api/users/me/cancel-deletion')
        .set('Authorization', `Bearer ${cancelToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message');

      // Clean up
      await testDb.query('DELETE FROM users WHERE id = $1', [cancelUserId]);
    });

    it('should reject deletion without password', async () => {
      const response = await request(app)
        .post('/api/users/me/delete-account')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          reason: 'Missing password',
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Export User Data', () => {
    it('should export all user data', async () => {
      const response = await request(app)
        .post('/api/users/me/export-data')
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(202); // Accepted
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('export_id');
    });

    it('should check export status', async () => {
      // First request export
      const exportResponse = await request(app)
        .post('/api/users/me/export-data')
        .set('Authorization', `Bearer ${accessToken}`);

      const exportId = exportResponse.body.export_id;

      // Check status
      const statusResponse = await request(app)
        .get(`/api/users/me/export-data/${exportId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(statusResponse.status).toBe(200);
      expect(statusResponse.body).toHaveProperty('status');
      expect(['pending', 'processing', 'completed', 'failed']).toContain(
        statusResponse.body.status
      );
    });
  });
});
