/**
 * E2E Image Upload and Segmentation Tests
 * Comprehensive end-to-end tests for image management and ML segmentation workflow
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import fs from 'fs/promises';
import path from 'path';

describe('E2E Image Upload and Segmentation Flow', () => {
  let app: Express;
  let testUserId: number;
  let accessToken: string;
  let projectId: number;
  let imageId: number;
  let testImagePath: string;

  beforeAll(async () => {
    app = await createApp();

    // Clean up test data
    await testDb.query('DELETE FROM projects WHERE name LIKE $1', ['%E2E Image Test%']);
    await testDb.cleanupUsers('%@e2e-image.com');

    // Create test user
    const testUserEmail = `user-${Date.now()}@e2e-image.com`;
    const user = await testDb.createTestUser(testUserEmail, 'hashed', {
      name: 'Image Test User'
    });
    testUserId = user.id;

    // Generate token
    accessToken = jwt.sign(
      { userId: testUserId, email: testUserEmail },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Create test project
    const project = await testDb.createTestProject(testUserId.toString(), {
      name: 'E2E Image Test Project',
      description: 'Project for image testing'
    });
    projectId = project.id;

    // Create test image file
    testImagePath = path.join(__dirname, 'test-image.jpg');
    const imageBuffer = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    await fs.writeFile(testImagePath, imageBuffer);
  });

  afterAll(async () => {
    // Clean up
    if (testImagePath) {
      await fs.unlink(testImagePath).catch(() => {});
    }
    await testDb.query('DELETE FROM projects WHERE name LIKE $1', ['%E2E Image Test%']);
    await testDb.cleanupUsers('%@e2e-image.com');
  });

  describe('Image Upload', () => {
    it('should upload single image to project', async () => {
      const response = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('projectId', projectId.toString())
        .attach('images', testImagePath, 'test-image.jpg');

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');
      expect(Array.isArray(response.body.images)).toBe(true);
      expect(response.body.images).toHaveLength(1);

      const uploadedImage = response.body.images[0];
      expect(uploadedImage).toHaveProperty('id');
      expect(uploadedImage).toHaveProperty('filename', 'test-image.jpg');
      expect(uploadedImage).toHaveProperty('project_id', projectId);
      expect(uploadedImage).toHaveProperty('status', 'pending');
      expect(uploadedImage).toHaveProperty('url');
      expect(uploadedImage).toHaveProperty('thumbnail_url');
      expect(uploadedImage).toHaveProperty('size');
      expect(uploadedImage).toHaveProperty('mime_type', 'image/jpeg');

      imageId = uploadedImage.id;
    });

    it('should upload multiple images', async () => {
      // Create additional test images
      const testImage2Path = path.join(__dirname, 'test-image2.jpg');
      const testImage3Path = path.join(__dirname, 'test-image3.jpg');

      await fs.writeFile(testImage2Path, await fs.readFile(testImagePath));
      await fs.writeFile(testImage3Path, await fs.readFile(testImagePath));

      const response = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('projectId', projectId.toString())
        .attach('images', testImage2Path, 'test-image2.jpg')
        .attach('images', testImage3Path, 'test-image3.jpg');

      expect(response.status).toBe(200);
      expect(response.body.images).toHaveLength(2);

      // Clean up
      await fs.unlink(testImage2Path).catch(() => {});
      await fs.unlink(testImage3Path).catch(() => {});
    });

    it('should reject upload without project ID', async () => {
      const response = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('images', testImagePath, 'test-image.jpg');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');
    });

    it('should reject unsupported file types', async () => {
      const textFilePath = path.join(__dirname, 'test.txt');
      await fs.writeFile(textFilePath, 'This is not an image');

      const response = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('projectId', projectId.toString())
        .attach('images', textFilePath, 'test.txt');

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('error');

      await fs.unlink(textFilePath).catch(() => {});
    });

    it('should enforce file size limits', async () => {
      // Mock a large file by setting content-length header
      const response = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .set('Content-Length', '52428800') // 50MB + 1
        .field('projectId', projectId.toString())
        .attach('images', testImagePath, 'large-test.jpg');

      expect(response.status).toBe(413);
    });
  });

  describe('Image Management', () => {
    it('should list project images', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');
      expect(Array.isArray(response.body.images)).toBe(true);
      expect(response.body.images.length).toBeGreaterThanOrEqual(1);

      const image = response.body.images.find((img: any) => img.id === imageId);
      expect(image).toBeDefined();
      expect(image).toHaveProperty('segmentation_status');
    });

    it('should get single image details', async () => {
      const response = await request(app)
        .get(`/api/projects/images/${imageId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('id', imageId);
      expect(response.body).toHaveProperty('filename');
      expect(response.body).toHaveProperty('metadata');
      expect(response.body).toHaveProperty('segmentation');
    });

    it('should update image metadata', async () => {
      const response = await request(app)
        .patch(`/api/projects/images/${imageId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          metadata: {
            description: 'Test image for E2E testing',
            tags: ['test', 'e2e'],
          },
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('metadata');
      expect(response.body.metadata).toHaveProperty('description', 'Test image for E2E testing');
      expect(response.body.metadata).toHaveProperty('tags');
      expect(response.body.metadata.tags).toContain('test');
    });

    it('should delete image', async () => {
      // Upload a new image to delete
      const uploadResponse = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('projectId', projectId.toString())
        .attach('images', testImagePath, 'delete-test.jpg');

      const deleteImageId = uploadResponse.body.images[0].id;

      const deleteResponse = await request(app)
        .delete(`/api/projects/images/${deleteImageId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.body).toHaveProperty('message');

      // Verify deletion
      const getResponse = await request(app)
        .get(`/api/projects/images/${deleteImageId}`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(getResponse.status).toBe(404);
    });

    it('should bulk delete images', async () => {
      // Upload multiple images
      const uploadResponse = await request(app)
        .post('/api/projects/images/upload')
        .set('Authorization', `Bearer ${accessToken}`)
        .field('projectId', projectId.toString())
        .attach('images', testImagePath, 'bulk-delete-1.jpg')
        .attach('images', testImagePath, 'bulk-delete-2.jpg');

      const imageIds = uploadResponse.body.images.map((img: any) => img.id);

      const deleteResponse = await request(app)
        .delete('/api/projects/images/bulk')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageIds: imageIds,
        });

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.body).toHaveProperty('deleted', imageIds.length);
    });
  });

  describe('Segmentation Workflow', () => {
    it('should trigger segmentation for image', async () => {
      const response = await request(app)
        .post(`/api/projects/images/${imageId}/segment`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
          parameters: {
            threshold: 0.5,
            min_area: 100,
          },
        });

      expect(response.status).toBe(202); // Accepted
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('task_id');
      expect(response.body).toHaveProperty('status', 'queued');
    });

    it('should get segmentation status', async () => {
      // Wait a bit for segmentation to start
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const response = await request(app)
        .get(`/api/projects/images/${imageId}/segmentation/status`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status');
      expect(['queued', 'processing', 'completed', 'failed']).toContain(response.body.status);
      expect(response.body).toHaveProperty('progress');

      if (response.body.status === 'completed') {
        expect(response.body).toHaveProperty('result');
      }
    });

    it('should batch segment multiple images', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/segment-all`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          model: 'resunet',
          filter: {
            status: 'pending',
          },
        });

      expect(response.status).toBe(202);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('batch_id');
      expect(response.body).toHaveProperty('image_count');
    });

    it('should get segmentation results', async () => {
      // Simulate completed segmentation
      await pool.query(
        `UPDATE images SET 
          segmentation_status = $1,
          segmentation_data = $2,
          segmented_at = NOW()
        WHERE id = $3`,
        [
          'completed',
          JSON.stringify({
            polygons: [
              {
                points: [
                  [0, 0],
                  [10, 0],
                  [10, 10],
                  [0, 10],
                ],
                label: 'cell',
              },
            ],
            confidence: 0.95,
            model: 'resunet',
          }),
          imageId,
        ]
      );

      const response = await request(app)
        .get(`/api/projects/images/${imageId}/segmentation`)
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('polygons');
      expect(Array.isArray(response.body.polygons)).toBe(true);
      expect(response.body).toHaveProperty('confidence');
      expect(response.body).toHaveProperty('model');
    });

    it('should update segmentation manually', async () => {
      const response = await request(app)
        .put(`/api/projects/images/${imageId}/segmentation`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          polygons: [
            {
              points: [
                [0, 0],
                [20, 0],
                [20, 20],
                [0, 20],
              ],
              label: 'cell',
            },
            {
              points: [
                [30, 30],
                [40, 30],
                [40, 40],
                [30, 40],
              ],
              label: 'cell',
            },
          ],
          edited_by: 'manual',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('polygons');
      expect(response.body.polygons).toHaveLength(2);
      expect(response.body).toHaveProperty('edited_by', 'manual');
    });

    it('should export segmentation in different formats', async () => {
      // COCO format
      const cocoResponse = await request(app)
        .get(`/api/projects/images/${imageId}/segmentation/export`)
        .query({ format: 'coco' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(cocoResponse.status).toBe(200);
      expect(cocoResponse.body).toHaveProperty('annotations');

      // CSV format
      const csvResponse = await request(app)
        .get(`/api/projects/images/${imageId}/segmentation/export`)
        .query({ format: 'csv' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(csvResponse.status).toBe(200);
      expect(csvResponse.headers['content-type']).toContain('text/csv');
    });
  });

  describe('Image Processing', () => {
    it('should generate image thumbnail', async () => {
      const response = await request(app)
        .post(`/api/projects/images/${imageId}/thumbnail`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          width: 200,
          height: 200,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('thumbnail_url');
    });

    it('should rotate image', async () => {
      const response = await request(app)
        .post(`/api/projects/images/${imageId}/rotate`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          angle: 90,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('url');
      expect(response.body).toHaveProperty('metadata');
      expect(response.body.metadata).toHaveProperty('rotation', 90);
    });

    it('should crop image', async () => {
      const response = await request(app)
        .post(`/api/projects/images/${imageId}/crop`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          x: 0,
          y: 0,
          width: 100,
          height: 100,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('url');
      expect(response.body).toHaveProperty('metadata');
      expect(response.body.metadata).toHaveProperty('cropped', true);
    });
  });

  describe('Batch Operations', () => {
    it('should move images between projects', async () => {
      // Create target project
      const targetProjectResult = await pool.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['E2E Target Project', testUserId]
      );
      const targetProjectId = targetProjectResult.rows[0].id;

      const response = await request(app)
        .post('/api/projects/images/move')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageIds: [imageId],
          targetProjectId: targetProjectId,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('moved', 1);

      // Move back
      await request(app)
        .post('/api/projects/images/move')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageIds: [imageId],
          targetProjectId: projectId,
        });

      // Clean up
      await pool.query('DELETE FROM projects WHERE id = $1', [targetProjectId]);
    });

    it('should copy images between projects', async () => {
      // Create target project
      const targetProjectResult = await pool.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['E2E Copy Target Project', testUserId]
      );
      const targetProjectId = targetProjectResult.rows[0].id;

      const response = await request(app)
        .post('/api/projects/images/copy')
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          imageIds: [imageId],
          targetProjectId: targetProjectId,
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('copied', 1);
      expect(response.body).toHaveProperty('newImages');
      expect(response.body.newImages).toHaveLength(1);

      // Clean up
      await pool.query('DELETE FROM images WHERE project_id = $1', [targetProjectId]);
      await pool.query('DELETE FROM projects WHERE id = $1', [targetProjectId]);
    });

    it('should export multiple images as dataset', async () => {
      const response = await request(app)
        .post(`/api/projects/${projectId}/export-dataset`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send({
          format: 'yolo',
          includeSegmentations: true,
          splitRatio: {
            train: 0.8,
            val: 0.1,
            test: 0.1,
          },
        });

      expect(response.status).toBe(200);
      expect(response.headers['content-type']).toContain('application/zip');
      expect(response.headers['content-disposition']).toContain('dataset');
    });
  });

  describe('Image Search and Filtering', () => {
    it('should search images by filename', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/images/search`)
        .query({ q: 'test' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');
      expect(response.body.images.length).toBeGreaterThan(0);
    });

    it('should filter images by status', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .query({ status: 'completed' })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');
      // All returned images should have completed status
      response.body.images.forEach((img: any) => {
        expect(img.segmentation_status).toBe('completed');
      });
    });

    it('should filter images by date range', async () => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);

      const response = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .query({
          uploaded_after: yesterday.toISOString(),
          uploaded_before: tomorrow.toISOString(),
        })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');
      expect(response.body.images.length).toBeGreaterThan(0);
    });

    it('should sort images by different criteria', async () => {
      const response = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .query({
          sort: 'size',
          order: 'desc',
        })
        .set('Authorization', `Bearer ${accessToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('images');

      // Verify sorting
      if (response.body.images.length > 1) {
        const sizes = response.body.images.map((img: any) => img.size);
        expect(sizes).toEqual([...sizes].sort((a, b) => b - a));
      }
    });
  });
});
