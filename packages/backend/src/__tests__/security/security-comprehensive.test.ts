/**
 * Comprehensive Security Tests
 * Tests for various security vulnerabilities and attack vectors
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import path from 'path';
import fs from 'fs/promises';

describe('Comprehensive Security Tests', () => {
  let app: Express;
  let validToken: string;
  let testUserId: number;

  beforeAll(async () => {
    app = await createApp();

    // Create test user
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'Security Test User']
    );
    testUserId = userResult.rows[0].id;

    validToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    await testDb.query('DELETE FROM users WHERE email = $1', ['<EMAIL>']);
  });

  describe('SQL Injection Prevention', () => {
    it('should prevent SQL injection in login', async () => {
      const maliciousPayloads = [
        { email: "admin' OR '1'='1", password: 'password' },
        { email: '<EMAIL>', password: "' OR '1'='1" },
        { email: "admin'; DROP TABLE users; --", password: 'password' },
        { email: '<EMAIL>', password: "'; DELETE FROM users; --" },
      ];

      for (const payload of maliciousPayloads) {
        const response = await request(app).post('/api/auth/login').send(payload);

        expect(response.status).toBe(401); // Should fail authentication
        expect(response.body.error).toBe('Invalid credentials');
      }

      // Verify users table still exists
      const tableCheck = await testDb.query(
        "SELECT EXISTS (SELECT FROM pg_tables WHERE tablename = 'users')"
      );
      expect(tableCheck.rows[0].exists).toBe(true);
    });

    it('should prevent SQL injection in search queries', async () => {
      const maliciousSearches = [
        "'; DROP TABLE projects; --",
        "' UNION SELECT * FROM users --",
        "' OR 1=1 --",
        "'; UPDATE users SET password_hash='hacked' --",
      ];

      for (const search of maliciousSearches) {
        const response = await request(app)
          .get('/api/projects')
          .query({ search })
          .set('Authorization', `Bearer ${validToken}`);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('projects');
      }

      // Verify no damage was done
      const userCheck = await testDb.query('SELECT password_hash FROM users WHERE id = $1', [
        testUserId,
      ]);
      expect(userCheck.rows[0].password_hash).toBe('hashed');
    });
  });

  describe('XSS (Cross-Site Scripting) Prevention', () => {
    it('should sanitize user input in project creation', async () => {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        '<svg onload=alert("XSS")>',
        'javascript:alert("XSS")',
        '<iframe src="javascript:alert(\'XSS\')"></iframe>',
      ];

      for (const payload of xssPayloads) {
        const response = await request(app)
          .post('/api/projects')
          .set('Authorization', `Bearer ${validToken}`)
          .send({
            name: payload,
            description: payload,
          });

        expect(response.status).toBe(201);
        const project = response.body.project;

        // Should not contain script tags
        expect(project.name).not.toContain('<script>');
        expect(project.name).not.toContain('onerror=');
        expect(project.name).not.toContain('javascript:');
        expect(project.description).not.toContain('<script>');
      }
    });

    it('should set proper Content-Type headers', async () => {
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.headers['content-type']).toContain('application/json');
      expect(response.headers['x-content-type-options']).toBe('nosniff');
    });
  });

  describe('CSRF (Cross-Site Request Forgery) Protection', () => {
    it('should validate CSRF tokens for state-changing operations', async () => {
      // Get CSRF token
      const csrfResponse = await request(app).get('/api/auth/csrf-token');
      const _csrfToken = csrfResponse.body.csrfToken;

      // Try to create project without CSRF token
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .set('Origin', 'http://malicious-site.com')
        .send({
          name: 'CSRF Test Project',
        });

      // Should either succeed (if CSRF not enforced) or fail with proper error
      expect([201, 403]).toContain(response.status);
    });

    it('should validate referrer for sensitive operations', async () => {
      const response = await request(app)
        .delete('/api/projects/1')
        .set('Authorization', `Bearer ${validToken}`)
        .set('Referer', 'http://malicious-site.com');

      // Should validate referrer for delete operations
      expect([200, 403, 404]).toContain(response.status);
    });
  });

  describe('Authentication Security', () => {
    it('should not leak information about valid emails', async () => {
      const validEmailResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'wrong-password',
      });

      const invalidEmailResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'wrong-password',
      });

      // Both should return same error to prevent email enumeration
      expect(validEmailResponse.status).toBe(401);
      expect(invalidEmailResponse.status).toBe(401);
      expect(validEmailResponse.body.error).toBe(invalidEmailResponse.body.error);
    });

    it('should enforce strong password requirements', async () => {
      const weakPasswords = ['123456', 'password', 'qwerty', 'abc123', 'password123', 'test', ''];

      for (const password of weakPasswords) {
        const response = await request(app)
          .post('/api/auth/register')
          .send({
            email: `weak-${Date.now()}@test.com`,
            password,
          });

        expect(response.status).toBe(400);
        expect(response.body.error).toContain('password');
      }
    });

    it('should prevent JWT token tampering', async () => {
      // Decode the valid token
      const decoded = jwt.decode(validToken) as any;

      // Create tampered tokens
      const tamperedTokens = [
        // Changed user ID
        jwt.sign({ ...decoded, userId: 999999 }, 'wrong-secret', { expiresIn: '1h' }),
        // Missing required fields
        jwt.sign({ email: decoded.email }, config.auth.jwtSecret || 'test-secret', {
          expiresIn: '1h',
        }),
        // Invalid signature
        validToken.slice(0, -10) + 'tamperedXX',
      ];

      for (const token of tamperedTokens) {
        const response = await request(app)
          .get('/api/users/me')
          .set('Authorization', `Bearer ${token}`);

        expect(response.status).toBe(401);
      }
    });
  });

  describe('Authorization and Access Control', () => {
    it('should prevent horizontal privilege escalation', async () => {
      // Create another user and their project
      const user = await testDb.createTestUser('<EMAIL>', 'hashed', 'Other User'.shift(), '<EMAIL>', 'hashed', 'Other User'.shift(), { name: '<EMAIL>', 'hashed', 'Other User'.shift() });
    const otherUserId = user.id;

      const projectResult = await testDb.query(
        'INSERT INTO projects (name, user_id) VALUES ($1, $2) RETURNING id',
        ['Other User Project', otherUserId]
      );
      const otherProjectId = projectResult.rows[0].id;

      // Try to access other user's project
      const accessResponse = await request(app)
        .get(`/api/projects/${otherProjectId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(accessResponse.status).toBe(403);

      // Try to modify other user's project
      const modifyResponse = await request(app)
        .put(`/api/projects/${otherProjectId}`)
        .set('Authorization', `Bearer ${validToken}`)
        .send({ name: 'Hacked!' });

      expect(modifyResponse.status).toBe(403);

      // Try to delete other user's project
      const deleteResponse = await request(app)
        .delete(`/api/projects/${otherProjectId}`)
        .set('Authorization', `Bearer ${validToken}`);

      expect(deleteResponse.status).toBe(403);

      // Clean up
      await testDb.query('DELETE FROM projects WHERE id = $1', [otherProjectId]);
      await testDb.query('DELETE FROM users WHERE id = $1', [otherUserId]);
    });

    it('should prevent vertical privilege escalation', async () => {
      // Try to access admin-only endpoints
      const adminEndpoints = [
        { method: 'GET', path: '/api/admin/users' },
        { method: 'GET', path: '/api/admin/system/logs' },
        { method: 'POST', path: '/api/admin/system/restart' },
        { method: 'DELETE', path: '/api/admin/users/1' },
      ];

      for (const endpoint of adminEndpoints) {
        const response = await request(app)
          [endpoint.method.toLowerCase()](endpoint.path)
          .set('Authorization', `Bearer ${validToken}`);

        expect([403, 404]).toContain(response.status);
      }
    });
  });

  describe('File Upload Security', () => {
    it('should prevent malicious file uploads', async () => {
      const maliciousFiles = [
        { name: 'exploit.php', content: '<?php system($_GET["cmd"]); ?>' },
        { name: 'shell.aspx', content: '<%@ Page Language="C#" %>' },
        { name: 'virus.exe', content: Buffer.from([0x4d, 0x5a]) }, // PE header
        { name: '../../../etc/passwd', content: 'root:x:0:0' },
      ];

      for (const file of maliciousFiles) {
        const filePath = path.join(__dirname, file.name.replace(/\//g, '_'));
        await fs.writeFile(filePath, file.content);

        const response = await request(app)
          .post('/api/projects/1/images')
          .set('Authorization', `Bearer ${validToken}`)
          .attach('images', filePath, file.name);

        expect(response.status).toBe(400); // Should reject non-image files
        expect(response.body.error).toContain('file type');

        await fs.unlink(filePath).catch(() => {});
      }
    });

    it('should sanitize uploaded filenames', async () => {
      const dangerousFilenames = [
        '../../../etc/passwd.png',
        'image.png.php',
        'image.png%00.txt',
        'image\x00.png',
        '../../uploads/exploit.png',
      ];

      // Create a valid PNG
      const pngBuffer = Buffer.from(
        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
        'base64'
      );

      for (const filename of dangerousFilenames) {
        const safeName = filename.replace(/[^a-zA-Z0-9._-]/g, '_');
        const filePath = path.join(__dirname, safeName);
        await fs.writeFile(filePath, pngBuffer);

        const response = await request(app)
          .post('/api/projects/1/images')
          .set('Authorization', `Bearer ${validToken}`)
          .attach('images', filePath, filename);

        if (response.status === 201) {
          const uploadedFilename = response.body.images[0].filename;
          // Should not contain path traversal sequences
          expect(uploadedFilename).not.toContain('..');
          expect(uploadedFilename).not.toContain('/');
          expect(uploadedFilename).not.toContain('\\');
        }

        await fs.unlink(filePath).catch(() => {});
      }
    });
  });

  describe('Rate Limiting and DDoS Protection', () => {
    it('should enforce rate limits on authentication endpoints', async () => {
      const attempts = 15;
      const responses = [];

      for (let i = 0; i < attempts; i++) {
        const response = await request(app).post('/api/auth/login').send({
          email: '<EMAIL>',
          password: 'wrong-password',
        });
        responses.push(response.status);
      }

      // Should start rate limiting after a certain number of attempts
      const rateLimited = responses.filter((status) => status === 429);
      expect(rateLimited.length).toBeGreaterThan(0);
    });

    it('should implement exponential backoff for repeated failures', async () => {
      const email = `backoff-${Date.now()}@test.com`;
      const delays = [];

      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        await request(app).post('/api/auth/login').send({ email, password: 'wrong' });
        const delay = Date.now() - startTime;
        delays.push(delay);
      }

      // Later attempts should take longer (exponential backoff)
      expect(delays[4]).toBeGreaterThan(delays[0]);
    });
  });

  describe('Information Disclosure Prevention', () => {
    it('should not expose sensitive information in error messages', async () => {
      const response = await request(app)
        .get('/api/this-endpoint-does-not-exist')
        .set('Authorization', `Bearer ${validToken}`);

      expect(response.status).toBe(404);
      // Should not expose internal paths or stack traces
      expect(JSON.stringify(response.body)).not.toContain('/home/');
      expect(JSON.stringify(response.body)).not.toContain('node_modules');
      expect(JSON.stringify(response.body)).not.toContain('stack');
    });

    it('should not expose database structure', async () => {
      const response = await request(app)
        .post('/api/projects')
        .set('Authorization', `Bearer ${validToken}`)
        .send({
          name: 'Test',
          invalid_field: 'value', // Non-existent field
        });

      // Should not expose column names or table structure
      expect(JSON.stringify(response.body)).not.toContain('column');
      expect(JSON.stringify(response.body)).not.toContain('table');
      expect(JSON.stringify(response.body)).not.toContain('projects');
    });
  });

  describe('Session Security', () => {
    it('should implement secure session management', async () => {
      // Login to create session
      const loginResponse = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password',
      });

      const cookies = loginResponse.headers['set-cookie'];
      if (cookies) {
        // Session cookies should have secure flags
        expect(cookies.some((c: string) => c.includes('HttpOnly'))).toBe(true);
        expect(cookies.some((c: string) => c.includes('SameSite'))).toBe(true);
      }
    });

    it('should regenerate session ID on privilege changes', async () => {
      // This would require session-based auth implementation
      // For now, verify JWT tokens are rotated
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'test-refresh-token' });

      if (response.status === 200) {
        expect(response.body).toHaveProperty('accessToken');
        expect(response.body.accessToken).not.toBe(validToken);
      }
    });
  });

  describe('API Security Headers', () => {
    it('should set security headers on all responses', async () => {
      const response = await request(app).get('/api/health');

      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('0'); // Modern browsers prefer CSP
      expect(response.headers['strict-transport-security']).toBeDefined();
    });

    it('should implement Content Security Policy', async () => {
      const response = await request(app).get('/api/health');

      const csp = response.headers['content-security-policy'];
      if (csp) {
        expect(csp).toContain("default-src 'self'");
        expect(csp).not.toContain('unsafe-inline');
        expect(csp).not.toContain('unsafe-eval');
      }
    });
  });
});
