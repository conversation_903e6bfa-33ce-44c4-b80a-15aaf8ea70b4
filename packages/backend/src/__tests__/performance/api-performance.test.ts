/**
 * API Performance Tests
 * Tests to ensure API endpoints meet performance requirements
 */

import request from 'supertest';
import { Express } from 'express';
import { createApp } from '../../app';
import testDb from '../helpers/testDb';
import jwt from 'jsonwebtoken';
import config from '../../config';
import path from 'path';
import fs from 'fs/promises';

describe('API Performance Tests', () => {
  let app: Express;
  let accessToken: string;
  let testUserId: number;
  const projectIds: number[] = [];
  const imageIds: number[] = [];

  beforeAll(async () => {
    app = await createApp();

    // Create test user
    const userResult = await testDb.query(
      'INSERT INTO users (email, password_hash, name) VALUES ($1, $2, $3) RETURNING id',
      ['<EMAIL>', 'hashed', 'Performance Test User']
    );
    testUserId = userResult.rows[0].id;

    accessToken = jwt.sign(
      { userId: testUserId, email: '<EMAIL>' },
      config.auth.jwtSecret || 'test-secret',
      { expiresIn: '1h' }
    );

    // Create test data for performance testing
    // Create 50 projects
    for (let i = 0; i < 50; i++) {
      const result = await testDb.query(
        'INSERT INTO projects (name, description, user_id) VALUES ($1, $2, $3) RETURNING id',
        [`Performance Test Project ${i}`, `Description for project ${i}`, testUserId]
      );
      projectIds.push(result.rows[0].id);
    }

    // Create 200 images across projects
    for (let i = 0; i < 200; i++) {
      const projectId = projectIds[Math.floor(Math.random() * projectIds.length)];
      const result = await testDb.query(
        'INSERT INTO images (filename, project_id, size, width, height) VALUES ($1, $2, $3, $4, $5) RETURNING id',
        [`test-image-${i}.png`, projectId, 1024 * (i + 1), 800, 600]
      );
      imageIds.push(result.rows[0].id);
    }
  });

  afterAll(async () => {
    // Clean up test data
    await testDb.query('DELETE FROM images WHERE project_id = ANY($1)', [projectIds]);
    await testDb.query('DELETE FROM projects WHERE user_id = $1', [testUserId]);
    await testDb.query('DELETE FROM users WHERE id = $1', [testUserId]);
  });

  describe('Response Time Requirements', () => {
    it('should respond to health check within 100ms', async () => {
      const startTime = Date.now();
      const response = await request(app).get('/api/health');
      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(100);
    });

    it('should authenticate user within 200ms', async () => {
      const startTime = Date.now();
      const _response = await request(app).post('/api/auth/login').send({
        email: '<EMAIL>',
        password: 'password', // Will fail but tests response time
      });
      const responseTime = Date.now() - startTime;

      expect(responseTime).toBeLessThan(200);
    });

    it('should list projects within 300ms', async () => {
      const startTime = Date.now();
      const response = await request(app)
        .get('/api/projects')
        .set('Authorization', `Bearer ${accessToken}`);
      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.body.projects).toHaveLength(50);
      expect(responseTime).toBeLessThan(300);
    });

    it('should get project details within 150ms', async () => {
      const projectId = projectIds[0];
      const startTime = Date.now();
      const response = await request(app)
        .get(`/api/projects/${projectId}`)
        .set('Authorization', `Bearer ${accessToken}`);
      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(150);
    });

    it('should list images with pagination within 250ms', async () => {
      const projectId = projectIds[0];
      const startTime = Date.now();
      const response = await request(app)
        .get(`/api/projects/${projectId}/images`)
        .query({ page: 1, limit: 20 })
        .set('Authorization', `Bearer ${accessToken}`);
      const responseTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(250);
    });
  });

  describe('Concurrent Request Handling', () => {
    it('should handle 20 concurrent project list requests', async () => {
      const startTime = Date.now();
      const promises = Array(20)
        .fill(null)
        .map(() => request(app).get('/api/projects').set('Authorization', `Bearer ${accessToken}`));

      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });

      // Total time for 20 concurrent requests should be reasonable
      expect(totalTime).toBeLessThan(2000); // 2 seconds for all
    });

    it('should handle mixed concurrent requests', async () => {
      const startTime = Date.now();
      const promises = [
        // 5 project lists
        ...Array(5)
          .fill(null)
          .map(() =>
            request(app).get('/api/projects').set('Authorization', `Bearer ${accessToken}`)
          ),
        // 5 project details
        ...Array(5)
          .fill(null)
          .map((_, i) =>
            request(app)
              .get(`/api/projects/${projectIds[i]}`)
              .set('Authorization', `Bearer ${accessToken}`)
          ),
        // 5 user profile requests
        ...Array(5)
          .fill(null)
          .map(() =>
            request(app).get('/api/users/me').set('Authorization', `Bearer ${accessToken}`)
          ),
      ];

      const responses = await Promise.all(promises);
      const totalTime = Date.now() - startTime;

      // All requests should succeed
      responses.forEach((response) => {
        expect(response.status).toBe(200);
      });

      expect(totalTime).toBeLessThan(3000); // 3 seconds for all
    });
  });

  describe('Large Data Set Performance', () => {
    it('should efficiently paginate through large image sets', async () => {
      const pageTimes: number[] = [];
      const pageSize = 20;
      const totalPages = 5;

      for (let page = 1; page <= totalPages; page++) {
        const startTime = Date.now();
        const response = await request(app)
          .get(`/api/projects/${projectIds[0]}/images`)
          .query({ page, limit: pageSize })
          .set('Authorization', `Bearer ${accessToken}`);
        const pageTime = Date.now() - startTime;

        expect(response.status).toBe(200);
        pageTimes.push(pageTime);
      }

      // Each page should load quickly
      pageTimes.forEach((time) => {
        expect(time).toBeLessThan(300);
      });

      // Average page load time
      const avgTime = pageTimes.reduce((a, b) => a + b) / pageTimes.length;
      expect(avgTime).toBeLessThan(250);
    });

    it('should handle search queries efficiently', async () => {
      const searchQueries = ['Performance', 'Test', 'Project', 'Description', 'NonExistent'];

      const searchTimes: number[] = [];

      for (const query of searchQueries) {
        const startTime = Date.now();
        const response = await request(app)
          .get('/api/projects')
          .query({ search: query })
          .set('Authorization', `Bearer ${accessToken}`);
        const searchTime = Date.now() - startTime;

        expect(response.status).toBe(200);
        searchTimes.push(searchTime);
      }

      // Search should be fast even with many projects
      searchTimes.forEach((time) => {
        expect(time).toBeLessThan(400);
      });
    });
  });

  describe('Memory and Resource Usage', () => {
    it('should not leak memory during repeated requests', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      const iterations = 100;

      for (let i = 0; i < iterations; i++) {
        await request(app).get('/api/projects').set('Authorization', `Bearer ${accessToken}`);
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 50MB for 100 requests)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
    });

    it('should handle file uploads without excessive memory usage', async () => {
      // Create a 5MB test image
      const imageSize = 5 * 1024 * 1024;
      const imageBuffer = Buffer.alloc(imageSize);
      const imagePath = path.join(__dirname, 'perf-test-image.png');
      await fs.writeFile(imagePath, imageBuffer);

      const initialMemory = process.memoryUsage().heapUsed;

      // Upload the image
      const response = await request(app)
        .post(`/api/projects/${projectIds[0]}/images`)
        .set('Authorization', `Bearer ${accessToken}`)
        .attach('images', imagePath, 'perf-test.png');

      expect(response.status).toBe(201);

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (less than 3x file size)
      expect(memoryIncrease).toBeLessThan(imageSize * 3);

      // Clean up
      await fs.unlink(imagePath).catch(() => {});
    });
  });

  describe('Database Query Performance', () => {
    it('should use efficient queries for complex operations', async () => {
      // Test complex query with joins and aggregations
      const startTime = Date.now();
      const response = await request(app)
        .get('/api/users/me/statistics')
        .set('Authorization', `Bearer ${accessToken}`);
      const queryTime = Date.now() - startTime;

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('total_projects', 50);
      expect(response.body).toHaveProperty('total_images');
      expect(queryTime).toBeLessThan(500); // Complex aggregation should still be fast
    });

    it('should handle database connection pooling efficiently', async () => {
      const connectionTimes: number[] = [];
      const requests = 50;

      for (let i = 0; i < requests; i++) {
        const startTime = Date.now();
        const response = await request(app).get('/api/health').query({ details: true });
        const connectionTime = Date.now() - startTime;

        expect(response.status).toBe(200);
        connectionTimes.push(connectionTime);
      }

      // Connection pooling should keep times consistent
      const avgTime = connectionTimes.reduce((a, b) => a + b) / connectionTimes.length;
      const variance =
        connectionTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) /
        connectionTimes.length;
      const stdDev = Math.sqrt(variance);

      // Standard deviation should be low (consistent performance)
      expect(stdDev).toBeLessThan(50);
    });
  });

  describe('Caching Performance', () => {
    it('should cache frequently accessed data', async () => {
      const projectId = projectIds[0];
      const uncachedTimes: number[] = [];
      const cachedTimes: number[] = [];

      // First 5 requests (likely uncached)
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        await request(app)
          .get(`/api/projects/${projectId}`)
          .set('Authorization', `Bearer ${accessToken}`);
        uncachedTimes.push(Date.now() - startTime);
      }

      // Next 5 requests (should be cached)
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now();
        await request(app)
          .get(`/api/projects/${projectId}`)
          .set('Authorization', `Bearer ${accessToken}`);
        cachedTimes.push(Date.now() - startTime);
      }

      const avgUncached = uncachedTimes.reduce((a, b) => a + b) / uncachedTimes.length;
      const avgCached = cachedTimes.reduce((a, b) => a + b) / cachedTimes.length;

      // Cached requests should be faster (at least 20% improvement)
      expect(avgCached).toBeLessThan(avgUncached * 0.8);
    });
  });

  describe('Load Testing', () => {
    it('should maintain performance under sustained load', async () => {
      const duration = 5000; // 5 seconds
      const startTime = Date.now();
      const responses: number[] = [];
      let requestCount = 0;

      while (Date.now() - startTime < duration) {
        const reqStart = Date.now();
        const response = await request(app)
          .get('/api/projects')
          .set('Authorization', `Bearer ${accessToken}`);
        const reqTime = Date.now() - reqStart;

        expect(response.status).toBe(200);
        responses.push(reqTime);
        requestCount++;
      }

      // Calculate performance metrics
      const avgResponseTime = responses.reduce((a, b) => a + b) / responses.length;
      const maxResponseTime = Math.max(...responses);
      const minResponseTime = Math.min(...responses);

      console.log(`Load test results:
        Requests: ${requestCount}
        Avg response time: ${avgResponseTime.toFixed(2)}ms
        Min response time: ${minResponseTime}ms
        Max response time: ${maxResponseTime}ms
        Requests per second: ${(requestCount / (duration / 1000)).toFixed(2)}
      `);

      // Performance requirements
      expect(avgResponseTime).toBeLessThan(400);
      expect(maxResponseTime).toBeLessThan(1000);
      expect(requestCount).toBeGreaterThan(50); // At least 10 requests per second
    });
  });
});
