/**
 * Express App Factory
 *
 * Creates and configures the Express application with all middleware
 * Extracted from the monolithic server for better modularity
 */

import express, { Request, Response, NextFunction } from 'express';
import compression from 'compression';
import helmet from 'helmet';
import cors from 'cors';
import rateLimit from 'express-rate-limit';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import connectRedis from 'connect-redis';
import { createClient } from 'redis';
import morgan from 'morgan';
import { Pool } from 'pg';
import { registerRoutes } from '../routes/index';
import { getProductionConfig } from '../config/production';
import { initializeDatabase } from '../db';
import logger from '../utils/logger';

/**
 * Create and configure the Express application
 */
export async function createProductionApp(): Promise<express.Application> {
  const app = express();
  const config = getProductionConfig();

  logger.info({ message: '🚀 Creating SpheroSeg Production App' });
  logger.info(`Environment: ${config.nodeEnv}`);
  logger.info(`CORS Origin: ${config.corsOrigin}`);

  // Trust proxy for production (behind reverse proxy/load balancer)
  if (config.enableTrust) {
    app.set('trust proxy', 1);
    logger.info({ message: '✅ Proxy trust enabled' });
  }

  // ============================================================================
  // SECURITY MIDDLEWARE
  // ============================================================================
  app.use(
    helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
          fontSrc: ["'self'", 'https://fonts.gstatic.com'],
          imgSrc: ["'self'", 'data:', 'blob:', 'https:'],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", 'https://api.spherosegapp.utia.cas.cz'],
        },
      },
      hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true,
      },
      crossOriginEmbedderPolicy: false, // Allows image processing
    })
  );

  // ============================================================================
  // COMPRESSION MIDDLEWARE
  // ============================================================================
  app.use(
    compression({
      level: config.compressionLevel,
      threshold: 1024, // Only compress files larger than 1KB
      filter: (req, res) => {
        // Don't compress images or already compressed files
        if (req.headers['content-type']?.includes('image/')) return false;
        return compression.filter(req, res);
      },
    })
  );

  // ============================================================================
  // CORS CONFIGURATION
  // ============================================================================
  if (config.nodeEnv === 'development') {
    // Allow all origins in development
    app.use(
      cors({
        origin: true,
        credentials: true,
        optionsSuccessStatus: 200,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'X-Requested-With',
          'X-Request-ID',
          'X-CSRF-Token',
          'If-None-Match',
          'Cache-Control',
          'Pragma',
        ],
      })
    );
  } else {
    app.use(
      cors({
        origin: config.corsOrigin,
        credentials: true,
        optionsSuccessStatus: 200,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'X-Requested-With',
          'X-Request-ID',
          'X-CSRF-Token',
          'If-None-Match',
          'Cache-Control',
          'Pragma',
        ],
      })
    );
  }

  // ============================================================================
  // RATE LIMITING
  // ============================================================================
  const limiter = rateLimit({
    windowMs: config.rateLimitWindow,
    max: config.rateLimitRequests,
    message: {
      error: 'Too many requests, please try again later.',
      retryAfter: Math.ceil(config.rateLimitWindow / 1000),
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/api/health' || req.path === '/health';
    },
  });
  app.use('/api/', limiter);

  // ============================================================================
  // BODY PARSING MIDDLEWARE
  // ============================================================================
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());

  // ============================================================================
  // LOGGING MIDDLEWARE
  // ============================================================================
  app.use(
    morgan('combined', {
      stream: {
        write: (message: string) => {
          logger.info(message.trim());
        },
      },
      skip: (req) => {
        // Skip logging for health checks to reduce noise
        return req.path === '/api/health' || req.path === '/health';
      },
    })
  );

  // ============================================================================
  // DATABASE INITIALIZATION
  // ============================================================================
  await initializeDatabase();

  // ============================================================================
  // SESSION MANAGEMENT
  // ============================================================================
  try {
    const RedisStore = (connectRedis as any)(session);
    const redisClient = createClient({ url: config.redisUrl });
    await redisClient.connect();

    app.use(
      session({
        store: new RedisStore({
          client: redisClient,
          prefix: 'spheroseg:sess:',
        }),
        secret: config.sessionSecret,
        resave: false,
        saveUninitialized: false,
        name: 'spheroseg.sid',
        cookie: {
          secure: config.nodeEnv === 'production', // HTTPS in production
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
          sameSite: 'strict',
        },
      })
    );

    logger.info({ message: '✅ Redis session store connected' });
  } catch (error) {
    logger.error('❌ Redis connection failed:', error);
    logger.warn('⚠️ Continuing without Redis session store');

    // Fallback to memory store
    app.use(
      session({
        secret: config.sessionSecret,
        resave: false,
        saveUninitialized: false,
        name: 'spheroseg.sid',
        cookie: {
          secure: config.nodeEnv === 'production',
          httpOnly: true,
          maxAge: 24 * 60 * 60 * 1000,
          sameSite: 'strict',
        },
      })
    );
  }

  // ============================================================================
  // ROUTES
  // ============================================================================
  registerRoutes({ app });

  // ============================================================================
  // ERROR HANDLING MIDDLEWARE
  // ============================================================================
  app.use((err: Error, req: Request, res: Response, _next: NextFunction) => {
    logger.error('Unhandled error:', { error: err.message, stack: err.stack });
    res.status(500).json({
      error: 'Internal server error',
      message: config.nodeEnv === 'development' ? err.message : undefined,
    });
  });

  return app;
}

/**
 * Get the database pool from the app instance
 */
export function getDbPool(app: express.Application): Pool {
  return app.get('db') as Pool;
}
