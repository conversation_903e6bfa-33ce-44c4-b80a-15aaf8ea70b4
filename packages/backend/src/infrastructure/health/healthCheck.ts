/**
 * Health Check Service
 * Comprehensive health monitoring for all system components
 */

import { PrismaClient } from '../../../generated/prisma';
import * as redis from 'redis';
import * as amqp from 'amqplib';
import axios from 'axios';
import * as fs from 'fs/promises';
import * as os from 'os';
import { appLogger } from '../logging/logger';

export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
}

export interface HealthCheckResult {
  status: HealthStatus;
  message?: string;
  duration?: number;
  metadata?: any;
}

export interface SystemHealth {
  status: HealthStatus;
  timestamp: string;
  uptime: number;
  version: string;
  checks: {
    database: HealthCheckResult;
    redis: HealthCheckResult;
    rabbitmq: HealthCheckResult;
    storage: HealthCheckResult;
    mlService: HealthCheckResult;
    memory: HealthCheckResult;
    cpu: HealthCheckResult;
  };
}

export class HealthCheckService {
  private prisma: PrismaClient;
  private redisClient: redis.RedisClientType;
  private rabbitmqUrl: string;
  private mlServiceUrl: string;
  private storagePath: string;
  private version: string;

  constructor(
    prisma: PrismaClient,
    redisClient: redis.RedisClientType,
    rabbitmqUrl: string = 'amqp://rabbitmq:5672',
    mlServiceUrl: string = 'http://ml:5002',
    storagePath: string = '/app/uploads',
    version: string = process.env.APP_VERSION || '1.0.0'
  ) {
    this.prisma = prisma;
    this.redisClient = redisClient;
    this.rabbitmqUrl = rabbitmqUrl;
    this.mlServiceUrl = mlServiceUrl;
    this.storagePath = storagePath;
    this.version = version;
  }

  async checkHealth(): Promise<SystemHealth> {
    const startTime = Date.now();

    // Run all health checks in parallel
    const [database, redisCheck, rabbitmq, storage, mlService, memory, cpu] = await Promise.all([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkRabbitMQ(),
      this.checkStorage(),
      this.checkMLService(),
      this.checkMemory(),
      this.checkCPU(),
    ]);

    // Determine overall status
    const checks = { database, redis: redisCheck, rabbitmq, storage, mlService, memory, cpu };
    const statuses = Object.values(checks).map((check) => check.status);

    let overallStatus = HealthStatus.HEALTHY;
    if (statuses.includes(HealthStatus.UNHEALTHY)) {
      overallStatus = HealthStatus.UNHEALTHY;
    } else if (statuses.includes(HealthStatus.DEGRADED)) {
      overallStatus = HealthStatus.DEGRADED;
    }

    const result: SystemHealth = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      version: this.version,
      checks,
    };

    // Log health check result
    appLogger.logEvent('health_check', {
      status: overallStatus,
      duration: Date.now() - startTime,
      checks: Object.entries(checks).reduce((acc, [key, value]) => {
        acc[key] = value.status;
        return acc;
      }, {} as any),
    });

    return result;
  }

  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // Execute a simple query to check database connectivity
      await this.prisma.$queryRaw`SELECT 1`;

      // Check connection pool stats (metrics not available in production builds)
      // const metrics = await this.prisma.$metrics.json();

      return {
        status: HealthStatus.HEALTHY,
        duration: Date.now() - startTime,
        metadata: {
          // Metrics not available in production builds
        },
      };
    } catch (error) {
      appLogger.error('Database health check failed', error as Error);
      return {
        status: HealthStatus.UNHEALTHY,
        message: 'Database connection failed',
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkRedis(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // Ping Redis
      const pong = await this.redisClient.ping();

      if (pong !== 'PONG') {
        return {
          status: HealthStatus.DEGRADED,
          message: 'Redis ping response unexpected',
          duration: Date.now() - startTime,
        };
      }

      // Get Redis info
      const info = await this.redisClient.info();

      return {
        status: HealthStatus.HEALTHY,
        duration: Date.now() - startTime,
        metadata: {
          connected: true,
          info: info.split('\n').slice(0, 5).join('\n'), // First 5 lines of info
        },
      };
    } catch (error) {
      appLogger.error('Redis health check failed', error as Error);
      return {
        status: HealthStatus.UNHEALTHY,
        message: 'Redis connection failed',
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkRabbitMQ(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // Try to connect to RabbitMQ
      const connection = await amqp.connect(this.rabbitmqUrl);
      const channel = await connection.createChannel();

      // Check if we can declare a test queue
      await channel.checkQueue('health_check_test');

      await channel.close();
      await connection.close();

      return {
        status: HealthStatus.HEALTHY,
        duration: Date.now() - startTime,
      };
    } catch (error) {
      appLogger.error('RabbitMQ health check failed', error as Error);
      return {
        status: HealthStatus.UNHEALTHY,
        message: 'RabbitMQ connection failed',
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkStorage(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // Check if storage directory exists and is writable
      const stats = await fs.stat(this.storagePath);

      if (!stats.isDirectory()) {
        return {
          status: HealthStatus.UNHEALTHY,
          message: 'Storage path is not a directory',
          duration: Date.now() - startTime,
        };
      }

      // Try to write and delete a test file
      const testFile = `${this.storagePath}/.health_check_${Date.now()}`;
      await fs.writeFile(testFile, 'test');
      await fs.unlink(testFile);

      // Check available disk space
      const { available, total } = await this.getDiskSpace();
      const usagePercent = ((total - available) / total) * 100;

      if (usagePercent > 90) {
        return {
          status: HealthStatus.UNHEALTHY,
          message: 'Disk space critical (>90% used)',
          duration: Date.now() - startTime,
          metadata: { usagePercent },
        };
      }

      if (usagePercent > 75) {
        return {
          status: HealthStatus.DEGRADED,
          message: 'Disk space warning (>75% used)',
          duration: Date.now() - startTime,
          metadata: { usagePercent },
        };
      }

      return {
        status: HealthStatus.HEALTHY,
        duration: Date.now() - startTime,
        metadata: {
          usagePercent,
          available: `${(available / 1024 / 1024 / 1024).toFixed(2)} GB`,
          total: `${(total / 1024 / 1024 / 1024).toFixed(2)} GB`,
        },
      };
    } catch (error) {
      appLogger.error('Storage health check failed', error as Error);
      return {
        status: HealthStatus.UNHEALTHY,
        message: 'Storage check failed',
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkMLService(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    try {
      // Check ML service health endpoint
      const response = await axios.get(`${this.mlServiceUrl}/health`, {
        timeout: 5000,
      });

      if (response.status !== 200) {
        return {
          status: HealthStatus.DEGRADED,
          message: `ML service returned status ${response.status}`,
          duration: Date.now() - startTime,
        };
      }

      return {
        status: HealthStatus.HEALTHY,
        duration: Date.now() - startTime,
        metadata: response.data,
      };
    } catch (error) {
      appLogger.error('ML service health check failed', error as Error);
      return {
        status: HealthStatus.UNHEALTHY,
        message: 'ML service unreachable',
        duration: Date.now() - startTime,
      };
    }
  }

  private async checkMemory(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usagePercent = (usedMemory / totalMemory) * 100;

    // Node.js process memory
    const processMemory = process.memoryUsage();
    const processMemoryMB = {
      rss: Math.round(processMemory.rss / 1024 / 1024),
      heapTotal: Math.round(processMemory.heapTotal / 1024 / 1024),
      heapUsed: Math.round(processMemory.heapUsed / 1024 / 1024),
      external: Math.round(processMemory.external / 1024 / 1024),
    };

    let status = HealthStatus.HEALTHY;
    let message: string | undefined;

    if (usagePercent > 90) {
      status = HealthStatus.UNHEALTHY;
      message = 'Memory usage critical (>90%)';
    } else if (usagePercent > 75) {
      status = HealthStatus.DEGRADED;
      message = 'Memory usage warning (>75%)';
    }

    return {
      status,
      message,
      duration: Date.now() - startTime,
      metadata: {
        system: {
          usagePercent: usagePercent.toFixed(2),
          free: `${(freeMemory / 1024 / 1024 / 1024).toFixed(2)} GB`,
          total: `${(totalMemory / 1024 / 1024 / 1024).toFixed(2)} GB`,
        },
        process: processMemoryMB,
      },
    };
  }

  private async checkCPU(): Promise<HealthCheckResult> {
    const startTime = Date.now();

    const cpus = os.cpus();
    const loadAvg = os.loadavg();

    // Calculate CPU usage
    const avgLoad = loadAvg[0]; // 1-minute load average
    const cpuCount = cpus.length;
    const loadPercentage = (avgLoad / cpuCount) * 100;

    let status = HealthStatus.HEALTHY;
    let message: string | undefined;

    if (loadPercentage > 90) {
      status = HealthStatus.UNHEALTHY;
      message = 'CPU load critical (>90%)';
    } else if (loadPercentage > 70) {
      status = HealthStatus.DEGRADED;
      message = 'CPU load warning (>70%)';
    }

    return {
      status,
      message,
      duration: Date.now() - startTime,
      metadata: {
        cores: cpuCount,
        loadAverage: {
          '1m': loadAvg[0].toFixed(2),
          '5m': loadAvg[1].toFixed(2),
          '15m': loadAvg[2].toFixed(2),
        },
        loadPercentage: loadPercentage.toFixed(2),
      },
    };
  }

  private async getDiskSpace(): Promise<{ available: number; total: number }> {
    // This is a simplified implementation
    // In production, you might want to use a library like 'diskusage'
    try {
      const stats = await fs.statfs(this.storagePath);
      return {
        available: stats.bavail * stats.bsize,
        total: stats.blocks * stats.bsize,
      };
    } catch {
      // Fallback values if statfs is not available
      return {
        available: 1024 * 1024 * 1024, // 1GB
        total: 10 * 1024 * 1024 * 1024, // 10GB
      };
    }
  }
}
