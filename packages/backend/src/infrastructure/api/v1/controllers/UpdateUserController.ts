import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '../../../../../generated/prisma';
import logger from '../../../../utils/logger';

const prisma = new PrismaClient();

export class UpdateUserController {
  constructor(private userRepository?: any) {}
  async handle(req: Request, res: Response, next: NextFunction): Promise<void> {
    return this.update(req, res, next);
  }

  async update(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const { name, email, role } = req.body;

      const user = await prisma.user.update({
        where: { id },
        data: {
          ...(name && { name }),
          ...(email && { email }),
          ...(role && { role }),
        },
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          updatedAt: true,
        },
      });

      res.json({ success: true, data: user });
    } catch (error) {
      logger.error('Error updating user:', error);
      next(error);
    }
  }
}

export default new UpdateUserController();
