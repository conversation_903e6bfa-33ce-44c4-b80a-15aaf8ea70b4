import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '../../../../../generated/prisma';
import logger from '../../../../utils/logger';

const prisma = new PrismaClient();

export class GetUserController {
  constructor(private userRepository?: any) {}
  async handle(req: Request, res: Response, next: NextFunction): Promise<void> {
    return this.getById(req, res, next);
  }

  async getById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        res.status(404).json({ error: 'User not found' });
        return;
      }

      res.json({ success: true, data: user });
    } catch (error) {
      logger.error('Error fetching user:', error);
      next(error);
    }
  }
}

export default new GetUserController();

export class GetAllUsersController extends GetUserController {
  async getAll(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          username: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      res.json({ success: true, data: users });
    } catch (error) {
      logger.error({ message: 'Error fetching all users', error });
      next(error);
    }
  }
}
