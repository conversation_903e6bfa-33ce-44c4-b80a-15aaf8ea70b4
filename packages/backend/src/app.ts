/**
 * Express Application Configuration
 *
 * This module configures the Express application with all middleware and routes.
 * Separated from server.ts for better testing and modularity.
 */

import express, { Application } from 'express';
import { configureMiddleware, configureErrorMiddleware } from './middleware';
import { setupConsolidatedRoutes } from './routes/index.consolidated';
import config from './config';
import logger from './utils/logger';
import { performHealthCheck } from './utils/healthCheck';
import { metricsHandler, prometheusMiddleware } from './monitoring/prometheus';
import { performanceMonitor, getPerformanceInsights, getCurrentSystemMetrics } from './middleware/performance';

/**
 * Create and configure Express application
 */
export const createApp = (): Application => {
  const app = express();

  // Enable trust proxy for proper IP detection behind nginx/docker
  app.set('trust proxy', true);

  // CRITICAL: Add body parser FIRST before any other middleware
  // This ensures the body is parsed before security checks
  app.use(
    express.json({
      limit: '10mb',
      verify: (req: express.Request, _res: express.Response, buf: Buffer) => {
        // Store raw body for debugging
        (req as unknown as Request & { rawBody?: Buffer }).rawBody = buf;
      },
    })
  );
  app.use(
    express.urlencoded({
      extended: true,
      limit: '10mb',
    })
  );

  // Configure all middleware in the correct order
  configureMiddleware(app);

  // Add Prometheus metrics middleware
  app.use(prometheusMiddleware);

  // Performance monitoring is now handled by the consolidated middleware in configureMiddleware()
  // No additional middleware needed here as the consolidated performance middleware 
  // handles all performance tracking, metrics, and insights

  // Health check endpoint (before API routes)
  app.get('/health', async (_req, res) => {
    try {
      const health = await performHealthCheck();
      const statusCode =
        health.overall === 'healthy' ? 200 : health.overall === 'degraded' ? 200 : 503;

      res.status(statusCode).json({
        success: health.overall !== 'unhealthy',
        ...health,
      });
    } catch (error) {
      logger.error('Health check failed', {
        error: error instanceof Error ? error.message : String(error),
      });
      res.status(503).json({
        success: false,
        overall: 'unhealthy',
        error: 'Health check failed',
        timestamp: new Date().toISOString(),
      });
    }
  });

  // Metrics endpoint for Prometheus
  app.get('/api/metrics', metricsHandler);

  // Performance insights and metrics endpoints
  app.get('/api/performance', (_req, res) => {
    const insights = getPerformanceInsights();
    const systemMetrics = getCurrentSystemMetrics();
    
    res.json({
      success: true,
      data: {
        insights,
        systemMetrics,
        timestamp: new Date().toISOString(),
      }
    });
  });

  // API routes - Using consolidated routes
  setupConsolidatedRoutes(app);

  // Configure error handling middleware (must be after routes)
  configureErrorMiddleware(app);

  logger.info(
    { message: 'Express application configured successfully' },
    {
      environment: config.env,
      corsOrigins: config.server.corsOrigins,
    }
  );

  return app;
};

// Export configured app instance
export default createApp();
