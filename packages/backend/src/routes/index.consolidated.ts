/**
 * Consolidated Route Registry
 * Manages all application routes in a single setup function
 */

import { Application } from 'express';
import { registerRoutes } from './index';
// Database module imported via prisma
import logger from '../utils/logger';

/**
 * Setup all consolidated routes for the application
 */
export async function setupConsolidatedRoutes(app: Application | any): Promise<void> {
  try {
    logger.info({ message: 'Setting up consolidated routes...' });

    // Register all routes (pool no longer needed with Prisma)
    registerRoutes({ app });

    logger.info({ message: '✅ All routes successfully registered' });
  } catch (error) {
    logger.error('Failed to setup consolidated routes:', error);
    throw error;
  }
}

/**
 * Export for backward compatibility
 */
export { registerRoutes } from './index';
