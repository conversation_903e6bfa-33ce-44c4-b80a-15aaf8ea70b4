/**
 * Image Routes Module
 * Image upload, processing, and segmentation endpoints
 */

import { Router, Request, Response } from 'express';
import multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import * as crypto from 'crypto';
import { createLogger } from '../../utils/logger';
import { prisma } from '../../services/PrismaService';
import { authenticate as authenticateToken } from '../../security/middleware/auth';
import { validateCsrfToken } from '../../middleware/csrfProtection';

const logger = createLogger('ImageRoutes');
const router = Router();

interface AuthenticatedRequest extends Request {
  userId?: string;
}

// Configure multer for image uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${Date.now()}-${crypto.randomBytes(8).toString('hex')}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  },
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|tiff|bmp/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  },
});

export function createImageRoutes(): Router {
  // Upload image to project
  router.post(
    '/upload/:projectId',
    authenticateToken,
    upload.single('image'),
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { projectId } = req.params;
        const file = req.file;

        if (!file) {
          return res.status(400).json({
            success: false,
            error: 'No image file provided',
          });
        }

        logger.info({ message: 'Image upload:' }, { projectId, userId, filename: file.filename });

        // Verify project ownership
        const project = await prisma.project.findFirst({
          where: {
            id: projectId,
            userId: userId,
          },
        });

        if (!project) {
          // Delete uploaded file
          fs.unlinkSync(file.path);
          return res.status(404).json({
            success: false,
            error: 'Project not found',
          });
        }

        // Get image dimensions (in production, use sharp or similar)
        const dimensions = { width: 1920, height: 1080 }; // Mock dimensions

        // Use Prisma transaction to create image and update project count
        const result = await prisma.$transaction(async (tx) => {
          // Save image to database
          const newImage = await tx.image.create({
            data: {
              id: crypto.randomUUID(),
              projectId,
              storageFilename: file.filename,
              storagePath: file.path,
              originalFilename: file.originalname,
              fileSize: file.size,
              width: dimensions.width,
              height: dimensions.height,
              segmentationStatus: 'pending',
            },
            select: {
              id: true,
              storageFilename: true,
              originalFilename: true,
              fileSize: true,
              width: true,
              height: true,
              uploadDate: true,
              segmentationStatus: true,
            },
          });

          // Note: imageCount field doesn't exist in current schema
          // Could add it later if needed

          return newImage;
        });

        logger.info({ message: 'Image saved successfully:', imageId: result.id });

        res.status(201).json({
          success: true,
          image: {
            id: result.id,
            filename: result.storageFilename,
            original_filename: result.originalFilename,
            file_size: result.fileSize,
            width: result.width,
            height: result.height,
            upload_date: result.uploadDate,
            segmentation_status: result.segmentationStatus,
          },
        });
      } catch (error) {
        logger.error('Image upload error:', error);
        if (req.file) {
          fs.unlinkSync(req.file.path);
        }
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get image details
  router.get('/:imageId', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
    try {
      const userId = req.userId;
      const { imageId } = req.params;

      logger.info({ message: 'GET /api/images/:imageId' }, { imageId, userId });

      const image = await prisma.image.findFirst({
        where: {
          id: imageId,
          project: { userId: userId },
        },
        include: {
          project: {
            select: { userId: true },
          },
        },
      });

      if (!image) {
        return res.status(404).json({
          success: false,
          error: 'Image not found',
        });
      }

      res.json({
        success: true,
        image: {
          id: image.id,
          project_id: image.projectId,
          filename: image.storageFilename,
          original_filename: image.originalFilename,
          file_size: image.fileSize,
          width: image.width,
          height: image.height,
          upload_date: image.uploadDate,
          segmentation_status: image.segmentationStatus,
          segmentation_result: null, // Use segmentationResults relationship
          segmentation_mask_path: null, // Field not in schema
          thumbnail_path: image.thumbnailPath,
          user_id: image.project.userId,
        },
      });
    } catch (error) {
      logger.error('Get image error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error',
      });
    }
  });

  // Get image thumbnail
  router.get(
    '/:imageId/thumbnail',
    authenticateToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { imageId } = req.params;

        logger.info({ message: 'GET /api/images/:imageId/thumbnail' }, { imageId, userId });

        // Verify ownership and get image info
        const image = await prisma.image.findFirst({
          where: {
            id: imageId,
            project: { userId: userId },
          },
          select: {
            storageFilename: true,
            thumbnailPath: true,
          },
        });

        if (!image) {
          return res.status(404).json({
            success: false,
            error: 'Image not found',
          });
        }
        const imagePath = path.join(
          __dirname,
          '../../../uploads',
          image.thumbnailPath || image.storageFilename
        );

        if (fs.existsSync(imagePath)) {
          res.sendFile(imagePath);
        } else {
          res.status(404).json({
            success: false,
            error: 'Thumbnail not found',
          });
        }
      } catch (error) {
        logger.error('Get thumbnail error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Start segmentation
  router.post(
    '/:imageId/segment',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { imageId } = req.params;
        const { model = 'default' } = req.body;

        logger.info({ message: 'POST /api/images/:imageId/segment' }, { imageId, userId, model });

        // Verify ownership and get image info
        const image = await prisma.image.findFirst({
          where: {
            id: imageId,
            project: { userId: userId },
          },
        });

        if (!image) {
          return res.status(404).json({
            success: false,
            error: 'Image not found',
          });
        }

        if (image.segmentationStatus === 'processing') {
          return res.status(400).json({
            success: false,
            error: 'Segmentation already in progress',
          });
        }

        // Update status to processing
        await prisma.image.update({
          where: { id: imageId },
          data: { segmentationStatus: 'processing' },
        });

        // In production, this would trigger ML service
        // For now, we'll simulate it
        setTimeout(async () => {
          await prisma.image.update({
            where: { id: imageId },
            data: {
              segmentationStatus: 'completed',
              // Store results in segmentationResults table
            },
          });
          logger.info({ message: 'Segmentation completed:', imageId });
        }, 5000);

        res.json({
          success: true,
          message: 'Segmentation started',
          status: 'processing',
        });
      } catch (error) {
        logger.error('Start segmentation error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Get segmentation result
  router.get(
    '/:imageId/segmentation',
    authenticateToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { imageId } = req.params;

        logger.info({ message: 'GET /api/images/:imageId/segmentation' }, { imageId, userId });

        const image = await prisma.image.findFirst({
          where: {
            id: imageId,
            project: { userId: userId },
          },
          include: {
            segmentationResults: true,
          },
        });

        if (!image) {
          return res.status(404).json({
            success: false,
            error: 'Image not found',
          });
        }

        if (image.segmentationStatus !== 'completed') {
          return res.json({
            success: true,
            status: image.segmentationStatus,
            result: null,
          });
        }

        res.json({
          success: true,
          status: 'completed',
          result: image.segmentationResults,
          maskPath: null, // Field not in schema
        });
      } catch (error) {
        logger.error('Get segmentation error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  // Delete image
  router.delete(
    '/:imageId',
    authenticateToken,
    validateCsrfToken,
    async (req: AuthenticatedRequest, res: Response) => {
      try {
        const userId = req.userId;
        const { imageId } = req.params;

        logger.info({ message: 'DELETE /api/images/:imageId' }, { imageId, userId });

        // Get image details with project info
        const image = await prisma.image.findFirst({
          where: {
            id: imageId,
            project: { userId: userId },
          },
          include: {
            project: {
              select: { id: true, userId: true },
            },
          },
        });

        if (!image) {
          return res.status(404).json({
            success: false,
            error: 'Image not found',
          });
        }

        // Delete file from filesystem
        const imagePath = path.join(__dirname, '../../../uploads', image.storageFilename);
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }

        // Delete image from database
        await prisma.image.delete({ where: { id: imageId } });

        logger.info({ message: 'Image deleted successfully:', imageId });

        res.json({
          success: true,
          message: 'Image deleted successfully',
        });
      } catch (error) {
        logger.error('Delete image error:', error);
        res.status(500).json({
          success: false,
          error: 'Internal server error',
        });
      }
    }
  );

  return router;
}
