import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';

export function createSegmentationRoutes(): Router {
  const router = Router();

  // Get segmentation status
  router.get('/status/:imageId', authenticate, async (req, res) => {
    try {
      const { imageId } = req.params;

      // Query segmentation status from database
      const segmentation = await prisma.segmentation.findFirst({
        where: { imageId },
        select: { status: true, polygons: true },
      });

      if (!segmentation) {
        return res.status(404).json({ error: 'Segmentation not found' });
      }

      res.json({
        success: true,
        data: segmentation,
      });
    } catch (error) {
      logger.error('Error fetching segmentation status:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Start segmentation task
  router.post('/start', authenticate, async (req, res) => {
    try {
      const { imageId } = req.body;

      // TODO: Add segmentation task to queue

      res.json({
        success: true,
        message: 'Segmentation started',
        taskId: `task_${Date.now()}`,
      });
    } catch (error) {
      logger.error('Error starting segmentation:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
}

export default createSegmentationRoutes;
