import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';
import bcrypt from 'bcryptjs';

export function createUserProfileRoutes(): Router {
  const router = Router();

  // Get user profile
  router.get('/', authenticate, async (req, res) => {
    try {
      const userId = req.user!.userId;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          username: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      res.json({
        success: true,
        data: {
          id: user.id,
          email: user.email,
          name: user.username,
          avatar: null, // Field not in schema
          created_at: user.createdAt,
          updated_at: user.updatedAt,
        },
      });
    } catch (error) {
      logger.error('Error fetching user profile:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Update user profile
  router.put('/', authenticate, async (req, res) => {
    try {
      const userId = req.user!.userId;
      const { name, avatar } = req.body;

      // Build update data dynamically
      const updateData: any = {};
      if (name !== undefined) updateData.name = name;
      if (avatar !== undefined) updateData.avatar = avatar;

      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: updateData,
        select: {
          id: true,
          email: true,
          username: true,
          updatedAt: true,
        },
      });

      res.json({
        success: true,
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          name: updatedUser.username,
          avatar: null,
          updated_at: updatedUser.updatedAt,
        },
      });
    } catch (error) {
      logger.error('Error updating user profile:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Change password
  router.post('/change-password', authenticate, async (req, res) => {
    try {
      const userId = req.user!.userId;
      const { currentPassword, newPassword } = req.body;

      // Verify current password
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { passwordHash: true },
      });

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      const isValidPassword = await bcrypt.compare(currentPassword, user.passwordHash);

      if (!isValidPassword) {
        return res.status(400).json({ error: 'Current password is incorrect' });
      }

      // Update password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      await prisma.user.update({
        where: { id: userId },
        data: { passwordHash: hashedPassword },
      });

      res.json({
        success: true,
        message: 'Password changed successfully',
      });
    } catch (error) {
      logger.error('Error changing password:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
}

export default createUserProfileRoutes;
