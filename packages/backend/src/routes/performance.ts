import { Router } from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';

export function createPerformanceRoutes(): Router {
  const router = Router();

  // Get performance metrics
  router.get('/metrics', authenticate, authorize('ADMIN'), async (req, res) => {
    try {
      // Get database metrics using raw query (Prisma doesn't have direct access to pg_stat_activity)
      const dbMetrics = (await prisma.$queryRaw`
        SELECT 
          count(*)::int as total_connections,
          count(*) FILTER (WHERE state = 'active')::int as active_connections,
          count(*) FILTER (WHERE state = 'idle')::int as idle_connections
        FROM pg_stat_activity
        WHERE datname = current_database()
      `) as any[];

      res.json({
        success: true,
        data: {
          database: dbMetrics[0],
          memory: process.memoryUsage(),
          uptime: process.uptime(),
          timestamp: new Date().toISOString(),
        },
      });
    } catch (error) {
      logger.error('Error fetching performance metrics:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Get query performance
  router.get('/queries', authenticate, authorize('ADMIN'), async (req, res) => {
    try {
      try {
        const slowQueries = (await prisma.$queryRaw`
          SELECT 
            query,
            calls,
            total_time,
            mean_time,
            max_time
          FROM pg_stat_statements
          ORDER BY mean_time DESC
          LIMIT 10
        `) as any[];

        res.json({
          success: true,
          data: {
            slowQueries,
          },
        });
      } catch (dbError) {
        // pg_stat_statements might not be enabled
        logger.warn('pg_stat_statements not available:', dbError);
        res.json({
          success: true,
          data: {
            slowQueries: [],
            note: 'pg_stat_statements extension not available',
          },
        });
      }
    } catch (error) {
      logger.error('Error fetching query performance:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
}

export default createPerformanceRoutes;
