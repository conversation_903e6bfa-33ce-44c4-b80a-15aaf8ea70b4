import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';

export function createUserStatsRoutes(): Router {
  const router = Router();

  // Get user statistics
  router.get('/', authenticate, async (req, res) => {
    try {
      const userId = req.user!.userId;

      // Get user statistics using Prisma
      const [totalProjects, totalImages, sharedProjects] = await Promise.all([
        prisma.project.count({ where: { userId } }),
        prisma.image.count({ where: { userId } }),
        prisma.projectShare.count({ where: { userId } }),
      ]);

      res.json({
        success: true,
        data: {
          total_projects: totalProjects,
          total_images: totalImages,
          shared_projects: sharedProjects,
        },
      });
    } catch (error) {
      logger.error('Error fetching user stats:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  // Get user activity
  router.get('/activity', authenticate, async (req, res) => {
    try {
      const userId = req.user!.userId;
      const { limit = 10 } = req.query;

      const projects = await prisma.project.findMany({
        where: { userId },
        select: {
          createdAt: true,
          title: true,
        },
        orderBy: { createdAt: 'desc' },
        take: Number(limit),
      });

      // Convert to activity format
      const activities = projects.map((project) => ({
        type: 'project_created',
        timestamp: project.createdAt,
        details: project.title,
      }));

      res.json({
        success: true,
        data: {
          activities,
        },
      });
    } catch (error) {
      logger.error('Error fetching user activity:', { error });
      res.status(500).json({ error: 'Internal server error' });
    }
  });

  return router;
}

export default createUserStatsRoutes;
