/**
 * Auth Service - Migrated to Prisma
 * Handles user authentication, registration, and password management
 */

import bcryptjs from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { prisma } from '../services/PrismaService';
import logger from '../utils/logger';
import { ApiError } from '../utils/errors';
import { ErrorCode } from '../utils/ApiError';
import tokenService from './tokenService';
import { sendNewPasswordEmail, sendVerificationEmail } from './email/CentralizedEmailService';
import config from '../config';
import { cacheService, CACHE_TTL } from './cacheService';

// Response type interfaces
interface RegisterResponse {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: Date;
  };
  accessToken: string;
  refreshToken: string;
  tokenType: string;
}

interface LoginResponse {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: Date;
    updated_at: Date;
    [key: string]: unknown;
  };
  accessToken: string;
  refreshToken: string;
  tokenType: string;
}

interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
}

interface UserResponse {
  id: string;
  email: string;
  name: string;
  created_at: Date;
  updated_at: Date;
  [key: string]: unknown;
}

class AuthService {
  /**
   * Registers a new user
   */
  public async registerUser(
    email: string,
    password: string,
    name?: string,
    preferred_language?: string
  ): Promise<RegisterResponse> {
    logger.info({ message: 'Processing user registration request' }, { email });

    try {
      // Check if email already exists
      const existingUser = await prisma.user.findUnique({
        where: { email },
      });

      if (existingUser) {
        logger.warn({ message: 'Registration attempt with existing email', email });
        throw new ApiError('Email already in use', 409, ErrorCode.RESOURCE_CONFLICT);
      }

      // Hash password
      const saltRounds = config.auth.saltRounds;
      const passwordHash = await bcryptjs.hash(password, saltRounds);

      // Create user with profile in a transaction
      const result = await prisma.$transaction(async (tx) => {
        // Create user
        const newUser = await tx.user.create({
          data: {
            id: uuidv4(),
            email,
            passwordHash,
            username: name || email.split('@')[0],
          },
        });

        // Create user profile
        await tx.userProfile.create({
          data: {
            id: uuidv4(),
            userId: newUser.id,
            preferredLanguage: preferred_language || 'en',
          },
        });

        return newUser;
      });

      // Generate tokens
      const tokenResponse = await tokenService.createTokenResponse(result.id, result.email);
      const { accessToken, refreshToken } = tokenResponse;

      // Clear any cached data for this email
      await cacheService.del(`user:email:${email}`);

      logger.info({ message: 'User registered successfully' }, { userId: result.id, email });

      return {
        user: {
          id: result.id,
          email: result.email,
          name: result.username || '',
          created_at: result.createdAt,
        },
        accessToken,
        refreshToken,
        tokenType: 'Bearer',
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error({ message: 'Registration failed', error });
      throw new ApiError('Registration failed', 500, ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Logs in a user
   */
  public async loginUser(email: string, password: string): Promise<LoginResponse> {
    logger.info({ message: 'Processing login request' }, { email });

    try {
      // Find user with profile
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          profile: true,
        },
      });

      if (!user) {
        logger.warn({ message: 'Login attempt with non-existent email', email });
        throw new ApiError('Invalid email or password', 401, ErrorCode.UNAUTHORIZED);
      }

      // Verify password
      const isPasswordValid = await bcryptjs.compare(password, user.passwordHash || '');
      if (!isPasswordValid) {
        logger.warn({ message: 'Login attempt with incorrect password', email });
        throw new ApiError('Invalid email or password', 401, ErrorCode.UNAUTHORIZED);
      }

      // Check if account is active
      if (!user.isActive) {
        logger.warn({ message: 'Login attempt with inactive account', email });
        throw new ApiError(
          'Account is not active. Please contact an administrator.',
          403,
          ErrorCode.FORBIDDEN
        );
      }

      // Update last login - field doesn't exist, skip for now
      // await prisma.user.update({
      //   where: { id: user.id },
      //   data: { lastLogin: new Date() },
      // });

      // Generate tokens
      const tokenResponse = await tokenService.createTokenResponse(user.id, user.email);
      const { accessToken, refreshToken } = tokenResponse;

      // Cache user data
      await cacheService.set(`user:${user.id}`, user, CACHE_TTL.USER);

      logger.info({ message: 'User logged in successfully' }, { userId: user.id });

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.username || '',
          created_at: user.createdAt,
          updated_at: user.updatedAt,
          ...(user.profile && {
            username: user.profile.username,
            full_name: user.profile.fullName,
            organization: user.profile.organization,
            bio: user.profile.bio,
            location: user.profile.location,
            avatar_url: user.profile.avatarUrl,
            preferred_language: user.profile.preferredLanguage,
            theme_preference: user.profile.themePreference,
          }),
        },
        accessToken,
        refreshToken,
        tokenType: 'Bearer',
      };
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error({ message: 'Login failed', error });
      throw new ApiError('Login failed', 500, ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Refreshes authentication tokens
   */
  public async refreshAuthTokens(refreshToken: string): Promise<RefreshTokenResponse> {
    logger.info({ message: 'Processing token refresh request' });

    try {
      // Verify and refresh tokens
      // First verify the token to get user info
      const payload = await tokenService.verifyRefreshToken(refreshToken);
      if (!payload) {
        throw new ApiError('Invalid refresh token', 401, ErrorCode.UNAUTHORIZED);
      }
      
      // Create new token response
      const newTokens = await tokenService.createTokenResponse(payload.userId, payload.email);
      const { accessToken, refreshToken: newRefreshToken } = newTokens;

      return {
        accessToken,
        refreshToken: newRefreshToken,
        tokenType: 'Bearer',
      };
    } catch (error) {
      logger.error({ message: 'Token refresh failed', error });
      throw new ApiError('Invalid refresh token', 401, ErrorCode.UNAUTHORIZED);
    }
  }

  /**
   * Logs out a user
   */
  public async logoutUser(userId: string, refreshToken?: string): Promise<void> {
    logger.info({ message: 'Processing logout request' }, { userId });

    try {
      if (refreshToken) {
        // revokeRefreshToken doesn't exist, use revokeAllUserTokens instead
        // We need userId for this
        const payload = await tokenService.verifyRefreshToken(refreshToken);
        if (payload) {
          await tokenService.revokeAllUserTokens(payload.userId);
        }
      }

      // Clear user cache
      await cacheService.del(`user:${userId}`);

      logger.info({ message: 'User logged out successfully' }, { userId });
    } catch (error) {
      logger.error({ message: 'Logout error', error });
      // Don't throw error on logout - always consider it successful
    }
  }

  /**
   * Gets user by ID
   */
  public async getUserById(userId: string): Promise<UserResponse | null> {
    // Try cache first
    const cached = await cacheService.get<UserResponse>(`user:${userId}`);
    if (cached) {
      return cached;
    }

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          profile: true,
        },
      });

      if (!user) {
        return null;
      }

      const userResponse: UserResponse = {
        id: user.id,
        email: user.email,
        name: user.username || '',
        created_at: user.createdAt,
        updated_at: user.updatedAt,
        ...(user.profile && {
          username: user.profile.username,
          full_name: user.profile.fullName,
          organization: user.profile.organization,
          bio: user.profile.bio,
          location: user.profile.location,
          avatar_url: user.profile.avatarUrl,
          preferred_language: user.profile.preferredLanguage,
          theme_preference: user.profile.themePreference,
        }),
      };

      // Cache the result
      await cacheService.set(`user:${userId}`, userResponse, CACHE_TTL.USER);

      return userResponse;
    } catch (error) {
      logger.error({ message: 'Failed to get user by ID', error, userId });
      return null;
    }
  }

  /**
   * Requests password reset
   */
  public async requestPasswordReset(email: string): Promise<void> {
    logger.info({ message: 'Password reset requested' }, { email });

    try {
      const user = await prisma.user.findUnique({
        where: { email },
      });

      if (!user) {
        // Don't reveal if email exists
        logger.warn({ message: 'Password reset requested for non-existent email', email });
        return;
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const tokenHash = crypto.createHash('sha256').update(resetToken).digest('hex');
      const expiresAt = new Date(Date.now() + 3600000); // 1 hour

      // Store token in database
      await prisma.passwordResetToken.create({
        data: {
          id: uuidv4(),
          userId: user.id,
          tokenHash,
          expiresAt,
        },
      });

      // Send reset email
      const resetUrl = `${config.appUrl}/reset-password?token=${resetToken}`;
      // sendNewPasswordEmail expects 3 params: to, userName, newPassword
      // For password reset, we don't send the new password, just the reset link
      await sendNewPasswordEmail(user.email, user.username || user.email, resetUrl);

      logger.info({ message: 'Password reset email sent' }, { email });
    } catch (error) {
      logger.error({ message: 'Password reset request failed', error, email });
      // Don't throw error to avoid revealing email existence
    }
  }

  /**
   * Resets password with token
   */
  public async resetPassword(token: string, newPassword: string): Promise<void> {
    logger.info({ message: 'Processing password reset' });

    try {
      const tokenHash = crypto.createHash('sha256').update(token).digest('hex');

      // Find valid token
      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          tokenHash,
          expiresAt: { gt: new Date() },
          usedAt: null,
        },
      });

      if (!resetToken) {
        throw new ApiError('Invalid or expired reset token', 400, ErrorCode.VALIDATION_ERROR);
      }

      // Hash new password
      const passwordHash = await bcryptjs.hash(newPassword, config.auth.saltRounds);

      // Update password and mark token as used
      await prisma.$transaction(async (tx) => {
        await tx.user.update({
          where: { id: resetToken.userId },
          data: { passwordHash },
        });

        await tx.passwordResetToken.update({
          where: { id: resetToken.id },
          data: { usedAt: new Date() },
        });
      });

      // Clear user cache
      await cacheService.del(`user:${resetToken.userId}`);

      logger.info({ message: 'Password reset successful' }, { userId: resetToken.userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error({ message: 'Password reset failed', error });
      throw new ApiError('Password reset failed', 500, ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Changes user password
   */
  public async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    logger.info({ message: 'Processing password change' }, { userId });

    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new ApiError('User not found', 404, ErrorCode.RESOURCE_NOT_FOUND);
      }

      // Verify current password
      const isPasswordValid = await bcryptjs.compare(currentPassword, user.passwordHash || '');
      if (!isPasswordValid) {
        throw new ApiError('Current password is incorrect', 401, ErrorCode.UNAUTHORIZED);
      }

      // Hash new password
      const passwordHash = await bcryptjs.hash(newPassword, config.auth.saltRounds);

      // Update password
      await prisma.user.update({
        where: { id: userId },
        data: { passwordHash },
      });

      // Clear user cache
      await cacheService.del(`user:${userId}`);

      logger.info({ message: 'Password changed successfully' }, { userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error({ message: 'Password change failed', error });
      throw new ApiError('Password change failed', 500, ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Verifies email with token
   */
  public async verifyEmail(token: string): Promise<void> {
    logger.info({ message: 'Processing email verification' });

    try {
      const verification = await prisma.emailVerification.findFirst({
        where: {
          token,
          expiresAt: { gt: new Date() },
          usedAt: null,
        },
      });

      if (!verification) {
        throw new ApiError('Invalid or expired verification token', 400, ErrorCode.VALIDATION_ERROR);
      }

      // Mark email as verified
      await prisma.$transaction(async (tx) => {
        await tx.user.update({
          where: { id: verification.userId },
          data: {
            // emailVerified and emailVerifiedAt fields don't exist in schema
            // Just update the user to mark activity
            updatedAt: new Date(),
          },
        });

        await tx.emailVerification.update({
          where: { id: verification.id },
          data: { usedAt: new Date() },
        });
      });

      logger.info({ message: 'Email verified successfully' }, { userId: verification.userId });
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }
      logger.error({ message: 'Email verification failed', error });
      throw new ApiError('Email verification failed', 500, ErrorCode.INTERNAL_SERVER_ERROR);
    }
  }
}

export default new AuthService();
