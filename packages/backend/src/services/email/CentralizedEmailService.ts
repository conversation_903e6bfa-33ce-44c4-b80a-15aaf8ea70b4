import nodemailer, { Transporter } from 'nodemailer';
import handlebars from 'handlebars';
import fs from 'fs/promises';
import path from 'path';
import prismaService from '../PrismaService';
import logger from '../../utils/logger';
import { createHash } from 'crypto';

// Email types
export interface EmailOptions {
  to: string | string[];
  subject: string;
  template?: string;
  context?: Record<string, any>;
  text?: string;
  html?: string;
  attachments?: Array<{
    filename: string;
    content?: Buffer | string;
    path?: string;
  }>;
  priority?: 'high' | 'normal' | 'low';
}

export interface EmailVerificationData {
  userId: string;
  email: string;
  name: string;
  verificationUrl: string;
}

export interface PasswordResetData {
  email: string;
  name: string;
  resetUrl: string;
  expiresIn: string;
}

export interface AccessRequestData {
  requesterId: string;
  requesterEmail: string;
  requesterName: string;
  projectId: string;
  projectTitle: string;
  message?: string;
}

export interface ProjectShareData {
  projectId: string;
  projectTitle: string;
  senderName: string;
  recipientEmail: string;
  role: 'viewer' | 'editor' | 'admin';
  inviteUrl: string;
  message?: string;
}

/**
 * Centralized Email Service
 * Handles all email operations for the SpheroSeg application
 */
export class CentralizedEmailService {
  private static instance: CentralizedEmailService;
  private transporter: Transporter | null = null;
  private templates: Map<string, handlebars.TemplateDelegate> = new Map();
  private prisma = prismaService;
  private isInitialized = false;

  // Admin emails for access requests
  private readonly ADMIN_EMAILS = [
    '<EMAIL>',
    '<EMAIL>'
  ];

  private constructor() {
    // Prisma already initialized in property declaration
  }

  public static getInstance(): CentralizedEmailService {
    if (!CentralizedEmailService.instance) {
      CentralizedEmailService.instance = new CentralizedEmailService();
    }
    return CentralizedEmailService.instance;
  }

  /**
   * Initialize the email service
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Configure transporter
      await this.configureTransporter();
      
      // Load email templates
      await this.loadTemplates();
      
      // Register Handlebars helpers
      this.registerHandlebarsHelpers();
      
      this.isInitialized = true;
      logger.info('CentralizedEmailService initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize CentralizedEmailService:', error);
      throw error;
    }
  }

  /**
   * Configure email transporter based on environment
   */
  private async configureTransporter(): Promise<void> {
    const config = {
      host: process.env.SMTP_HOST || 'mail.utia.cas.cz',
      port: parseInt(process.env.SMTP_PORT || '25'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: process.env.SMTP_USER && process.env.SMTP_PASS ? {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      } : undefined,
      tls: {
        rejectUnauthorized: process.env.NODE_ENV === 'production'
      }
    };

    this.transporter = nodemailer.createTransport(config);

    // Verify connection
    try {
      await this.transporter.verify();
      logger.info('Email transporter verified successfully');
    } catch (error) {
      logger.error('Email transporter verification failed:', error);
      // Don't throw in development, allow mock mode
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  /**
   * Load email templates from filesystem
   */
  private async loadTemplates(): Promise<void> {
    const templateDir = path.join(__dirname, '../../templates/email');
    
    const templateFiles = [
      'verification.hbs',
      'password-reset.hbs',
      'access-request.hbs',
      'project-share.hbs',
      'welcome.hbs'
    ];

    for (const file of templateFiles) {
      try {
        const templatePath = path.join(templateDir, file);
        const templateContent = await fs.readFile(templatePath, 'utf-8').catch(() => 
          this.getDefaultTemplate(file.replace('.hbs', ''))
        );
        const templateName = path.basename(file, '.hbs');
        this.templates.set(templateName, handlebars.compile(templateContent));
      } catch (error) {
        logger.warn(`Failed to load template ${file}, using default`);
        const templateName = path.basename(file, '.hbs');
        this.templates.set(templateName, handlebars.compile(this.getDefaultTemplate(templateName)));
      }
    }
  }

  /**
   * Get default template content
   */
  private getDefaultTemplate(name: string): string {
    const templates: Record<string, string> = {
      'verification': `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>SpheroSeg - Email Verification</h1>
            </div>
            <p>Hi {{name}},</p>
            <p>Thank you for registering with SpheroSeg! Please verify your email address by clicking the button below:</p>
            <div style="text-align: center;">
              <a href="{{verificationUrl}}" class="button">Verify Email</a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">{{verificationUrl}}</p>
            <p>This link will expire in 24 hours.</p>
            <div class="footer">
              <p>© 2025 SpheroSeg - Cell Segmentation Platform</p>
            </div>
          </div>
        </body>
        </html>
      `,
      'password-reset': `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>SpheroSeg - Password Reset</h1>
            </div>
            <p>Hi {{name}},</p>
            <p>We received a request to reset your password. Click the button below to create a new password:</p>
            <div style="text-align: center;">
              <a href="{{resetUrl}}" class="button">Reset Password</a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">{{resetUrl}}</p>
            <p>This link will expire in {{expiresIn}}.</p>
            <p>If you didn't request this, please ignore this email.</p>
            <div class="footer">
              <p>© 2025 SpheroSeg - Cell Segmentation Platform</p>
            </div>
          </div>
        </body>
        </html>
      `,
      'access-request': `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .info-box { background: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>SpheroSeg - Access Request</h1>
            </div>
            <p>Dear Administrator,</p>
            <p>A new access request has been submitted:</p>
            <div class="info-box">
              <p><strong>Requester:</strong> {{requesterName}} ({{requesterEmail}})</p>
              <p><strong>Project:</strong> {{projectTitle}}</p>
              <p><strong>Project ID:</strong> {{projectId}}</p>
              {{#if message}}
              <p><strong>Message:</strong> {{message}}</p>
              {{/if}}
            </div>
            <p>Please review this request in the SpheroSeg admin panel.</p>
            <div class="footer">
              <p>© 2025 SpheroSeg - Cell Segmentation Platform</p>
            </div>
          </div>
        </body>
        </html>
      `,
      'project-share': `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #17a2b8; color: white; padding: 20px; text-align: center; }
            .button { display: inline-block; padding: 12px 24px; background: #17a2b8; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .role-badge { display: inline-block; padding: 4px 8px; background: #ffc107; color: #333; border-radius: 3px; font-weight: bold; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>SpheroSeg - Project Invitation</h1>
            </div>
            <p>Hello,</p>
            <p>{{senderName}} has invited you to collaborate on a project:</p>
            <h2>{{projectTitle}}</h2>
            <p>Your role: <span class="role-badge">{{role}}</span></p>
            {{#if message}}
            <p><em>"{{message}}"</em></p>
            {{/if}}
            <div style="text-align: center;">
              <a href="{{inviteUrl}}" class="button">Accept Invitation</a>
            </div>
            <p>Or copy and paste this link into your browser:</p>
            <p style="word-break: break-all;">{{inviteUrl}}</p>
            <div class="footer">
              <p>© 2025 SpheroSeg - Cell Segmentation Platform</p>
            </div>
          </div>
        </body>
        </html>
      `,
      'welcome': `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #6f42c1; color: white; padding: 20px; text-align: center; }
            .button { display: inline-block; padding: 12px 24px; background: #6f42c1; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #666; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to SpheroSeg!</h1>
            </div>
            <p>Hi {{name}},</p>
            <p>Welcome to SpheroSeg - your advanced cell segmentation platform!</p>
            <p>Here's what you can do:</p>
            <ul>
              <li>Upload microscopy images</li>
              <li>Perform automatic cell segmentation</li>
              <li>Edit and refine segmentation results</li>
              <li>Export results in multiple formats</li>
              <li>Collaborate with your team</li>
            </ul>
            <div style="text-align: center;">
              <a href="{{appUrl}}/dashboard" class="button">Get Started</a>
            </div>
            <div class="footer">
              <p>© 2025 SpheroSeg - Cell Segmentation Platform</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    return templates[name] || '<p>{{content}}</p>';
  }

  /**
   * Register Handlebars helpers
   */
  private registerHandlebarsHelpers(): void {
    handlebars.registerHelper('formatDate', (date: Date) => {
      return new Date(date).toLocaleDateString();
    });

    handlebars.registerHelper('uppercase', (str: string) => {
      return str?.toUpperCase();
    });

    handlebars.registerHelper('eq', (a: any, b: any) => {
      return a === b;
    });
  }

  /**
   * Send email verification
   */
  public async sendVerificationEmail(data: EmailVerificationData): Promise<void> {
    const token = this.generateToken();
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // Store token in database
    await this.prisma.prisma.emailVerification.create({
      data: {
        userId: data.userId,
        token,
        expiresAt
      }
    });

    const verificationUrl = `${process.env.APP_URL || 'http://localhost:3000'}/verify-email?token=${token}`;

    await this.sendEmail({
      to: data.email,
      subject: 'Verify your SpheroSeg account',
      template: 'verification',
      context: {
        ...data,
        verificationUrl
      }
    });

    logger.info(`Verification email sent to ${data.email}`);
  }

  /**
   * Send password reset email
   */
  public async sendPasswordResetEmail(data: PasswordResetData): Promise<void> {
    await this.sendEmail({
      to: data.email,
      subject: 'Reset your SpheroSeg password',
      template: 'password-reset',
      context: data
    });

    logger.info(`Password reset email sent to ${data.email}`);
  }

  /**
   * Send access request notification to admins
   */
  public async sendAccessRequestEmail(data: AccessRequestData): Promise<void> {
    // Send to all admin emails
    for (const adminEmail of this.ADMIN_EMAILS) {
      await this.sendEmail({
        to: adminEmail,
        subject: `Access Request: ${data.projectTitle}`,
        template: 'access-request',
        context: data,
        priority: 'high'
      });
    }

    // Store access request in database
    await this.prisma.prisma.accessRequest.create({
      data: {
        email: data.requesterEmail,
        name: data.requesterName || null,
        reason: data.message || null,
        status: 'pending'
      }
    });

    logger.info(`Access request emails sent to admins for project ${data.projectId}`);
  }

  /**
   * Send project share invitation
   */
  public async sendProjectShareEmail(data: ProjectShareData): Promise<void> {
    const token = this.generateToken();
    const inviteUrl = `${process.env.APP_URL || 'http://localhost:3000'}/invite?token=${token}`;

    // Store invitation in database - requires recipient userId which isn't available
    // The ProjectShare will be created when the recipient accepts the invitation
    // await this.prisma.prisma.projectShare.create({
    //   data: {
    //     projectId: data.projectId,
    //     userId: recipientUserId, // Need to get this from user lookup by email
    //     invitationEmail: data.recipientEmail,
    //     permission: data.role || 'view',
    //     invitationToken: token
    //   }
    // });

    await this.sendEmail({
      to: data.recipientEmail,
      subject: `You've been invited to collaborate on ${data.projectTitle}`,
      template: 'project-share',
      context: {
        ...data,
        inviteUrl
      }
    });

    logger.info(`Project share invitation sent to ${data.recipientEmail}`);
  }

  /**
   * Core email sending method
   */
  private async sendEmail(options: EmailOptions): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      let html = options.html;
      let text = options.text;

      // Use template if provided
      if (options.template && this.templates.has(options.template)) {
        const template = this.templates.get(options.template)!;
        html = template(options.context || {});
        
        // Generate text version from HTML if not provided
        if (!text) {
          text = this.htmlToText(html);
        }
      }

      const mailOptions = {
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        text,
        html,
        attachments: options.attachments,
        priority: options.priority
      };

      // Send email
      if (this.transporter) {
        const info = await this.transporter.sendMail(mailOptions);
        logger.info(`Email sent: ${info.messageId}`);
        
        // Log email audit
        await this.logEmailAudit({
          to: options.to,
          subject: options.subject,
          template: options.template,
          status: 'sent',
          messageId: info.messageId
        });
      } else {
        // Fallback to console in development
        logger.warn('Email transporter not available, logging to console:');
        console.log('📧 Email:', mailOptions);
        
        await this.logEmailAudit({
          to: options.to,
          subject: options.subject,
          template: options.template,
          status: 'mock',
          messageId: `mock-${Date.now()}`
        });
      }
    } catch (error) {
      logger.error('Failed to send email:', error);
      
      // Log failed email
      await this.logEmailAudit({
        to: options.to,
        subject: options.subject,
        template: options.template,
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      
      throw error;
    }
  }

  /**
   * Generate secure token
   */
  private generateToken(): string {
    return createHash('sha256')
      .update(Math.random().toString() + Date.now().toString())
      .digest('hex');
  }

  /**
   * Convert HTML to plain text
   */
  private htmlToText(html: string): string {
    return html
      .replace(/<style[^>]*>.*?<\/style>/gs, '')
      .replace(/<script[^>]*>.*?<\/script>/gs, '')
      .replace(/<[^>]+>/g, '')
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * Log email audit trail
   * TODO: Add emailLog table to schema if audit logging is needed
   */
  private async logEmailAudit(data: {
    to: string | string[];
    subject: string;
    template?: string;
    status: 'sent' | 'failed' | 'mock';
    messageId?: string;
    error?: string;
  }): Promise<void> {
    // Email audit logging disabled - emailLog table doesn't exist in current schema
    // If audit logging is needed, add the following table to schema.prisma:
    //
    // model EmailLog {
    //   id        String   @id @default(uuid())
    //   to        String
    //   subject   String
    //   template  String?
    //   status    String
    //   messageId String?  @map("message_id")
    //   error     String?
    //   sentAt    DateTime @default(now()) @map("sent_at")
    //   createdAt DateTime @default(now()) @map("created_at")
    //   
    //   @@index([sentAt])
    //   @@index([status])
    //   @@map("email_logs")
    // }
    
    // For now, just log to application logs
    logger.info('Email audit:', {
      to: Array.isArray(data.to) ? data.to.join(', ') : data.to,
      subject: data.subject,
      template: data.template,
      status: data.status,
      messageId: data.messageId,
      error: data.error
    });
  }

  /**
   * Verify email token
   * TODO: Add emailVerified field to User model if email verification tracking is needed
   */
  public async verifyEmailToken(token: string): Promise<{ userId: string } | null> {
    const verification = await this.prisma.prisma.emailVerification.findUnique({
      where: { token }
    });

    if (!verification || verification.expiresAt < new Date()) {
      return null;
    }

    // Mark user as verified - emailVerified field doesn't exist in current User schema
    // If email verification tracking is needed, add to User model in schema.prisma:
    // emailVerified Boolean @default(false) @map("email_verified")
    // emailVerifiedAt DateTime? @map("email_verified_at")
    //
    // await this.prisma.prisma.user.update({
    //   where: { id: verification.userId },
    //   data: { 
    //     emailVerified: true,
    //     emailVerifiedAt: new Date()
    //   }
    // });

    // Mark the verification as used instead of deleting (for audit purposes)
    await this.prisma.prisma.emailVerification.update({
      where: { id: verification.id },
      data: { usedAt: new Date() }
    });

    logger.info(`Email verified for user ${verification.userId}`);
    return { userId: verification.userId };
  }

  /**
   * Test email configuration
   */
  public async testEmailConfiguration(): Promise<boolean> {
    try {
      if (!this.transporter) {
        await this.initialize();
      }

      if (this.transporter) {
        await this.transporter.verify();
        return true;
      }
      return false;
    } catch (error) {
      logger.error('Email configuration test failed:', error);
      return false;
    }
  }
}

// Export singleton instance
export const emailService = CentralizedEmailService.getInstance();

// Backward compatibility exports for existing code
export async function sendPasswordResetSimple(
  to: string,
  userName: string,
  newPassword: string
): Promise<void> {
  await emailService.sendPasswordResetEmail({
    email: to,
    name: userName,
    resetUrl: `${process.env.APP_URL}/reset-password?password=${encodeURIComponent(newPassword)}`,
    expiresIn: '24 hours'
  });
}

export async function sendProjectShareInvite(
  to: string,
  inviterName: string,
  projectName: string,
  role: string,
  invitationLink: string
): Promise<void> {
  await emailService.sendProjectShareEmail({
    projectId: 'unknown',
    projectTitle: projectName,
    senderName: inviterName,
    recipientEmail: to,
    role: role as 'viewer' | 'editor' | 'admin',
    inviteUrl: invitationLink
  });
}

export async function sendNewPasswordEmail(
  to: string,
  userName: string,
  newPassword: string
): Promise<void> {
  await sendPasswordResetSimple(to, userName, newPassword);
}

export async function sendVerificationEmail(params: {
  to: string;
  userName: string;
  verificationUrl: string;
}): Promise<void> {
  await emailService.sendVerificationEmail({
    userId: 'temp-user-id',
    email: params.to,
    name: params.userName,
    verificationUrl: params.verificationUrl
  });
}

export async function sendAccessRequest(params: any): Promise<void> {
  await emailService.sendAccessRequestEmail({
    requesterId: params.requesterId || 'unknown',
    requesterEmail: params.requesterEmail || params.email,
    requesterName: params.requesterName || params.name,
    projectId: params.projectId || 'unknown',
    projectTitle: params.projectTitle || 'Unknown Project',
    message: params.message || params.reason
  });
}

export async function sendAccessRequestNotification(params: any): Promise<void> {
  await sendAccessRequest(params);
}

export async function sendInvitationAcceptedNotification(params: any): Promise<void> {
  // This functionality can be implemented later if needed
  console.log('Invitation accepted notification:', params);
}