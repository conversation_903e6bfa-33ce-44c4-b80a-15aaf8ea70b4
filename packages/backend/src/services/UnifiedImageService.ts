/**
 * Unified Image Service
 * Consolidates all image processing functionality from:
 * - imageProcessingService.ts
 * - imageProcessing.enhanced.ts
 * - imageDeleteService.ts
 */

import sharp from 'sharp';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { prisma } from './PrismaService';
import { createLogger } from '../utils/logger';

const logger = createLogger('UnifiedImageService');

// Configuration
const THUMBNAIL_SIZE = { width: 300, height: 300 };
const PREVIEW_SIZE = { width: 800, height: 800 };
const MAX_IMAGE_SIZE = 100 * 1024 * 1024; // 100MB
const SUPPORTED_FORMATS = ['jpeg', 'jpg', 'png', 'tiff', 'tif', 'webp', 'bmp', 'gif'];
const UPLOAD_DIR = path.join(__dirname, '../../../uploads');
const THUMBNAILS_DIR = path.join(UPLOAD_DIR, 'thumbnails');
const TEMP_DIR = path.join(UPLOAD_DIR, 'temp');

// Interfaces
export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  hasAlpha?: boolean;
  orientation?: number;
  channels?: number;
  density?: number;
}

export interface ProcessingOptions {
  generateThumbnail?: boolean;
  generatePreview?: boolean;
  optimize?: boolean;
  extractMetadata?: boolean;
}

export interface ProcessedImage {
  id: string;
  originalPath: string;
  thumbnailPath?: string;
  previewPath?: string;
  metadata: ImageMetadata;
  optimized: boolean;
}

export class UnifiedImageService {
  private initialized: boolean = false;

  constructor() {
    this.initialize();
  }

  /**
   * Initialize service and create necessary directories
   */
  private async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await fs.mkdir(UPLOAD_DIR, { recursive: true });
      await fs.mkdir(THUMBNAILS_DIR, { recursive: true });
      await fs.mkdir(TEMP_DIR, { recursive: true });
      this.initialized = true;
      logger.info({ message: 'UnifiedImageService initialized successfully' });
    } catch (error) {
      logger.error('Failed to initialize UnifiedImageService:', error);
      throw error;
    }
  }

  /**
   * Validate image file
   */
  async validateImage(filePath: string): Promise<boolean> {
    try {
      const stats = await fs.stat(filePath);

      // Check file size
      if (stats.size > MAX_IMAGE_SIZE) {
        throw new Error(`File too large: ${stats.size} bytes (max: ${MAX_IMAGE_SIZE})`);
      }

      // Check if Sharp can process it
      const metadata = await sharp(filePath).metadata();

      // Check format
      const format = metadata.format?.toLowerCase();
      if (!format || !SUPPORTED_FORMATS.includes(format)) {
        throw new Error(`Unsupported format: ${format}`);
      }

      return true;
    } catch (error) {
      logger.error('Image validation failed:', error);
      return false;
    }
  }

  /**
   * Extract image metadata
   */
  async extractMetadata(filePath: string): Promise<ImageMetadata> {
    try {
      const metadata = await sharp(filePath).metadata();
      const stats = await fs.stat(filePath);

      return {
        width: metadata.width || 0,
        height: metadata.height || 0,
        format: metadata.format || 'unknown',
        size: stats.size,
        hasAlpha: metadata.hasAlpha,
        orientation: metadata.orientation,
        channels: metadata.channels,
        density: metadata.density,
      };
    } catch (error) {
      logger.error('Failed to extract metadata:', error);
      throw error;
    }
  }

  /**
   * Generate thumbnail
   */
  async generateThumbnail(sourcePath: string, outputPath?: string): Promise<string> {
    try {
      const filename = path.basename(sourcePath, path.extname(sourcePath));
      const thumbnailPath = outputPath || path.join(THUMBNAILS_DIR, `${filename}_thumb.jpg`);

      await sharp(sourcePath)
        .resize(THUMBNAIL_SIZE.width, THUMBNAIL_SIZE.height, {
          fit: 'cover',
          withoutEnlargement: true,
        })
        .jpeg({ quality: 85 })
        .toFile(thumbnailPath);

      logger.info({ message: 'Thumbnail generated:', thumbnailPath });
      return thumbnailPath;
    } catch (error) {
      logger.error('Failed to generate thumbnail:', error);
      throw error;
    }
  }

  /**
   * Generate preview (larger than thumbnail)
   */
  async generatePreview(sourcePath: string, outputPath?: string): Promise<string> {
    try {
      const filename = path.basename(sourcePath, path.extname(sourcePath));
      const previewPath =
        outputPath || path.join(UPLOAD_DIR, 'previews', `${filename}_preview.jpg`);

      // Ensure preview directory exists
      await fs.mkdir(path.dirname(previewPath), { recursive: true });

      await sharp(sourcePath)
        .resize(PREVIEW_SIZE.width, PREVIEW_SIZE.height, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ quality: 90 })
        .toFile(previewPath);

      logger.info({ message: 'Preview generated:', previewPath });
      return previewPath;
    } catch (error) {
      logger.error('Failed to generate preview:', error);
      throw error;
    }
  }

  /**
   * Optimize image (reduce size while maintaining quality)
   */
  async optimizeImage(sourcePath: string): Promise<string> {
    try {
      const metadata = await sharp(sourcePath).metadata();
      const ext = path.extname(sourcePath);
      const optimizedPath = sourcePath.replace(ext, `_optimized${ext}`);

      const pipeline = sharp(sourcePath);

      // Apply format-specific optimization
      switch (metadata.format) {
        case 'jpeg':
        case 'jpg':
          await pipeline.jpeg({ quality: 85, progressive: true }).toFile(optimizedPath);
          break;
        case 'png':
          await pipeline.png({ compressionLevel: 9, progressive: true }).toFile(optimizedPath);
          break;
        case 'webp':
          await pipeline.webp({ quality: 85 }).toFile(optimizedPath);
          break;
        default:
          // Convert to JPEG for other formats
          await pipeline
            .jpeg({ quality: 85, progressive: true })
            .toFile(optimizedPath.replace(ext, '.jpg'));
      }

      // Check if optimization actually reduced size
      const originalStats = await fs.stat(sourcePath);
      const optimizedStats = await fs.stat(optimizedPath);

      if (optimizedStats.size < originalStats.size) {
        logger.info(`Image optimized: ${originalStats.size} → ${optimizedStats.size} bytes`);
        return optimizedPath;
      } else {
        // Remove optimized version if it's not smaller
        await fs.unlink(optimizedPath);
        return sourcePath;
      }
    } catch (error) {
      logger.error('Failed to optimize image:', error);
      return sourcePath; // Return original on error
    }
  }

  /**
   * Process uploaded image with all operations
   */
  async processImage(filePath: string, options: ProcessingOptions = {}): Promise<ProcessedImage> {
    const {
      generateThumbnail = true,
      generatePreview = false,
      optimize = true,
      extractMetadata = true,
    } = options;

    // Validate image
    const isValid = await this.validateImage(filePath);
    if (!isValid) {
      throw new Error('Invalid image file');
    }

    const result: ProcessedImage = {
      id: crypto.randomUUID(),
      originalPath: filePath,
      metadata: {} as ImageMetadata,
      optimized: false,
    };

    // Extract metadata
    if (extractMetadata) {
      result.metadata = await this.extractMetadata(filePath);
    }

    // Optimize image
    if (optimize) {
      const optimizedPath = await this.optimizeImage(filePath);
      if (optimizedPath !== filePath) {
        result.originalPath = optimizedPath;
        result.optimized = true;
      }
    }

    // Generate thumbnail
    if (generateThumbnail) {
      result.thumbnailPath = await this.generateThumbnail(result.originalPath);
    }

    // Generate preview
    if (generatePreview) {
      result.previewPath = await this.generatePreview(result.originalPath);
    }

    return result;
  }

  /**
   * Delete image and all associated files
   */
  async deleteImage(imageId: string): Promise<void> {
    try {
      // Get image info from database
      const image = await prisma.image.findUnique({
        where: { id: imageId },
        select: {
          storageFilename: true,
          thumbnailPath: true,
          previewPath: true,
        },
      });

      if (!image) {
        throw new Error(`Image not found: ${imageId}`);
      }

      const filesToDelete = [
        image.storageFilename && path.join(UPLOAD_DIR, image.storageFilename),
        image.thumbnailPath && path.join(UPLOAD_DIR, image.thumbnailPath),
        image.previewPath && path.join(UPLOAD_DIR, image.previewPath),
      ].filter(Boolean);

      // Delete files
      for (const file of filesToDelete) {
        try {
          await fs.unlink(file);
          logger.info({ message: 'Deleted file:', file });
        } catch (error) {
          logger.warn({ message: 'Failed to delete file:', file, error });
        }
      }

      // Delete from database
      await prisma.image.delete({
        where: { id: imageId },
      });

      logger.info({ message: 'Image deleted successfully:', imageId });
    } catch (error) {
      logger.error('Failed to delete image:', error);
      throw error;
    }
  }

  /**
   * Clean up temporary files
   */
  async cleanupTempFiles(olderThanHours: number = 24): Promise<number> {
    try {
      const files = await fs.readdir(TEMP_DIR);
      const now = Date.now();
      const maxAge = olderThanHours * 60 * 60 * 1000;
      let deletedCount = 0;

      for (const file of files) {
        const filePath = path.join(TEMP_DIR, file);
        const stats = await fs.stat(filePath);

        if (now - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          deletedCount++;
        }
      }

      logger.info(`Cleaned up ${deletedCount} temporary files`);
      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup temp files:', error);
      return 0;
    }
  }

  /**
   * Convert image to different format
   */
  async convertFormat(sourcePath: string, targetFormat: 'jpeg' | 'png' | 'webp'): Promise<string> {
    try {
      const filename = path.basename(sourcePath, path.extname(sourcePath));
      const outputPath = path.join(path.dirname(sourcePath), `${filename}.${targetFormat}`);

      await sharp(sourcePath).toFormat(targetFormat).toFile(outputPath);

      logger.info({ message: `Image converted to ${targetFormat}:`, outputPath });
      return outputPath;
    } catch (error) {
      logger.error('Failed to convert image format:', error);
      throw error;
    }
  }

  /**
   * Batch process multiple images
   */
  async batchProcess(
    filePaths: string[],
    options: ProcessingOptions = {}
  ): Promise<ProcessedImage[]> {
    const results: ProcessedImage[] = [];

    for (const filePath of filePaths) {
      try {
        const processed = await this.processImage(filePath, options);
        results.push(processed);
      } catch (error) {
        logger.error(`Failed to process image ${filePath}:`, error);
      }
    }

    return results;
  }
}

// Singleton instance
let instance: UnifiedImageService | null = null;

export function getImageService(): UnifiedImageService {
  if (!instance) {
    instance = new UnifiedImageService();
  }
  return instance;
}
