/**
 * Recommendation Aggregator Service
 *
 * Centralized service that collects, analyzes, and prioritizes recommendations
 * from various system components including performance monitoring, error tracking,
 * database optimization, and resource management.
 */

import logger from '../utils/logger';
import { performanceTracker } from '../monitoring/performanceTracker.stub';
import { getPerformanceCoordinatorReport } from '../startup/performanceCoordinator.startup';
import DatabaseOptimizationService from './databaseOptimizationService';
import { getPool } from '../db';
import { RecommendationValidator } from '../utils/recommendationValidator';

export interface UnifiedRecommendation {
  id: string;
  source: 'performance' | 'database' | 'error' | 'resource' | 'ml' | 'security' | 'business';
  type: 'optimization' | 'scaling' | 'caching' | 'refactoring' | 'security' | 'configuration';
  priority: number; // 0-100
  title: string;
  description: string;
  impact: 'critical' | 'high' | 'medium' | 'low';
  effort: 'low' | 'medium' | 'high';
  category: string;
  actionItems: string[];
  estimatedTimeToImplement?: string;
  potentialSavings?: {
    performance?: string;
    cost?: string;
    resources?: string;
  };
  metadata?: Record<string, unknown>;
  timestamp: Date;
  expires?: Date;
  implemented?: boolean;
}

export interface RecommendationContext {
  systemLoad: number;
  errorRate: number;
  memoryUsage: number;
  cpuUsage: number;
  queueLength: number;
  userCount: number;
  requestRate: number;
}

class RecommendationAggregatorService {
  private recommendations: Map<string, UnifiedRecommendation> = new Map();
  private dbOptimizationService: DatabaseOptimizationService | null = null;
  private lastAggregation: Date = new Date();
  private aggregationInterval = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.initializeServices();
  }

  private async initializeServices(): Promise<void> {
    try {
      const pool = getPool();
      this.dbOptimizationService = new DatabaseOptimizationService({} as any);
    } catch (error) {
      logger.error('Failed to initialize recommendation aggregator services', { error });
    }
  }

  /**
   * Collect and aggregate recommendations from all sources
   */
  public async aggregateRecommendations(
    context?: RecommendationContext
  ): Promise<UnifiedRecommendation[]> {
    const now = new Date();

    // Check if we should re-aggregate
    if (now.getTime() - this.lastAggregation.getTime() < this.aggregationInterval) {
      return Array.from(this.recommendations.values())
        .filter((rec) => !rec.expires || rec.expires > now)
        .sort((a, b) => b.priority - a.priority);
    }

    this.lastAggregation = now;
    this.recommendations.clear();

    // Collect from all sources
    await Promise.all([
      this.collectPerformanceRecommendations(context),
      this.collectDatabaseRecommendations(context),
      this.collectResourceRecommendations(context),
      this.collectMLRecommendations(context),
      this.collectBusinessRecommendations(context),
      this.collectSecurityRecommendations(context),
    ]);

    // Apply context-based adjustments
    if (context) {
      this.adjustPrioritiesBasedOnContext(context);
    }

    // Return sorted recommendations
    return Array.from(this.recommendations.values())
      .filter((rec) => !rec.expires || rec.expires > now)
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Collect performance-related recommendations
   */
  private async collectPerformanceRecommendations(_context?: RecommendationContext): Promise<void> {
    try {
      // Get from performance tracker
      const perfStats = performanceTracker.getPerformanceStats();
      const perfReport = performanceTracker.generatePerformanceReport();

      // Get from performance coordinator
      const coordinatorReport = await getPerformanceCoordinatorReport();

      // Process performance tracker recommendations
      perfStats.recommendations.forEach((rec: unknown) => {
        const recommendation_data = rec as Record<string, unknown>;
        const recommendation: UnifiedRecommendation = {
          id: `perf_${recommendation_data.id || 'unknown'}`,
          source: 'performance',
          type:
            (recommendation_data.type as
              | 'optimization'
              | 'scaling'
              | 'caching'
              | 'refactoring'
              | 'security'
              | 'configuration') || 'optimization',
          priority: (recommendation_data.priority as number) || 50,
          title: (recommendation_data.description as string) || 'Performance recommendation',
          description: (recommendation_data.description as string) || 'Performance recommendation',
          impact: this.calculateImpact(recommendation_data.impact as string),
          effort: (recommendation_data.effort as 'low' | 'medium' | 'high') || 'medium',
          category: (recommendation_data.component as string) || 'performance',
          actionItems:
            ((recommendation_data.details as Record<string, unknown>)?.suggestions as string[]) ||
            [],
          metadata: recommendation_data.details as Record<string, unknown>,
          timestamp: new Date(),
          expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        };

        // Validate and sanitize before adding
        const validation = RecommendationValidator.validateRecommendation(recommendation);
        if (validation.isValid) {
          this.recommendations.set(recommendation.id, recommendation);
        } else {
          logger.warn('Invalid recommendation from performance tracker', {
            id: recommendation_data.id,
            errors: validation.errors,
          });
          // Try to sanitize and add anyway
          const sanitized = RecommendationValidator.sanitizeRecommendation(
            recommendation as unknown as Record<string, unknown>
          );
          if (sanitized) {
            this.recommendations.set(sanitized.id, sanitized);
          }
        }
      });

      // Process string recommendations from report
      perfReport.recommendations.forEach((rec: unknown, index: number) => {
        const recText =
          typeof rec === 'string'
            ? rec
            : String(
                (rec as Record<string, unknown>).description ||
                  (rec as Record<string, unknown>).message ||
                  'Unknown recommendation'
              );
        const priority = this.calculatePriorityFromText(recText);
        const recommendation: UnifiedRecommendation = {
          id: `perf_report_${index}`,
          source: 'performance',
          type: 'optimization',
          priority,
          title: String(recText),
          description: String(recText),
          impact: priority > 80 ? 'critical' : priority > 60 ? 'high' : 'medium',
          effort: 'medium',
          category: 'performance',
          actionItems: [String(recText)],
          timestamp: new Date(),
        };

        this.recommendations.set(recommendation.id, recommendation);
      });

      // Process coordinator recommendations
      if (
        coordinatorReport &&
        typeof coordinatorReport === 'object' &&
        'recommendations' in coordinatorReport &&
        Array.isArray(coordinatorReport.recommendations)
      ) {
        coordinatorReport.recommendations.forEach((rec: string, index: number) => {
          const priority = this.calculatePriorityFromText(rec);
          const recommendation: UnifiedRecommendation = {
            id: `coord_${index}`,
            source: 'performance',
            type: 'optimization',
            priority,
            title: rec,
            description: rec,
            impact: priority > 80 ? 'critical' : priority > 60 ? 'high' : 'medium',
            effort: 'medium',
            category: 'system',
            actionItems: [rec],
            timestamp: new Date(),
          };

          this.recommendations.set(recommendation.id, recommendation);
        });
      }
    } catch (error) {
      logger.error('Error collecting performance recommendations', { error });
    }
  }

  /**
   * Collect database optimization recommendations
   */
  private async collectDatabaseRecommendations(_context?: RecommendationContext): Promise<void> {
    try {
      if (!this.dbOptimizationService) return;

      const dbProfile = await this.dbOptimizationService.generatePerformanceProfile();

      dbProfile.recommendedOptimizations.forEach((rec: string, index: number) => {
        const priority = this.calculatePriorityFromText(rec);
        const recommendation: UnifiedRecommendation = {
          id: `db_${index}`,
          source: 'database',
          type: 'optimization',
          priority,
          title: rec,
          description: rec,
          impact: priority > 80 ? 'critical' : priority > 60 ? 'high' : 'medium',
          effort: rec.includes('index') ? 'low' : 'medium',
          category: 'database',
          actionItems: this.generateDatabaseActionItems(rec),
          estimatedTimeToImplement: rec.includes('index') ? '1-2 hours' : '2-4 hours',
          potentialSavings: {
            performance: priority > 80 ? '50-70% query time reduction' : '20-40% improvement',
          },
          timestamp: new Date(),
        };

        this.recommendations.set(recommendation.id, recommendation);
      });
    } catch (error) {
      logger.error('Error collecting database recommendations', { error });
    }
  }

  /**
   * Collect resource management recommendations
   */
  private async collectResourceRecommendations(context?: RecommendationContext): Promise<void> {
    const memUsage = process.memoryUsage();
    const memoryUtilization = (memUsage.heapUsed / memUsage.heapTotal) * 100;

    if (memoryUtilization > 85) {
      this.recommendations.set('resource_memory_critical', {
        id: 'resource_memory_critical',
        source: 'resource',
        type: 'optimization',
        priority: 95,
        title: 'Critical: Memory usage exceeds 85%',
        description: `Memory utilization is at ${memoryUtilization.toFixed(1)}%. Immediate action required to prevent out-of-memory errors.`,
        impact: 'critical',
        effort: 'medium',
        category: 'memory',
        actionItems: [
          'Enable aggressive garbage collection',
          'Review and fix memory leaks',
          'Implement memory-efficient data structures',
          'Consider horizontal scaling',
          'Review cache eviction policies',
        ],
        estimatedTimeToImplement: '4-8 hours',
        potentialSavings: {
          resources: '30-50% memory reduction possible',
          performance: 'Prevent OOM crashes',
        },
        timestamp: new Date(),
        expires: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
      });
    }

    // CPU recommendations
    if (context && context.cpuUsage > 80) {
      this.recommendations.set('resource_cpu_high', {
        id: 'resource_cpu_high',
        source: 'resource',
        type: 'scaling',
        priority: 85,
        title: 'High CPU usage detected',
        description: `CPU usage is at ${context.cpuUsage}%. Consider optimization or scaling.`,
        impact: 'high',
        effort: 'medium',
        category: 'cpu',
        actionItems: [
          'Profile CPU-intensive operations',
          'Optimize algorithms and data structures',
          'Implement request throttling',
          'Consider vertical or horizontal scaling',
          'Move heavy computations to background jobs',
        ],
        estimatedTimeToImplement: '8-16 hours',
        timestamp: new Date(),
      });
    }
  }

  /**
   * Collect ML-specific recommendations
   */
  private async collectMLRecommendations(context?: RecommendationContext): Promise<void> {
    if (context && context.queueLength > 20) {
      this.recommendations.set('ml_queue_backlog', {
        id: 'ml_queue_backlog',
        source: 'ml',
        type: 'scaling',
        priority: 88,
        title: 'ML processing queue backlog detected',
        description: `${context.queueLength} items waiting in ML processing queue. Consider scaling ML workers.`,
        impact: 'high',
        effort: 'low',
        category: 'ml_processing',
        actionItems: [
          'Scale ML service replicas',
          'Optimize ML model inference time',
          'Implement batch processing',
          'Consider GPU acceleration',
          'Add queue priority levels',
        ],
        estimatedTimeToImplement: '2-4 hours',
        potentialSavings: {
          performance: '60-80% reduction in queue wait time',
        },
        timestamp: new Date(),
      });
    }
  }

  /**
   * Collect business metrics recommendations
   */
  private async collectBusinessRecommendations(context?: RecommendationContext): Promise<void> {
    if (context && context.errorRate > 5) {
      this.recommendations.set('business_error_rate', {
        id: 'business_error_rate',
        source: 'business',
        type: 'optimization',
        priority: 92,
        title: 'High user error rate impacting business',
        description: `Error rate of ${context.errorRate}% is impacting user experience and potentially causing data loss.`,
        impact: 'critical',
        effort: 'medium',
        category: 'user_experience',
        actionItems: [
          'Implement comprehensive error handling',
          'Add user-friendly error messages',
          'Create error recovery mechanisms',
          'Add client-side validation',
          'Implement retry logic with exponential backoff',
        ],
        estimatedTimeToImplement: '8-16 hours',
        potentialSavings: {
          performance: 'Reduce support tickets by 40-60%',
        },
        timestamp: new Date(),
      });
    }
  }

  /**
   * Collect security recommendations
   */
  private async collectSecurityRecommendations(_context?: RecommendationContext): Promise<void> {
    // Add security-specific recommendations
    const hasOldDependencies = true; // This would check actual dependencies

    if (hasOldDependencies) {
      this.recommendations.set('security_dependencies', {
        id: 'security_dependencies',
        source: 'security',
        type: 'security',
        priority: 75,
        title: 'Update dependencies for security patches',
        description:
          'Several dependencies have known security vulnerabilities that should be patched.',
        impact: 'high',
        effort: 'low',
        category: 'dependencies',
        actionItems: [
          'Run npm audit and review vulnerabilities',
          'Update dependencies to latest secure versions',
          'Test application after updates',
          'Set up automated dependency scanning',
        ],
        estimatedTimeToImplement: '2-4 hours',
        timestamp: new Date(),
      });
    }
  }

  /**
   * Calculate priority from recommendation text
   */
  private calculatePriorityFromText(text: string): number {
    const lowerText = text.toLowerCase();

    if (lowerText.includes('critical') || lowerText.includes('immediately')) {
      return 95;
    } else if (lowerText.includes('high') || lowerText.includes('urgent')) {
      return 85;
    } else if (lowerText.includes('warning') || lowerText.includes('consider')) {
      return 70;
    } else if (lowerText.includes('low') || lowerText.includes('optional')) {
      return 30;
    }

    return 50; // Default medium priority
  }

  /**
   * Calculate impact level
   */
  private calculateImpact(impact: string | undefined): 'critical' | 'high' | 'medium' | 'low' {
    if (!impact) return 'medium';

    const impactLower = impact.toLowerCase();
    if (impactLower === 'critical' || impactLower === 'high') {
      return impactLower as 'critical' | 'high';
    } else if (impactLower === 'low') {
      return 'low';
    }

    return 'medium';
  }

  /**
   * Generate database-specific action items
   */
  private generateDatabaseActionItems(recommendation: string): string[] {
    const actions: string[] = [];

    if (recommendation.includes('index')) {
      actions.push(
        'Analyze query execution plans',
        'Identify missing indexes',
        'Create composite indexes for common queries',
        'Monitor index usage and effectiveness'
      );
    } else if (recommendation.includes('query')) {
      actions.push(
        'Profile slow queries',
        'Optimize query structure',
        'Consider query result caching',
        'Review N+1 query patterns'
      );
    } else if (recommendation.includes('connection')) {
      actions.push(
        'Review connection pool settings',
        'Implement connection pooling if not present',
        'Monitor connection lifecycle',
        'Optimize connection reuse'
      );
    } else {
      actions.push(recommendation);
    }

    return actions;
  }

  /**
   * Adjust priorities based on current system context
   */
  private adjustPrioritiesBasedOnContext(context: RecommendationContext): void {
    this.recommendations.forEach((rec, id) => {
      let adjustedPriority = rec.priority;

      // Boost priority for high error rates
      if (context.errorRate > 10 && rec.source === 'error') {
        adjustedPriority += 15;
      }

      // Boost priority for high memory usage
      if (context.memoryUsage > 85 && rec.category === 'memory') {
        adjustedPriority += 20;
      }

      // Boost ML recommendations when queue is backed up
      if (context.queueLength > 50 && rec.source === 'ml') {
        adjustedPriority += 10;
      }

      // Cap at 100
      adjustedPriority = Math.min(100, adjustedPriority);

      this.recommendations.set(id, { ...rec, priority: adjustedPriority });
    });
  }

  /**
   * Get recommendations by source
   */
  public getRecommendationsBySource(source: string): UnifiedRecommendation[] {
    return Array.from(this.recommendations.values())
      .filter((rec) => rec.source === source)
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Get high priority recommendations
   */
  public getHighPriorityRecommendations(threshold: number = 80): UnifiedRecommendation[] {
    return Array.from(this.recommendations.values())
      .filter((rec) => rec.priority >= threshold)
      .sort((a, b) => b.priority - a.priority);
  }

  /**
   * Mark recommendation as implemented
   */
  public markAsImplemented(recommendationId: string): boolean {
    const rec = this.recommendations.get(recommendationId);
    if (rec) {
      rec.implemented = true;
      return true;
    }
    return false;
  }

  /**
   * Get recommendation statistics
   */
  public getStatistics(): {
    total: number;
    bySource: Record<string, number>;
    byPriority: { critical: number; high: number; medium: number; low: number };
    implemented: number;
  } {
    const recs = Array.from(this.recommendations.values());

    const bySource: Record<string, number> = {};
    let critical = 0,
      high = 0,
      medium = 0,
      low = 0;
    let implemented = 0;

    recs.forEach((rec) => {
      // Count by source
      bySource[rec.source] = (bySource[rec.source] || 0) + 1;

      // Count by priority
      if (rec.priority >= 90) critical++;
      else if (rec.priority >= 70) high++;
      else if (rec.priority >= 40) medium++;
      else low++;

      // Count implemented
      if (rec.implemented) implemented++;
    });

    return {
      total: recs.length,
      bySource,
      byPriority: { critical, high, medium, low },
      implemented,
    };
  }
}

// Create singleton instance
const recommendationAggregator = new RecommendationAggregatorService();

export default recommendationAggregator;
