/**
 * Memory Manager for Backend
 * Centralized memory management with automatic garbage collection
 */

import logger from './logger';
import { performance } from 'perf_hooks';
import { EventEmitter } from 'events';

interface MemoryThresholds {
  warning: number; // Percentage
  critical: number; // Percentage
  emergency: number; // Percentage
}

interface MemoryStats {
  heapUsed: number;
  heapTotal: number;
  heapLimit: number;
  percentage: number;
  external: number;
  rss: number;
}

class MemoryManager extends EventEmitter {
  private thresholds: MemoryThresholds = {
    warning: 70,
    critical: 85,
    emergency: 95,
  };

  private gcInterval: NodeJS.Timeout | null = null;
  public lastGcTime: number = 0;
  public gcCount: number = 0;
  public isEmergencyMode: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    // Note: We intentionally avoid manual garbage collection
    // as it can interfere with Node.js automatic optimization
    logger.info({ message: 'Memory manager initialized with monitoring-only approach' });
  }

  /**
   * Get current memory statistics
   */
  getMemoryStats(): MemoryStats {
    const usage = process.memoryUsage();
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const v8HeapStats = require('v8').getHeapStatistics();

    const heapUsed = usage.heapUsed;
    const heapTotal = usage.heapTotal;
    const heapLimit = v8HeapStats.heap_size_limit;
    const percentage = (heapUsed / heapLimit) * 100;

    return {
      heapUsed,
      heapTotal,
      heapLimit,
      percentage,
      external: usage.external,
      rss: usage.rss,
    };
  }

  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`;
  }

  /**
   * Monitor memory and suggest optimization instead of manual GC
   * Manual GC can interfere with Node.js optimization
   */
  suggestMemoryOptimization(force: boolean = false): void {
    const now = Date.now();
    const timeSinceLastCheck = now - this.lastGcTime;

    // Prevent too frequent checks (minimum 5 seconds between checks)
    if (!force && timeSinceLastCheck < 5000) {
      return;
    }

    const startTime = performance.now();
    const stats = this.getMemoryStats();

    // Instead of manual GC, suggest memory optimization
    logger.info(
      { message: 'Memory optimization check completed' },
      {
        heapUsed: this.formatBytes(stats.heapUsed),
        heapTotal: this.formatBytes(stats.heapTotal),
        percentage: `${stats.percentage.toFixed(2)}%`,
        external: this.formatBytes(stats.external),
        rss: this.formatBytes(stats.rss),
        suggestion: stats.percentage > 80 ? 'Consider reducing memory usage' : 'Memory usage normal',
      }
    );

    this.lastGcTime = now;
    this.emit('memoryCheck', { stats, timestamp: now });
  }

  /**
   * Check memory usage and trigger GC if needed
   */
  checkMemoryUsage(): void {
    const stats = this.getMemoryStats();

    if (stats.percentage >= this.thresholds.emergency) {
      logger.error('Emergency memory threshold reached', {
        heapUsed: this.formatBytes(stats.heapUsed),
        heapLimit: this.formatBytes(stats.heapLimit),
        percentage: `${stats.percentage.toFixed(2)}%`,
      });

      this.enterEmergencyMode();
      this.suggestMemoryOptimization(true);
    } else if (stats.percentage >= this.thresholds.critical) {
      logger.warn('Critical memory threshold reached', {
        heapUsed: this.formatBytes(stats.heapUsed),
        heapLimit: this.formatBytes(stats.heapLimit),
        percentage: `${stats.percentage.toFixed(2)}%`,
      });

      this.suggestMemoryOptimization(true);
    } else if (stats.percentage >= this.thresholds.warning) {
      logger.debug('Warning memory threshold reached', {
        heapUsed: this.formatBytes(stats.heapUsed),
        percentage: `${stats.percentage.toFixed(2)}%`,
      });

      this.suggestMemoryOptimization(false);
    }

    // Exit emergency mode if memory usage is back to normal
    if (this.isEmergencyMode && stats.percentage < this.thresholds.warning) {
      this.exitEmergencyMode();
    }
  }

  /**
   * Enter emergency mode - aggressive memory management
   */
  private enterEmergencyMode(): void {
    if (this.isEmergencyMode) return;

    this.isEmergencyMode = true;
    logger.warn('Entering emergency memory mode');

    // Clear all caches
    const globalWithCache = global as Record<string, unknown>;
    if (
      globalWithCache.cacheManager &&
      typeof globalWithCache.cacheManager === 'object' &&
      globalWithCache.cacheManager !== null
    ) {
      const cacheManager = globalWithCache.cacheManager as { clearAll?: () => void };
      if (cacheManager.clearAll) {
        cacheManager.clearAll();
      }
    }

    // Reduce connection pool sizes
    if (
      globalWithCache.dbPool &&
      typeof globalWithCache.dbPool === 'object' &&
      globalWithCache.dbPool !== null
    ) {
      const dbPool = globalWithCache.dbPool as { setMaxListeners?: (n: number) => void };
      if (dbPool.setMaxListeners) {
        dbPool.setMaxListeners(5);
      }
    }

    // Disable non-critical features
    this.emit('memory:emergency', true);
  }

  /**
   * Exit emergency mode
   */
  private exitEmergencyMode(): void {
    if (!this.isEmergencyMode) return;

    this.isEmergencyMode = false;
    logger.info({ message: 'Exiting emergency memory mode' });

    // Re-enable features
    this.emit('memory:emergency', false);
  }

  /**
   * Start automatic memory monitoring
   */
  startMonitoring(intervalMs: number = 30000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    logger.info({ message: 'Starting memory monitoring' }, { intervalMs });

    // Initial check
    this.checkMemoryUsage();

    // Regular monitoring
    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs);

    // Periodic memory monitoring (every 5 minutes in normal mode, every 1 minute in emergency)
    this.gcInterval = setInterval(
      () => {
        this.suggestMemoryOptimization(false);
      },
      this.isEmergencyMode ? 60000 : 300000
    );
  }

  /**
   * Stop memory monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.gcInterval) {
      clearInterval(this.gcInterval);
      this.gcInterval = null;
    }

    logger.info({ message: 'Stopped memory monitoring' });
  }

  /**
   * Get memory health status
   */
  getHealthStatus(): { status: string; message: string; stats: MemoryStats } {
    const stats = this.getMemoryStats();

    let status = 'healthy';
    let message = 'Memory usage normal';

    if (stats.percentage >= this.thresholds.emergency) {
      status = 'unhealthy';
      message = 'Emergency memory threshold exceeded';
    } else if (stats.percentage >= this.thresholds.critical) {
      status = 'degraded';
      message = 'Critical memory threshold exceeded';
    } else if (stats.percentage >= this.thresholds.warning) {
      status = 'degraded';
      message = 'Memory usage high';
    }

    return { status, message, stats };
  }

  /**
   * Force clear caches and run GC
   */
  emergencyCleanup(): void {
    logger.warn('Running emergency memory cleanup');

    // Clear all caches
    const globalWithCache = global as Record<string, unknown>;
    if (
      globalWithCache.cacheManager &&
      typeof globalWithCache.cacheManager === 'object' &&
      globalWithCache.cacheManager !== null
    ) {
      const cacheManager = globalWithCache.cacheManager as { clearAll?: () => void };
      if (cacheManager.clearAll) {
        cacheManager.clearAll();
      }
    }

    // Check memory and suggest optimization
    this.suggestMemoryOptimization(true);

    // Clear require cache for non-critical modules
    Object.keys(require.cache).forEach((key) => {
      if (key.includes('node_modules') && !key.includes('express')) {
        delete require.cache[key];
      }
    });
  }
}

// Export singleton instance
export const memoryManager = new MemoryManager();
export default memoryManager;
