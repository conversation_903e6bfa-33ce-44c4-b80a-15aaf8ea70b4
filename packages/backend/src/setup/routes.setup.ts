/**
 * Routes Setup for Production Server
 * Configures all API routes using modular route system
 */

import express, { Express, Request, Response } from 'express';
import { Server as SocketIOServer } from 'socket.io';
import { ProductionConfig } from '../config/production.config';
import { createLogger } from '../utils/logger';

// Import modular routes
import { createAuthRoutes } from '../routes/auth';
import { createProjectRoutes } from '../routes/projects';
import { createUserRoutes } from '../routes/users';
import { createImageRoutes } from '../routes/images';
import { createAdminRoutes } from '../routes/admin';
import { createHealthRoutes } from '../routes/health';

const logger = createLogger('RoutesSetup');

export interface RouteContext {
  io?: SocketIOServer;
  config: ProductionConfig;
}

export function setupRoutes(app: Express, context: RouteContext): void {
  const { io: _io, config } = context;

  // API version prefix
  const API_PREFIX = '/api';

  // Health check routes (no auth required)
  app.use(`${API_PREFIX}/health`, createHealthRoutes());
  logger.info({ message: 'Health routes configured' });

  // Authentication routes
  app.use(`${API_PREFIX}/auth`, createAuthRoutes());
  logger.info({ message: 'Auth routes configured' });

  // User routes
  app.use(`${API_PREFIX}/users`, createUserRoutes());
  logger.info({ message: 'User routes configured' });

  // Project routes
  app.use(`${API_PREFIX}/projects`, createProjectRoutes());
  logger.info({ message: 'Project routes configured' });

  // Image routes
  app.use(`${API_PREFIX}/images`, createImageRoutes());
  logger.info({ message: 'Image routes configured' });

  // Admin routes (protected)
  app.use(`${API_PREFIX}/admin`, createAdminRoutes());
  logger.info({ message: 'Admin routes configured' });

  // API documentation endpoint
  app.get(`${API_PREFIX}/docs`, (req: Request, res: Response) => {
    res.json({
      version: '1.0.0',
      endpoints: {
        auth: {
          login: 'POST /api/auth/login',
          register: 'POST /api/auth/register',
          logout: 'POST /api/auth/logout',
          refresh: 'POST /api/auth/refresh',
          forgotPassword: 'POST /api/auth/forgot-password',
          resetPassword: 'POST /api/auth/reset-password',
        },
        users: {
          profile: 'GET /api/users/me',
          updateProfile: 'PATCH /api/users/me',
          changePassword: 'POST /api/users/me/change-password',
          deleteAccount: 'DELETE /api/users/me',
        },
        projects: {
          list: 'GET /api/projects',
          create: 'POST /api/projects',
          get: 'GET /api/projects/:id',
          update: 'PATCH /api/projects/:id',
          delete: 'DELETE /api/projects/:id',
          share: 'POST /api/projects/:id/share',
          duplicate: 'POST /api/projects/:id/duplicate',
          export: 'GET /api/projects/:id/export',
          stats: 'GET /api/projects/:id/stats',
        },
        images: {
          list: 'GET /api/projects/:projectId/images',
          upload: 'POST /api/projects/:projectId/images',
          get: 'GET /api/images/:id',
          update: 'PATCH /api/images/:id',
          delete: 'DELETE /api/images/:id',
          segment: 'POST /api/images/:id/segment',
          segmentationStatus: 'GET /api/images/:id/segmentation',
          download: 'GET /api/images/:id/download',
        },
        admin: {
          stats: 'GET /api/admin/stats',
          users: 'GET /api/admin/users',
          projects: 'GET /api/admin/projects',
          system: 'GET /api/admin/system',
          logs: 'GET /api/admin/logs',
        },
        health: {
          status: 'GET /api/health',
          ready: 'GET /api/health/ready',
          live: 'GET /api/health/live',
        },
      },
    });
  });

  // 404 handler for API routes
  app.use(`${API_PREFIX}/*`, (req: Request, res: Response) => {
    res.status(404).json({
      error: 'Not Found',
      message: `API endpoint ${req.originalUrl} not found`,
      timestamp: new Date().toISOString(),
    });
  });

  // Serve frontend in production
  if (config.nodeEnv === 'production') {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const frontendPath = require('path').join(__dirname, '../../../frontend/dist');
    app.use(express.static(frontendPath));

    // Catch-all handler - send React app for any non-API route
    app.get('*', (req: Request, res: Response) => {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      res.sendFile(require('path').join(frontendPath, 'index.html'));
    });

    logger.info({ message: 'Frontend static files configured' });
  }

  logger.info({ message: 'All routes configured successfully' });
}
