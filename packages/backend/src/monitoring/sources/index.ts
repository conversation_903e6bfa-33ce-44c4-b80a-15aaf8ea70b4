/**
 * Performance Monitoring Sources
 *
 * Collection of optimized monitoring sources for different system components.
 * Each source is designed for minimal overhead and maximum insight.
 */

export { default as SystemResourceSource } from './systemResource.source';
export { default as DatabaseSource } from './database.source';

// TODO: Implement missing monitoring sources
// export { default as ApiEndpointSource } from './apiEndpoint.source';
// export { default as CacheSource } from './cache.source';
// export { default as QueueSource } from './queue.source';
// export { default as WebSocketSource } from './websocket.source';
// export { default as MLServiceSource } from './mlService.source';
// export { default as ErrorTrackingSource } from './errorTracking.source';

// Import and re-export types from stub
import type { PerformanceMetric } from '../performanceTracker.stub';
export type { PerformanceMetric } from '../performanceTracker.stub';

// MonitoringSource interface
export interface MonitoringSource {
  id: string;
  name: string;
  priority: 'low' | 'medium' | 'high';
  enabled: boolean;
  intervalMs: number;
  lastCollection?: number;
  collectMetrics(): Promise<PerformanceMetric[]>;
}
