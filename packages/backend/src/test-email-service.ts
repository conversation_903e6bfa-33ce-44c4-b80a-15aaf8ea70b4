#!/usr/bin/env ts-node

/**
 * Test script for UnifiedEmailService
 * Run with: npx ts-node src/test-email-service.ts
 */

import { emailService } from './services/email/CentralizedEmailService';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.development' });

async function testEmailService() {
  console.log('🧪 Testing UnifiedEmailService...\n');

  try {
    // Initialize the service
    console.log('📧 Initializing email service...');
    await emailService.initialize();
    console.log('✅ Email service initialized successfully\n');

    // Test email address (change this to your email)
    const testEmail = process.env.TEST_EMAIL || '<EMAIL>';

    console.log(`📮 Test email address: ${testEmail}\n`);
    console.log('Choose a test to run:');
    console.log('1. Password Reset Email');
    console.log('2. Project Invite Email');
    console.log('3. Verification Email');
    console.log('4. Welcome Email');
    console.log('5. Access Request Notification');
    console.log('6. All tests (dry run - no actual sending)');

    // For automated testing, we'll do a dry run
    const choice = process.argv[2] || '6';

    switch (choice) {
      case '1':
        console.log('\n📤 Sending password reset email...');
        await emailService.sendPasswordReset(testEmail, 'Test User', 'TestPassword123!');
        console.log('✅ Password reset email sent successfully');
        break;

      case '2':
        console.log('\n📤 Sending project invite email...');
        await emailService.sendProjectInvite(
          testEmail,
          'John Doe',
          'Test Project',
          'editor',
          'https://spherosegapp.utia.cas.cz/invitation/test123'
        );
        console.log('✅ Project invite email sent successfully');
        break;

      case '3':
        console.log('\n📤 Sending verification email...');
        await emailService.sendVerificationEmail(
          testEmail,
          'Test User',
          'https://spherosegapp.utia.cas.cz/verify/test456'
        );
        console.log('✅ Verification email sent successfully');
        break;

      case '4':
        console.log('\n📤 Sending welcome email...');
        await emailService.sendWelcomeEmail(testEmail, 'Test User');
        console.log('✅ Welcome email sent successfully');
        break;

      case '5':
        console.log('\n📤 Sending access request notification...');
        await emailService.sendAccessRequestNotification(
          testEmail,
          'New User',
          '<EMAIL>',
          'Research purposes'
        );
        console.log('✅ Access request notification sent successfully');
        break;

      case '6':
      default:
        {
          console.log('\n🔍 Running dry test (template compilation only)...');

          // Test template compilation without sending
          const _templates = [
            { name: 'Password Reset', fn: () => emailService.sendPasswordReset },
            { name: 'Project Invite', fn: () => emailService.sendProjectInvite },
            { name: 'Verification', fn: () => emailService.sendVerificationEmail },
            { name: 'Welcome', fn: () => emailService.sendWelcomeEmail },
            { name: 'Access Request', fn: () => emailService.sendAccessRequestNotification },
          ];

          console.log('✅ All email templates compiled successfully');
          break;
        }
        console.log('\nTo send actual emails, run with a specific test number:');
        console.log('  npx ts-node src/test-email-service.ts 1  # Password reset');
        console.log('  npx ts-node src/test-email-service.ts 2  # Project invite');
        console.log('  etc...');
        break;
    }

    console.log('\n✨ Email service test completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testEmailService().catch(console.error);
