/**
 * Performance Monitoring Startup Module
 *
 * Initializes the consolidated performance monitoring system.
 * This replaces the previous performance coordinator system with
 * our unified performance middleware.
 */

import { Redis } from 'ioredis';
import logger from '../utils/logger';
import { 
  performanceMonitor, 
  getPerformanceInsights, 
  getCurrentSystemMetrics 
} from '../middleware/performance';

let isInitialized = false;

export async function initializePerformanceCoordinatorOnStartup(redisClient: Redis): Promise<void> {
  try {
    logger.info({ message: 'Initializing consolidated performance monitoring system...' });

    if (isInitialized) {
      logger.info({ message: 'Performance monitoring already initialized, skipping...' });
      return;
    }

    // Start the consolidated performance monitoring
    performanceMonitor.startMonitoring({
      enabled: true,
      enablePrometheus: true,
      enableMemoryMonitoring: true,
      enableDatabaseMonitoring: true,
      enableInsights: true,
      enableOptimizations: true,
      enableBatching: true,
      batchSize: 200,
      flushIntervalMs: 20000,
      memoryWarningThreshold: 75,
      memoryErrorThreshold: 85,
    });

    // Set up event listeners for performance insights
    performanceMonitor.on('insight', (insight) => {
      if (insight.severity === 'critical') {
        logger.warn('Critical performance insight generated', {
          title: insight.title,
          description: insight.description,
          recommendation: insight.recommendation,
        });
      }
    });

    performanceMonitor.on('memoryPressure', (data) => {
      logger.warn('Memory pressure detected', {
        level: data.level,
        heapUsed: data.usage.heapUsed,
        heapTotal: data.usage.heapTotal,
      });
    });

    // Setup graceful shutdown
    const gracefulShutdown = async () => {
      logger.info({ message: 'Shutting down performance monitoring...' });
      performanceMonitor.stopMonitoring();
    };

    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

    isInitialized = true;

    logger.info({ message: 'Consolidated performance monitoring initialized successfully' });
  } catch (error) {
    logger.error('Failed to initialize performance monitoring', {
      error: error instanceof Error ? error.message : String(error),
    });
    throw error;
  }
}

// Compatibility functions for existing code
export function getPerformanceCoordinator() {
  return performanceMonitor;
}

export async function getPerformanceCoordinatorReport() {
  const insights = getPerformanceInsights();
  const systemMetrics = getCurrentSystemMetrics();
  
  return {
    insights,
    systemMetrics,
    summary: {
      totalInsights: insights.length,
      criticalIssues: insights.filter(i => i.severity === 'critical').length,
      highPriorityIssues: insights.filter(i => i.severity === 'high').length,
      systemHealth: systemMetrics ? 'healthy' : 'unknown',
    },
    timestamp: new Date().toISOString(),
  };
}

export function getPerformanceCoordinatorSourceStatus() {
  return [{
    id: 'consolidated-monitor',
    name: 'Consolidated Performance Monitor',
    enabled: true,
    status: 'active',
    lastUpdate: new Date().toISOString(),
  }];
}

export default {
  initializePerformanceCoordinatorOnStartup,
  getPerformanceCoordinator,
  getPerformanceCoordinatorReport,
  getPerformanceCoordinatorSourceStatus,
};