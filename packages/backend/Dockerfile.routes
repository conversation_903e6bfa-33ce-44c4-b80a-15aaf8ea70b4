FROM node:20-alpine

WORKDIR /app

# Install dependencies
RUN apk add --no-cache postgresql-client curl

# Install runtime and TypeScript packages globally  
RUN npm install -g express@4.17.1 cors@2.8.5 compression@1.8.0 helmet@7.2.0 \
    express-rate-limit@7.5.1 cookie-parser@1.4.7 express-session@1.18.1 \
    connect-redis@7.1.1 redis@4.7.1 morgan@1.10.0 pg@8.15.6 \
    bcryptjs@2.4.3 jsonwebtoken@9.0.2 multer@2.0.2 sharp@0.34.3 \
    dotenv@16.5.0 @types/node nodemailer @types/nodemailer \
    zod@3.25.1 uuid@10.0.0 mime-types@2.1.35 \
    ts-node typescript

# Copy all backend source files
COPY packages/backend/src packages/backend/src/

# Create necessary directories
RUN mkdir -p uploads logs

# Create minimal tsconfig.json for ts-node
RUN echo '{"compilerOptions":{"target":"ES2020","module":"commonjs","moduleResolution":"node","esModuleInterop":true,"allowSyntheticDefaultImports":true,"skipLibCheck":true,"strict":false},"ts-node":{"transpileOnly":true}}' > tsconfig.json

ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=768"
ENV NODE_PATH="/usr/local/lib/node_modules"

EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:5001/api/health || exit 1

# Use ts-node to run TypeScript directly
CMD ["ts-node", "--transpile-only", "packages/backend/src/server.ts"]