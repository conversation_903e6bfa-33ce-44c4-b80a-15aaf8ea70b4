/**
 * Jest Configuration for E2E Tests
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/e2e'],
  testMatch: [
    '**/e2e/**/*.test.ts',
    '**/e2e/**/*.e2e.test.ts'
  ],
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts',
    '!src/**/index.ts'
  ],
  coverageDirectory: '<rootDir>/coverage/e2e',
  coverageReporters: ['text', 'lcov', 'html'],
  globalSetup: '<rootDir>/e2e/setup/global-setup.ts',
  globalTeardown: '<rootDir>/e2e/setup/global-teardown.ts',
  setupFilesAfterEnv: ['<rootDir>/e2e/setup/test-setup.ts'],
  testTimeout: 30000,
  maxWorkers: 1, // Run tests sequentially for E2E
  verbose: true,
  bail: false,
  detectOpenHandles: true,
  forceExit: true,
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@e2e/(.*)$': '<rootDir>/e2e/$1'
  }
};