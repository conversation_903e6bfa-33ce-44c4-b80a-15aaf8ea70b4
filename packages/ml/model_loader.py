"""
Optimized ML Model Loader with caching and lazy loading
"""
import os
import hashlib
import pickle
import time
from pathlib import Path
from typing import Optional, Dict, Any
import torch
import logging
from functools import lru_cache
import requests
from tqdm import tqdm

logger = logging.getLogger(__name__)

class ModelLoader:
    """Optimized model loader with caching and external storage support"""
    
    def __init__(self, cache_dir: str = "/tmp/ml_models"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._model_cache: Dict[str, Any] = {}
        self.model_urls = {
            "checkpoint_epoch_9": os.getenv(
                "ML_MODEL_URL",
                "https://storage.googleapis.com/spheroseg-models/checkpoint_epoch_9.pth.tar"
            )
        }
    
    def _get_cache_path(self, model_name: str) -> Path:
        """Get cache path for model"""
        return self.cache_dir / f"{model_name}.pth.tar"
    
    def _download_model(self, model_name: str, url: str) -> Path:
        """Download model from external storage with progress bar"""
        cache_path = self._get_cache_path(model_name)
        
        if cache_path.exists():
            # Verify checksum if available
            if self._verify_checksum(cache_path, model_name):
                logger.info(f"Model {model_name} found in cache")
                return cache_path
        
        logger.info(f"Downloading model {model_name} from {url}")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(cache_path, 'wb') as f:
                with tqdm(total=total_size, unit='B', unit_scale=True, desc=model_name) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            logger.info(f"Model {model_name} downloaded successfully")
            return cache_path
            
        except Exception as e:
            logger.error(f"Failed to download model {model_name}: {e}")
            # Fallback to local model if exists
            local_path = Path(__file__).parent / f"{model_name}.pth.tar"
            if local_path.exists():
                logger.warning(f"Using local model at {local_path}")
                return local_path
            raise
    
    def _verify_checksum(self, file_path: Path, model_name: str) -> bool:
        """Verify model checksum"""
        expected_checksums = {
            "checkpoint_epoch_9": "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"
        }
        
        if model_name not in expected_checksums:
            return True  # No checksum available
        
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        
        actual = sha256_hash.hexdigest()
        expected = expected_checksums[model_name]
        
        if actual != expected:
            logger.warning(f"Checksum mismatch for {model_name}: {actual} != {expected}")
            return False
        
        return True
    
    @lru_cache(maxsize=2)
    def load_model(self, model_name: str = "checkpoint_epoch_9", device: str = "cuda") -> Any:
        """Load model with caching and lazy loading"""
        
        # Check in-memory cache first
        cache_key = f"{model_name}_{device}"
        if cache_key in self._model_cache:
            logger.info(f"Loading model {model_name} from memory cache")
            return self._model_cache[cache_key]
        
        start_time = time.time()
        
        # Get model path (download if necessary)
        if model_name in self.model_urls:
            model_path = self._download_model(model_name, self.model_urls[model_name])
        else:
            model_path = Path(__file__).parent / f"{model_name}.pth.tar"
        
        if not model_path.exists():
            raise FileNotFoundError(f"Model {model_name} not found at {model_path}")
        
        # Load model with optimizations
        logger.info(f"Loading model from {model_path}")
        
        # Use map_location to load directly to target device
        if device == "cuda" and not torch.cuda.is_available():
            device = "cpu"
            logger.warning("CUDA not available, falling back to CPU")
        
        checkpoint = torch.load(
            model_path,
            map_location=device,
            weights_only=False  # Set to True if possible for security
        )
        
        # Extract model from checkpoint
        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                model = checkpoint['model']
            elif 'state_dict' in checkpoint:
                # Create model instance and load state dict
                from model import ResUNet  # Import your model class
                model = ResUNet()
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model = checkpoint
        else:
            model = checkpoint
        
        # Move to device and set to eval mode
        if hasattr(model, 'to'):
            model = model.to(device)
        if hasattr(model, 'eval'):
            model.eval()
        
        # Enable mixed precision if available
        if device == "cuda":
            try:
                model = model.half()  # Use FP16 for faster inference
                logger.info("Using FP16 precision for faster inference")
            except:
                logger.info("FP16 not supported, using FP32")
        
        # Cache the model
        self._model_cache[cache_key] = model
        
        load_time = time.time() - start_time
        logger.info(f"Model {model_name} loaded in {load_time:.2f} seconds")
        
        return model
    
    def preload_model(self, model_name: str = "checkpoint_epoch_9", device: str = "cuda"):
        """Preload model into cache"""
        self.load_model(model_name, device)
    
    def clear_cache(self):
        """Clear model cache to free memory"""
        self._model_cache.clear()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        logger.info("Model cache cleared")
    
    def get_model_info(self, model_name: str = "checkpoint_epoch_9") -> Dict[str, Any]:
        """Get model information without loading it"""
        model_path = self._get_cache_path(model_name)
        
        if not model_path.exists():
            model_path = Path(__file__).parent / f"{model_name}.pth.tar"
        
        if not model_path.exists():
            return {"error": "Model not found"}
        
        return {
            "name": model_name,
            "path": str(model_path),
            "size_mb": model_path.stat().st_size / (1024 * 1024),
            "cached": model_name in self._model_cache,
            "last_modified": time.ctime(model_path.stat().st_mtime)
        }

# Global model loader instance
model_loader = ModelLoader()

# Preload model on startup if configured
if os.getenv("PRELOAD_MODEL", "false").lower() == "true":
    model_loader.preload_model()