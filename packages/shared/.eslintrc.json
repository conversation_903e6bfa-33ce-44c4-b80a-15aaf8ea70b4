{
  "root": false,
  "extends": ["../../.eslintrc.cjs"],
  "env": {
    "browser": true,
    "node": true
  },
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.eslint.json",
    "tsconfigRootDir": "."
  },
  "ignorePatterns": ["dist", ".eslintrc.json", "node_modules", "coverage", "*.config.ts", "*.config.js"],
  "rules": {
    // Shared package should be more strict since it's used everywhere
    "@typescript-eslint/no-explicit-any": "error",
    "no-console": "error"
  },
  "overrides": [
    {
      "files": ["**/test-utils/**/*", "**/*.test.ts", "**/*.test.tsx"],
      "rules": {
        "@typescript-eslint/no-explicit-any": "warn",
        "no-console": "off"
      }
    }
  ]
}