// Shared package - Single Source of Truth for types and utilities

// Re-export all entity types (SSOT)
export * from './types/entities';
export * from './types';

// Re-export utilities
export * from './utils';
export * from './utils/polygonUtils.unified';
export * as polygonUtils from './utils/polygonUtils';

// Export specific polygon utilities for easier access
export { 
  slicePolygon, 
  createPolygon,
  calculatePolygonArea as calculatePolygonAreaExport
} from './utils/polygonUtils.unified';

// Export specific classes and utilities that are commonly used
export { PolygonBoundingBoxCache, polygonBoundingBoxCache, calculateBoundingBox, isBoxVisible } from './utils/polygonUtils.unified';

// Export API paths constants
export const API_PATHS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    VERIFY_EMAIL: '/auth/verify-email',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    PROFILE: '/auth/profile',
  },
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    STATISTICS: '/users/statistics',
    EXTENDED_STATISTICS: '/users/extended-statistics',
    SETTINGS: '/users/settings',
  },
  PROJECTS: {
    BASE: '/projects',
    IMAGES: (projectId: string | number) => `/projects/${projectId}/images`,
    SHARE: (projectId: string | number) => `/projects/${projectId}/share`,
    DOWNLOAD: (projectId: string | number) => `/projects/${projectId}/download`,
  },
  SEGMENTATION: {
    BASE: '/segmentation',
    PROCESS: '/segmentation/process',
    STATUS: (taskId: string) => `/segmentation/status/${taskId}`,
    RESULT: (taskId: string) => `/segmentation/result/${taskId}`,
    QUEUE: '/segmentation/queue',
  },
  IMAGES: {
    BASE: '/images',
    UPLOAD: '/images/upload',
    BY_ID: (imageId: string | number) => `/images/${imageId}`,
    DOWNLOAD: (imageId: string | number) => `/images/${imageId}/download`,
  },
  UPLOADS: {
    BASE: '/uploads',
    IMAGES: '/uploads/images',
  },
  SYSTEM: {
    HEALTH: '/health',
    STATUS: '/status',
    VERSION: '/version',
  },
} as const;

// Export segmentation status constants
export const SEGMENTATION_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
} as const;

// Basic utility functions
export const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

// Polygon utilities (types are exported from ./types)
import type { Point } from './types';

// Basic polygon utility functions
export const distance = (p1: Point, p2: Point): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
};

export const calculatePolygonArea = (points: Point[]): number => {
  let area = 0;
  for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
    area += (points[j].x + points[i].x) * (points[j].y - points[i].y);
  }
  return Math.abs(area) / 2;
};

export const isPointInPolygon = (point: Point, polygon: Point[]): boolean => {
  let isInside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
        (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
      isInside = !isInside;
    }
  }
  return isInside;
};

// Default export
export default {
  API_PATHS,
  SEGMENTATION_STATUS,
  generateRequestId,
  distance,
  calculatePolygonArea,
  isPointInPolygon,
};