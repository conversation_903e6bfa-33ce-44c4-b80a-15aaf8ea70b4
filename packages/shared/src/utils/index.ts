/**
 * Shared utilities index
 * Re-exports all polygon utilities from the consolidated polygonUtils module
 */

// Import and re-export everything from the consolidated polygon utilities
export * from './polygonUtils';

// Default export for convenience
export { default as polygonUtils } from './polygonUtils';

// Export specific functions that are commonly used
export { isPointInPolygon, PolygonBoundingBoxCache, polygonBoundingBoxCache } from './polygonUtils.unified';

// Export path utilities
export * from './pathUtils';

// Export zoom handlers
export * from './zoomHandlers';

// Export image utilities
export * from './imageUtils';
export { default as imageUtils } from './imageUtils';
