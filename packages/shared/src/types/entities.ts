/**
 * Canonical Entity Type Definitions
 * 
 * SINGLE SOURCE OF TRUTH for all entity types
 * All other packages MUST import from here
 * NO local entity type definitions allowed
 */

// ============================================================================
// USER TYPES
// ============================================================================

export interface User {
  id: string;
  email: string;
  name?: string;
  role?: 'user' | 'admin';
  created_at?: string;
  updated_at?: string;
}

export interface UserProfile {
  user_id: string;
  username: string | null;
  full_name: string | null;
  title: string | null;
  organization: string | null;
  bio: string | null;
  location: string | null;
  avatar_url: string | null;
  preferred_language: string | null;
  preferred_theme?: 'light' | 'dark' | 'system' | null;
  storage_limit_bytes?: number | null;
  storage_used_bytes?: number | null;
}

export type UserProfileUpdatePayload = Partial<Omit<UserProfile, 'user_id'>>;

// ============================================================================
// PROJECT TYPES
// ============================================================================

export interface Project {
  id: string;
  user_id: string;
  title: string;
  description: string | null;
  created_at: string;
  updated_at: string;
  thumbnail_url?: string | null;
  image_count?: number;
}

export type ProjectCreatePayload = Pick<Project, 'title' | 'description'>;
export type ProjectUpdatePayload = Partial<Pick<Project, 'title' | 'description'>>;

export interface ProjectShare {
  id: string;
  project_id: string;
  shared_with_user_id: string;
  permission: 'view' | 'edit' | 'admin';
  created_at: string;
  updated_at: string;
}

export interface ProjectStats {
  user_id: string;
  project_count: number;
  image_count: number;
  segmentation_count: number;
  recently_updated_projects: Array<{
    id: string;
    title: string;
    updated_at: string;
  }>;
  storage_usage: {
    total_bytes: number;
    images_bytes: number;
    segmentations_bytes: number;
  };
}

// ============================================================================
// IMAGE TYPES
// ============================================================================

export type ImageStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'queued';

export interface Image {
  id: string;
  project_id: string;
  user_id: string;
  name: string;
  storage_path: string;
  thumbnail_path: string | null;
  width: number | null;
  height: number | null;
  file_size?: number;
  mime_type?: string;
  metadata: Record<string, unknown> | null;
  status: ImageStatus;
  created_at: string;
  updated_at: string;
}

export interface ImageWithSegmentation extends Image {
  segmentation_result?: {
    id?: string;
    path?: string | null;
    result_path?: string | null;
    created_at?: string;
    confidence?: number;
    processing_time_ms?: number;
  } | null;
}

// ============================================================================
// SEGMENTATION TYPES
// ============================================================================

export interface SegmentationTask {
  id: string;
  image_id: string;
  project_id: string;
  user_id: string;
  status: ImageStatus;
  error_message?: string | null;
  started_at?: string | null;
  completed_at?: string | null;
  processing_time_ms?: number | null;
  model_version?: string;
  created_at: string;
  updated_at: string;
}

export interface SegmentationResult {
  id: string;
  task_id: string;
  image_id: string;
  result_path: string;
  confidence_score?: number;
  cell_count?: number;
  metadata?: Record<string, unknown>;
  created_at: string;
}

// ============================================================================
// AUTHENTICATION TYPES
// ============================================================================

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends LoginCredentials {
  name?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken?: string;
  user: User;
  expiresAt?: string;
}

export interface RefreshTokenResponse {
  token: string;
  expires_at: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  password: string;
}

// ============================================================================
// JWT TYPES
// ============================================================================

export interface JWTPayload {
  userId: string;
  email: string;
  role?: string;
  iat?: number;
  exp?: number;
  sub?: string;
}

export interface JWK {
  kty: string;
  kid: string;
  use: string;
  alg: string;
  n?: string;
  e?: string;
  x5c?: string[];
  x5t?: string;
  [key: string]: any;
}

export interface JWKS {
  keys: JWK[];
}

// ============================================================================
// ACCESS REQUEST TYPES
// ============================================================================

export interface AccessRequest {
  id: string;
  email: string;
  name?: string;
  organization?: string;
  reason?: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: string;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export type AccessRequestPayload = Omit<AccessRequest, 'id' | 'status' | 'reviewed_by' | 'reviewed_at' | 'created_at' | 'updated_at'>;

// ============================================================================
// API RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, unknown>;
  };
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

export interface PaginatedResponse<T> {
  items: T[];
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// ============================================================================
// COMMON TYPES
// ============================================================================

export interface Timestamped {
  created_at: string;
  updated_at: string;
}

export interface Identifiable {
  id: string;
}

export type WithTimestamps<T> = T & Timestamped;
export type WithId<T> = T & Identifiable;

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;
export type Nullable<T> = T | null;
export type Undefinable<T> = T | undefined;

// ============================================================================
// EXPORT ALL AS NAMESPACE FOR CONVENIENCE
// ============================================================================

export * as Entities from './entities';