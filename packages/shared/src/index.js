// Fallback JavaScript export for shared module
// This helps with build issues when TypeScript compilation fails

module.exports = {
  // Re-export everything that might be needed
  polygonUtils: {},
  imageUtils: {},
  pathUtils: {},
  
  // Constants
  SEGMENTATION_STATUS: {
    QUEUED: 'queued',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    FAILED: 'failed',
    WITHOUT_SEGMENTATION: 'without_segmentation'
  },
  
  // Mock functions for now
  distance: () => 0,
  calculateBoundingBox: () => ({ minX: 0, minY: 0, maxX: 0, maxY: 0 }),
  calculatePolygonArea: () => 0,
  isPointInPolygon: () => false,
  
  // Logger fallback
  logger: {
    info: console.log,
    error: console.error,
    warn: console.warn,
    debug: console.debug
  }
};