// API path constants for consistent endpoint definitions
// This file serves as the Single Source of Truth for all API paths

export const API_PATHS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    REFRESH: '/auth/refresh',
    VERIFY_EMAIL: '/auth/verify-email',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    PROFILE: '/auth/profile',
  },

  // User management endpoints
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    STATISTICS: '/users/statistics',
    EXTENDED_STATISTICS: '/users/extended-statistics',
    SETTINGS: '/users/settings',
  },

  // Project endpoints
  PROJECTS: {
    BASE: '/projects',
    IMAGES: (projectId: string | number) => `/projects/${projectId}/images`,
    SHARE: (projectId: string | number) => `/projects/${projectId}/share`,
    DOWNLOAD: (projectId: string | number) => `/projects/${projectId}/download`,
  },

  // Segmentation endpoints
  SEGMENTATION: {
    BASE: '/segmentation',
    PROCESS: '/segmentation/process',
    STATUS: (taskId: string) => `/segmentation/status/${taskId}`,
    RESULT: (taskId: string) => `/segmentation/result/${taskId}`,
    QUEUE: '/segmentation/queue',
  },

  // Image endpoints
  IMAGES: {
    BASE: '/images',
    UPLOAD: '/images/upload',
    BY_ID: (imageId: string | number) => `/images/${imageId}`,
    DOWNLOAD: (imageId: string | number) => `/images/${imageId}/download`,
  },

  // File upload endpoints
  UPLOADS: {
    BASE: '/uploads',
    IMAGES: '/uploads/images',
  },

  // System endpoints
  SYSTEM: {
    HEALTH: '/health',
    STATUS: '/status',
    VERSION: '/version',
  },
} as const;

// Export individual path groups for convenience
export const { AUTH, USERS, PROJECTS, SEGMENTATION, IMAGES, UPLOADS, SYSTEM } = API_PATHS;

// Type for API paths
export type ApiPaths = typeof API_PATHS;

// Default export for convenience
export default API_PATHS;