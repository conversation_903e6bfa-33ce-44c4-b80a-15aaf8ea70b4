{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2020", "DOM", "DOM.Iterable"], "declaration": true, "outDir": "dist", "jsx": "react-jsx", "baseUrl": ".", "paths": {"@shared/*": ["src/*"], "@/*": ["src/*"]}, "noEmit": false, "allowImportingTsExtensions": false, "exactOptionalPropertyTypes": false, "types": ["vitest/globals", "node", "dompurify"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "../../node_modules/@types/jest"]}