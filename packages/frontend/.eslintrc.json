{
  "root": false,
  "extends": ["../../.eslintrc.cjs"],
  "parserOptions": {
    "ecmaVersion": "latest",
    "sourceType": "module",
    "project": "./tsconfig.json",
    "tsconfigRootDir": "."
  },
  "plugins": ["react-refresh", "unused-imports"],
  "ignorePatterns": ["dist", ".eslintrc.json", "node_modules", "coverage", "playwright-report", "test-results", "e2e/**/*", "eslint-rules/**/*", "*.config.ts", "*.config.js", "public/**/*", "scripts/**/*", "test-cache-reporter.ts", "vite-static-fix.js", "vite.config.enhanced.ts", "vite-*.js", "vite-plugin-*.ts", "debug-config.js", "vitest.config.example.ts"],
  "rules": {
    // Frontend-specific rules
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "@typescript-eslint/no-empty-object-type": "warn",
    
    // Unused imports (frontend-specific plugin)
    "unused-imports/no-unused-imports": "error",
    "unused-imports/no-unused-vars": [
      "warn",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ]
  },
  "overrides": [
    {
      "files": ["vite.config.ts", "vitest.config.ts", "playwright.config.ts"],
      "parserOptions": {
        "project": null
      },
      "rules": {}
    },
    {
      "files": ["**/serviceWorkerRegistration.ts", "**/*visual-regression*/**/*", "**/*setupVisualRegression*"],
      "rules": {
        "react-hooks/rules-of-hooks": "off"
      }
    }
  ]
}