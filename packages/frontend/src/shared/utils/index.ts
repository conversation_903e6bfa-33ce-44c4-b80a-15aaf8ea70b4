// Import utility functions from polygon operations
import {
  calculateBoundingBox,
  isPointInPolygon,
  calculatePolygonArea,
  calculatePolygonPerimeter,
  simplifyPolygon,
  slicePolygon,
  WorkerRequest,
  WorkerResponse,
} from '@spheroseg/shared';

// Import utility functions from polygon worker
import {
  generateRequestId,
  createWorkerRequest,
  createWorkerResponse,
} from '@spheroseg/shared';

// Import utility functions from polygon slicing
import {
  getPointSideOfLine,
  distanceToLineSegment,
  createPolygon,
} from './polygonSlicingUtils';

// Re-export functions and values
export {
  // From polygonOperationsUtils
  calculateBoundingBox,
  isPointInPolygon,
  calculatePolygonArea,
  calculatePolygonPerimeter,
  simplifyPolygon,
  slicePolygon,

  // From polygonWorkerUtils
  generateRequestId,
  createWorkerRequest,
  createWorkerResponse,

  // From polygonSlicingUtils
  getPointSideOfLine,
  distanceToLineSegment,
  createPolygon,
};

// Re-export types
export type { WorkerRequest, WorkerResponse };
