/**
 * Testing Dashboard Component
 * Real-time monitoring and debugging interface for development
 */

import React, { useState, useEffect, useCallback, useRef } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  AlertCircle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Wifi,
  WifiOff,
  Bug,
} from "lucide-react";
import { toast } from "sonner";

interface ConsoleEntry {
  id: string;
  type: "log" | "info" | "warn" | "error";
  message: string;
  timestamp: Date;
  count: number;
  stack?: string;
}

interface NetworkEntry {
  id: string;
  method: string;
  url: string;
  status: number;
  duration: number;
  timestamp: Date;
  size?: number;
  error?: string;
}

interface HealthStatus {
  service: string;
  status: "healthy" | "degraded" | "down";
  lastCheck: Date;
  responseTime?: number;
  error?: string;
}

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  trend?: "up" | "down" | "stable";
}

export const TestingDashboard: React.FC = () => {
  const [isMinimized, setIsMinimized] = useState(false);
  const [consoleEntries, setConsoleEntries] = useState<ConsoleEntry[]>([]);
  const [networkEntries, setNetworkEntries] = useState<NetworkEntry[]>([]);
  const [healthStatuses, setHealthStatuses] = useState<HealthStatus[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<
    PerformanceMetric[]
  >([]);
  const [wsStatus, setWsStatus] = useState<"connected" | "disconnected">(
    "disconnected",
  );
  const [errorCount, setErrorCount] = useState(0);
  const [warningCount, setWarningCount] = useState(0);

  const originalConsole = useRef<{
    log: typeof console.log;
    info: typeof console.info;
    warn: typeof console.warn;
    error: typeof console.error;
  }>();

  // Override console methods to capture logs (with compatibility check)
  useEffect(() => {
    // Store the current console methods (which might already be overridden)
    const currentConsole = {
      log: console.log,
      info: console.info,
      warn: console.warn,
      error: console.error,
    };

    // Store original or already-overridden methods
    originalConsole.current = currentConsole;

    const captureConsole = (type: ConsoleEntry["type"]) => {
      const currentMethod = currentConsole[type];

      return (...args: any[]) => {
        // Skip internal service logs to prevent noise
        const message = args
          .map((arg) =>
            typeof arg === "object"
              ? JSON.stringify(arg, null, 2)
              : String(arg),
          )
          .join(" ");

        // Filter out internal logs
        if (
          message.includes("[UnifiedErrorHandler]") ||
          message.includes("[UnifiedLogger]") ||
          message.includes("[UnifiedAuthService]") ||
          message.includes("[UnifiedCacheService]") ||
          message.includes("[Proxy Request]") ||
          message.includes("[Proxy Response]")
        ) {
          // Still call the original method but don't capture
          currentMethod.apply(console, args);
          return;
        }

        setConsoleEntries((prev) => {
          const existing = prev.find(
            (e) => e.message === message && e.type === type,
          );
          if (existing) {
            return prev.map((e) =>
              e.id === existing.id
                ? { ...e, count: e.count + 1, timestamp: new Date() }
                : e,
            );
          }

          const newEntry: ConsoleEntry = {
            id: Date.now().toString() + Math.random(),
            type,
            message,
            timestamp: new Date(),
            count: 1,
            stack: type === "error" ? new Error().stack : undefined,
          };

          if (type === "error") setErrorCount((prev) => prev + 1);
          if (type === "warn") setWarningCount((prev) => prev + 1);

          return [...prev.slice(-99), newEntry];
        });

        // Call the current/original console method
        currentMethod.apply(console, args);
      };
    };

    // Override console methods
    console.log = captureConsole("log");
    console.info = captureConsole("info");
    console.warn = captureConsole("warn");
    console.error = captureConsole("error");

    return () => {
      // Restore to what we found (might be globalErrorHandler's overrides)
      if (originalConsole.current) {
        console.log = originalConsole.current.log;
        console.info = originalConsole.current.info;
        console.warn = originalConsole.current.warn;
        console.error = originalConsole.current.error;
      }
    };
  }, []);

  // Intercept fetch requests
  useEffect(() => {
    const currentFetch = window.fetch; // Get current fetch (might already be intercepted)

    window.fetch = async (...args) => {
      const startTime = Date.now();
      const [resource, config] = args;
      const url = typeof resource === "string" ? resource : resource.url;
      const method = config?.method || "GET";

      // Skip tracking .ts file requests (already handled in main.tsx)
      if (url && url.endsWith(".ts")) {
        return currentFetch(...args);
      }

      try {
        const response = await currentFetch(...args);
        const duration = Date.now() - startTime;

        const clonedResponse = response.clone();
        const contentLength = response.headers.get("content-length");

        setNetworkEntries((prev) => [
          ...prev.slice(-49),
          {
            id: Date.now().toString(),
            method,
            url,
            status: response.status,
            duration,
            timestamp: new Date(),
            size: contentLength ? parseInt(contentLength) : undefined,
          },
        ]);

        return response;
      } catch (error) {
        const duration = Date.now() - startTime;

        setNetworkEntries((prev) => [
          ...prev.slice(-49),
          {
            id: Date.now().toString(),
            method,
            url,
            status: 0,
            duration,
            timestamp: new Date(),
            error: error instanceof Error ? error.message : "Network error",
          },
        ]);

        throw error;
      }
    };

    return () => {
      window.fetch = currentFetch; // Restore to what we found
    };
  }, []);

  // Health checks
  const performHealthChecks = useCallback(async () => {
    // Use relative URLs for API calls to work with proxy in development
    const isDocker = window.location.port === "3010";
    const backendPort = isDocker ? "5001" : "5555"; // Docker uses 5001, local dev uses 5555

    const services = [
      { name: "Backend API", url: `/api/health` },
      { name: "Frontend", url: "/" },
      { name: "Database", url: `/api/health/db` },
      { name: "Redis", url: `/api/health/redis` },
      { name: "ML Service", url: `/api/health/ml` },
    ];

    const checks = await Promise.all(
      services.map(async (service) => {
        const startTime = Date.now();
        try {
          const response = await fetch(service.url, {
            method: "GET",
            mode: "cors",
            cache: "no-cache",
          });
          const responseTime = Date.now() - startTime;

          return {
            service: service.name,
            status: response.ok ? "healthy" : "degraded",
            lastCheck: new Date(),
            responseTime,
          } as HealthStatus;
        } catch (error) {
          return {
            service: service.name,
            status: "down",
            lastCheck: new Date(),
            error: error instanceof Error ? error.message : "Connection failed",
          } as HealthStatus;
        }
      }),
    );

    setHealthStatuses(checks);
  }, []);

  // Performance monitoring
  useEffect(() => {
    const updatePerformanceMetrics = () => {
      const metrics: PerformanceMetric[] = [];

      // Memory usage
      if ("memory" in performance) {
        const memory = (performance as any).memory;
        metrics.push({
          name: "JS Heap Used",
          value: Math.round(memory.usedJSHeapSize / 1048576),
          unit: "MB",
          trend: "stable",
        });
        metrics.push({
          name: "JS Heap Limit",
          value: Math.round(memory.jsHeapSizeLimit / 1048576),
          unit: "MB",
          trend: "stable",
        });
      }

      // Page load time
      const perfData = performance.getEntriesByType(
        "navigation",
      )[0] as PerformanceNavigationTiming;
      if (perfData) {
        metrics.push({
          name: "Page Load Time",
          value: Math.round(perfData.loadEventEnd - perfData.fetchStart),
          unit: "ms",
          trend: "stable",
        });
        metrics.push({
          name: "DOM Content Loaded",
          value: Math.round(
            perfData.domContentLoadedEventEnd - perfData.fetchStart,
          ),
          unit: "ms",
          trend: "stable",
        });
      }

      setPerformanceMetrics(metrics);
    };

    updatePerformanceMetrics();
    const interval = setInterval(updatePerformanceMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  // WebSocket status monitoring
  useEffect(() => {
    // Check if any WebSocket connections exist
    const checkWebSocketStatus = () => {
      // This is a simplified check - in real app, you'd check actual WS connection
      setWsStatus("connected"); // Mock for now
    };

    checkWebSocketStatus();
    const interval = setInterval(checkWebSocketStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  // Initial health check
  useEffect(() => {
    performHealthChecks();
    const interval = setInterval(performHealthChecks, 30000); // Check every 30 seconds
    return () => clearInterval(interval);
  }, [performHealthChecks]);

  const clearConsole = () => {
    setConsoleEntries([]);
    setErrorCount(0);
    setWarningCount(0);
    toast.success("Console cleared");
  };

  const clearNetwork = () => {
    setNetworkEntries([]);
    toast.success("Network logs cleared");
  };

  const exportLogs = () => {
    const logs = {
      console: consoleEntries,
      network: networkEntries,
      health: healthStatuses,
      performance: performanceMetrics,
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(logs, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `spheroseg-debug-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success("Logs exported");
  };

  const getStatusIcon = (status: HealthStatus["status"]) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "degraded":
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case "down":
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getConsoleIcon = (type: ConsoleEntry["type"]) => {
    switch (type) {
      case "error":
        return <XCircle className="w-3 h-3 text-red-500" />;
      case "warn":
        return <AlertCircle className="w-3 h-3 text-yellow-500" />;
      case "info":
        return <AlertCircle className="w-3 h-3 text-blue-500" />;
      default:
        return <CheckCircle className="w-3 h-3 text-gray-500" />;
    }
  };

  if (isMinimized) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsMinimized(false)}
          className="shadow-lg"
          variant={errorCount > 0 ? "destructive" : "default"}
        >
          <Bug className="w-4 h-4 mr-2" />
          Debug
          {errorCount > 0 && (
            <Badge variant="destructive" className="ml-2">
              {errorCount}
            </Badge>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-[600px] max-h-[70vh] bg-background border rounded-lg shadow-2xl overflow-hidden">
      <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between py-3">
          <CardTitle className="text-sm font-semibold flex items-center gap-2">
            <Bug className="w-4 h-4" />
            Testing Dashboard
            <Badge variant="outline" className="text-xs">
              DEV
            </Badge>
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1">
              {wsStatus === "connected" ? (
                <Wifi className="w-4 h-4 text-green-500" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-500" />
              )}
              {errorCount > 0 && (
                <Badge variant="destructive" className="text-xs">
                  {errorCount} errors
                </Badge>
              )}
              {warningCount > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {warningCount} warnings
                </Badge>
              )}
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={performHealthChecks}
              className="h-7 px-2"
            >
              <RefreshCw className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={exportLogs}
              className="h-7 px-2"
            >
              <Download className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setIsMinimized(true)}
              className="h-7 px-2"
            >
              _
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-2">
          <Tabs defaultValue="console" className="h-full">
            <TabsList className="grid w-full grid-cols-4 h-8">
              <TabsTrigger value="console" className="text-xs">
                Console
              </TabsTrigger>
              <TabsTrigger value="network" className="text-xs">
                Network
              </TabsTrigger>
              <TabsTrigger value="health" className="text-xs">
                Health
              </TabsTrigger>
              <TabsTrigger value="performance" className="text-xs">
                Performance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="console" className="mt-2">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-muted-foreground">
                  {consoleEntries.length} entries
                </span>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearConsole}
                  className="h-6 text-xs"
                >
                  Clear
                </Button>
              </div>
              <ScrollArea className="h-[350px] w-full rounded-md border p-2">
                {consoleEntries.map((entry) => (
                  <div
                    key={entry.id}
                    className={`text-xs mb-2 p-1 rounded ${
                      entry.type === "error"
                        ? "bg-red-50 dark:bg-red-950"
                        : entry.type === "warn"
                          ? "bg-yellow-50 dark:bg-yellow-950"
                          : ""
                    }`}
                  >
                    <div className="flex items-start gap-1">
                      {getConsoleIcon(entry.type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-[10px] text-muted-foreground">
                            {entry.timestamp.toLocaleTimeString()}
                          </span>
                          {entry.count > 1 && (
                            <Badge
                              variant="secondary"
                              className="text-[10px] h-4 px-1"
                            >
                              {entry.count}
                            </Badge>
                          )}
                        </div>
                        <pre className="whitespace-pre-wrap break-all font-mono text-[11px] mt-1">
                          {entry.message}
                        </pre>
                        {entry.stack && (
                          <details className="mt-1">
                            <summary className="cursor-pointer text-[10px] text-muted-foreground">
                              Stack trace
                            </summary>
                            <pre className="text-[10px] mt-1 text-muted-foreground">
                              {entry.stack}
                            </pre>
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </ScrollArea>
            </TabsContent>

            <TabsContent value="network" className="mt-2">
              <div className="flex justify-between items-center mb-2">
                <span className="text-xs text-muted-foreground">
                  {networkEntries.length} requests
                </span>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={clearNetwork}
                  className="h-6 text-xs"
                >
                  Clear
                </Button>
              </div>
              <ScrollArea className="h-[350px] w-full rounded-md border">
                <table className="w-full text-xs">
                  <thead className="border-b">
                    <tr>
                      <th className="text-left p-1">Method</th>
                      <th className="text-left p-1">URL</th>
                      <th className="text-left p-1">Status</th>
                      <th className="text-left p-1">Time</th>
                      <th className="text-left p-1">Size</th>
                    </tr>
                  </thead>
                  <tbody>
                    {networkEntries.map((entry) => (
                      <tr
                        key={entry.id}
                        className={`border-b hover:bg-gray-50 dark:hover:bg-gray-900 ${
                          entry.status >= 400 ? "text-red-600" : ""
                        }`}
                      >
                        <td className="p-1 font-mono">{entry.method}</td>
                        <td
                          className="p-1 truncate max-w-[200px]"
                          title={entry.url}
                        >
                          {entry.url.replace("http://localhost:", "")}
                        </td>
                        <td className="p-1">
                          {entry.status || entry.error || "Failed"}
                        </td>
                        <td className="p-1">{entry.duration}ms</td>
                        <td className="p-1">
                          {entry.size
                            ? `${(entry.size / 1024).toFixed(1)}KB`
                            : "-"}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="health" className="mt-2">
              <ScrollArea className="h-[350px] w-full rounded-md border p-2">
                <div className="space-y-2">
                  {healthStatuses.map((status) => (
                    <div
                      key={status.service}
                      className="flex items-center justify-between p-2 rounded-md border"
                    >
                      <div className="flex items-center gap-2">
                        {getStatusIcon(status.status)}
                        <div>
                          <div className="text-sm font-medium">
                            {status.service}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Last check: {status.lastCheck.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        {status.responseTime && (
                          <div className="text-xs">{status.responseTime}ms</div>
                        )}
                        {status.error && (
                          <div className="text-xs text-red-500">
                            {status.error}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="performance" className="mt-2">
              <ScrollArea className="h-[350px] w-full rounded-md border p-2">
                <div className="space-y-2">
                  {performanceMetrics.map((metric) => (
                    <div
                      key={metric.name}
                      className="flex items-center justify-between p-2 rounded-md border"
                    >
                      <div>
                        <div className="text-sm font-medium">{metric.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {metric.value} {metric.unit}
                        </div>
                      </div>
                      {metric.trend && (
                        <div
                          className={`text-xs ${
                            metric.trend === "up"
                              ? "text-red-500"
                              : metric.trend === "down"
                                ? "text-green-500"
                                : "text-gray-500"
                          }`}
                        >
                          {metric.trend === "up"
                            ? "↑"
                            : metric.trend === "down"
                              ? "↓"
                              : "→"}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestingDashboard;
