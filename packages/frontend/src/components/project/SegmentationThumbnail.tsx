import React, { useState, useEffect, useRef } from "react";
import apiClient from "@/services/api/client";
import {
  Point as ApiPoint,
  Polygon as ApiPolygon,
  SegmentationResult as ApiSegmentationResult,
} from "@/lib/segmentation/types";
import { createSvgPath, scalePolygons } from "@/lib/svgUtils";
import { getLogger } from "@/utils/logging/unifiedLogger";
import type { ApiResult } from "@spheroseg/shared";
import { thumbnailCache } from "@/services/thumbnailCache";
import { constructUrl } from "@/lib/urlUtils";

const CLogger = getLogger("SegmentationThumbnail");

// Define internal types based on ApiPolygon and to accommodate legacy fields
type InternalPolygon = ApiPolygon & {
  legacyLabel?: string;
  legacyScore?: number;
};

// Define a polygon type specific for display, adding color
interface DisplayPolygon extends ApiPolygon {
  color?: string;
}

interface InternalSegmentationDataState {
  polygons: InternalPolygon[];
  imageWidth?: number;
  imageHeight?: number;
}

// For legacy API responses that might be a simple array of polygons
interface LegacyListItem {
  points: ApiPoint[];
  label?: string;
  score?: number;
  id?: string;
  type?: "external" | "internal";
  class?: string;
  holes?: ApiPoint[][];
}

// Combined type for what axios.get might return
type ApiResponse = ApiSegmentationResult | LegacyListItem[];

interface SegmentationThumbnailProps {
  imageId: string;
  projectId: string;
  thumbnailUrl: string | null | undefined;
  fallbackSrc?: string;
  altText?: string;
  className?: string;
  width?: number;
  height?: number;
}

/**
 * Component that displays an image thumbnail with segmentation polygons overlaid
 */
const SegmentationThumbnail: React.FC<SegmentationThumbnailProps> = ({
  imageId,
  projectId,
  thumbnailUrl,
  fallbackSrc = "/placeholder.svg",
  altText = "Image thumbnail",
  className = "",
  width = 300,
  height = 300,
}) => {
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isFallback, setIsFallback] = useState(false);
  const [triedDirectUrl, setTriedDirectUrl] = useState(false);
  const [segmentationData, setSegmentationData] =
    useState<InternalSegmentationDataState | null>(null);
  const [imageSize, setImageSize] = useState<{
    width: number;
    height: number;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [scaledPolygons, setScaledPolygons] = useState<DisplayPolygon[] | null>(
    null,
  );
  const containerRef = useRef<HTMLDivElement>(null);
  const cachedImageIdRef = useRef<string | null>(null);

  useEffect(() => {
    const fetchSegmentationData = async () => {
      if (!imageId || !projectId) {
        CLogger.debug("No imageId or projectId provided, skipping fetch.", {
          imageId,
          projectId,
        });
        return;
      }

      CLogger.debug(
        `Fetching segmentation data for image ${imageId} in project ${projectId}`,
      );

      try {
        setIsLoading(true);

        // Try multiple endpoints to get segmentation data
        let success = false;

        // Define endpoints to try - using the most reliable one first based on backend code
        const endpoints = [
          { name: "Primary Endpoint", url: `/images/${imageId}/segmentation` },
          // Only try these if needed
          {
            name: "Project Endpoint",
            url: `/projects/${projectId}/images/${imageId}/segmentation`,
          },
          { name: "Legacy Endpoint", url: `/segmentation/${imageId}` },
        ];

        // Try each endpoint
        for (const endpoint of endpoints) {
          if (success) break;

          try {
            const response = await apiClient.get<ApiResponse>(endpoint.url);

            let fetchedPolygons: InternalPolygon[] = []; // Explicitly typed
            let width: number | undefined;
            let height: number | undefined;

            // Check for polygons in different response formats
            if (response.data) {
              // Check if it's the standardized API response format
              if ("success" in response.data && "data" in response.data) {
                const apiResult = response.data as ApiResult<any>;
                if (apiResult.success && apiResult.data) {
                  // Check if data contains polygons
                  if (
                    apiResult.data.polygons &&
                    Array.isArray(apiResult.data.polygons)
                  ) {
                    const segData = apiResult.data as ApiSegmentationResult;
                    fetchedPolygons = segData.polygons.map(
                      (apiPoly: ApiPolygon): InternalPolygon => ({
                        ...apiPoly,
                      }),
                    );
                    // Segmentation is always done on 1024x1024 images
                    // But we need to use the original image dimensions
                    width = segData.imageWidth || 1024;
                    height = segData.imageHeight || 1024;
                    CLogger.info(
                      `Fetched segmentation data from standardized API response. Polygons: ${fetchedPolygons.length}, segmentation dimensions: ${width}x${height}`,
                    );
                  }
                  // Check if data itself is an array of polygons (legacy format wrapped in standard response)
                  else if (Array.isArray(apiResult.data)) {
                    const legacyData = apiResult.data as LegacyListItem[];
                    fetchedPolygons = legacyData.map(
                      (p: LegacyListItem): InternalPolygon => ({
                        id:
                          p.id ||
                          `legacy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                        points: p.points,
                        type: p.type || "external",
                        class: p.class || p.label,
                        holes: p.holes,
                        legacyLabel: p.label,
                        legacyScore: p.score,
                      }),
                    );
                    CLogger.warn(
                      `Fetched legacy format from standardized response. Polygons: ${fetchedPolygons.length}`,
                    );
                  }
                }
              }
              // Direct polygons array (legacy format)
              else if (Array.isArray(response.data)) {
                const legacyData = response.data as LegacyListItem[];
                fetchedPolygons = legacyData.map(
                  (p: LegacyListItem): InternalPolygon => ({
                    id:
                      p.id ||
                      `legacy-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                    points: p.points,
                    type: p.type || "external",
                    class: p.class || p.label,
                    holes: p.holes,
                    legacyLabel: p.label,
                    legacyScore: p.score,
                  }),
                );
                CLogger.warn(
                  `Fetched legacy segmentation format from ${endpoint.name}. Original image dimensions not available from this endpoint.`,
                );
              }
              // Polygons in result_data (old format)
              else if (
                (response.data as any).polygons &&
                Array.isArray((response.data as any).polygons)
              ) {
                const currentData = response.data as ApiSegmentationResult;
                fetchedPolygons = currentData.polygons.map(
                  (apiPoly: ApiPolygon): InternalPolygon => ({
                    ...apiPoly,
                  }),
                );
                width = currentData.imageWidth;
                height = currentData.imageHeight;
                CLogger.info(
                  `Fetched segmentation data from ${endpoint.name}. Polygons: ${fetchedPolygons.length}, API imageWidth: ${width}, API imageHeight: ${height}`,
                );
              }
            }

            if (
              fetchedPolygons.length > 0 ||
              (endpoint.name && response.status === 200)
            ) {
              // If we got a 200 OK with empty array, it's valid
              CLogger.info(
                `${endpoint.name} success, got ${fetchedPolygons.length} polygons for image ${imageId}`,
              );

              // Process polygons - ensure each polygon has a type
              const processedPolygons = fetchedPolygons.map(
                (polygon): InternalPolygon => ({
                  ...polygon,
                  id:
                    polygon.id ||
                    `poly-${Math.random().toString(36).substring(2, 9)}`,
                  type: polygon.type || "external",
                }),
              );

              setSegmentationData({
                polygons: processedPolygons,
                imageWidth: width, // This might be undefined if legacy API was used
                imageHeight: height, // This might be undefined if legacy API was used
              });
              success = true;

              // If we have image dimensions in the response, use them
              if (width && height) {
                setImageSize({
                  width,
                  height,
                });
                CLogger.info(
                  `Dimensions for image ${imageId} from API: ${width}x${height}`,
                );
              } else {
                CLogger.warn(
                  `API for image ${imageId} returned polygons but no explicit imageWidth/imageHeight. Will need to use original image dimensions.`,
                );
                // Try to get dimensions from the backend
                try {
                  const imageInfoResponse = await apiClient.get(
                    `/images/${imageId}`,
                  );
                  const imageInfo = imageInfoResponse.data as any;
                  if (imageInfo.width && imageInfo.height) {
                    setImageSize({
                      width: imageInfo.width,
                      height: imageInfo.height,
                    });
                    CLogger.info(
                      `Got original dimensions from image info: ${imageInfo.width}x${imageInfo.height}`,
                    );
                  }
                } catch (err) {
                  CLogger.warn("Failed to get image dimensions from backend");
                }
              }

              // Break the loop since we found valid data
              break;
            } else {
              CLogger.info(
                `${endpoint.name} for image ${imageId} returned no valid polygons`,
              );
            }
          } catch (_error) {
            if (_error instanceof Error) {
              CLogger.warn(
                `Error fetching from ${endpoint.name} for image ${imageId}:`,
                _error.message,
              );
            } else {
              CLogger.warn(
                `Unknown error fetching from ${endpoint.name} for image ${imageId}:`,
                _error,
              );
            }
          }
        }

        // If no real data was found, set empty polygons
        if (!success) {
          CLogger.info(
            `No segmentation data found for image ${imageId} after trying all endpoints.`,
          );

          // Set empty polygons array
          setSegmentationData({ polygons: [] });

          // Set default image size if not already set by API (which it wouldn't be if success is false)
          if (!imageSize) {
            CLogger.info(
              `Setting default imageSize for ${imageId} as API provided no dimensions and no polygons.`,
            );
            setImageSize({ width: 100, height: 100 });
          }
        }
      } catch (_error) {
        if (_error instanceof Error) {
          CLogger.error(
            `Error fetching segmentation data for image ${imageId}:`,
            _error.message,
          );
        } else {
          CLogger.error(
            `Unknown error fetching segmentation data for image ${imageId}:`,
            _error,
          );
        }

        // Don't create demo polygons in case of error either
        setSegmentationData({ polygons: [] });

        // Set default image size if not already set
        if (!imageSize) {
          CLogger.info(
            `Setting default imageSize for ${imageId} due to error in fetchSegmentationData.`,
          );
          setImageSize({ width: 100, height: 100 });
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchSegmentationData();

    // Reason: imageSize is set internally based on image load or API response within this fetchSegmentationData effect.
    // Including imageSize as a dependency would cause an infinite loop of re-fetching
    // as setImageSize inside this effect would trigger the effect again.
    // projectId is included as it's essential for constructing the correct API endpoint and ensures
    // data is re-fetched if the project context changes for the same imageId.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [imageId, projectId]);

  // Set image source using cache
  useEffect(() => {
    let mounted = true;

    const loadImage = async () => {
      // Clean up previous cached reference if switching images
      if (cachedImageIdRef.current && cachedImageIdRef.current !== imageId) {
        thumbnailCache.releaseThumbnail(cachedImageIdRef.current);
        cachedImageIdRef.current = null;
      }

      if (thumbnailUrl) {
        const constructedUrl = constructUrl(thumbnailUrl);
        CLogger.debug(`Using direct thumbnailUrl: ${thumbnailUrl} -> ${constructedUrl}`);
        setImageSrc(constructedUrl);
        setTriedDirectUrl(true);
      } else if (imageId) {
        CLogger.debug(`Fetching thumbnail for imageId: ${imageId} from cache`);

        try {
          const cachedUrl = await thumbnailCache.getThumbnail(imageId);

          if (mounted && cachedUrl) {
            setImageSrc(cachedUrl);
            cachedImageIdRef.current = imageId;
            setTriedDirectUrl(false);
          } else if (mounted) {
            // Cache returned null, use fallback
            CLogger.warn(`Cache failed for ${imageId}, using fallback`);
            setImageSrc(fallbackSrc);
            setIsFallback(true);
          }
        } catch (error) {
          CLogger.error(
            `Error getting thumbnail from cache for ${imageId}:`,
            error,
          );
          if (mounted && fallbackSrc) {
            setImageSrc(fallbackSrc);
            setIsFallback(true);
          }
        }
      } else if (fallbackSrc) {
        CLogger.debug(`No imageId or thumbnailUrl, using fallback`);
        setImageSrc(fallbackSrc);
        setIsFallback(true);
        setTriedDirectUrl(false);
      }

      // Set initial image size from props if needed
      if (!imageSize && (width || height)) {
        CLogger.debug(`Setting initial imageSize from props`, {
          width,
          height,
        });
        setImageSize({ width: width || 100, height: height || 100 });
      }
    };

    loadImage();

    return () => {
      mounted = false;
    };
  }, [imageId, thumbnailUrl, fallbackSrc, width, height]); // Removed imageSize dependency to prevent loops

  // Cleanup cache references on unmount
  useEffect(() => {
    return () => {
      if (cachedImageIdRef.current) {
        thumbnailCache.releaseThumbnail(cachedImageIdRef.current);
        cachedImageIdRef.current = null;
      }
    };
  }, []);

  // Handle image load to get dimensions if not provided
  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    if (e.currentTarget) {
      const newImageSize = {
        width: e.currentTarget.naturalWidth,
        height: e.currentTarget.naturalHeight,
      };

      // Only update if the new size is different and valid,
      // and if we haven't already received dimensions from the API
      if (
        newImageSize.width > 0 &&
        newImageSize.height > 0 &&
        (!imageSize || // if imageSize is not set yet by API
          // Or if API didn't provide valid dimensions (width/height might be 0 or undefined from API response)
          // This condition checks if imageSize is not set OR if it was set but to invalid values (e.g. 0x0 or default 100x100 if API failed)
          (!(
            imageSize &&
            imageSize.width > 0 &&
            imageSize.height > 0 &&
            (imageSize.width !== 100 || imageSize.height !== 100)
          ) && // Avoid overwriting valid API dimensions (unless they were the default 100x100)
            (imageSize?.width !== newImageSize.width ||
              imageSize?.height !== newImageSize.height))) // and new dimensions are different
      ) {
        CLogger.info(
          `Image ${imageId} loaded, natural dimensions: ${newImageSize.width}x${newImageSize.height}. API did not provide valid dimensions or they were default. Updating imageSize based on thumbnail.`,
        );
        setImageSize(newImageSize);
      } else if (
        imageSize &&
        imageSize.width === newImageSize.width &&
        imageSize.height === newImageSize.height
      ) {
        CLogger.info(
          `Image ${imageId} loaded, natural dimensions match API/existing dimensions: ${newImageSize.width}x${newImageSize.height}. No update to imageSize needed.`,
        );
      } else {
        CLogger.info(
          `Image ${imageId} loaded, natural dimensions: ${newImageSize.width}x${newImageSize.height}. API/existing dimensions (${imageSize?.width}x${imageSize?.height}) will be preferred or already set.`,
        );
      }
    }
    setIsLoading(false); // Image element itself has loaded
  };

  // Effect to scale polygons when imageSize, containerRef, or segmentationData changes
  useEffect(() => {
    if (
      !segmentationData ||
      !Array.isArray(segmentationData.polygons) ||
      segmentationData.polygons.length === 0
    ) {
      CLogger.debug("No segmentation data or empty polygons array");
      return;
    }

    if (!imageSize || imageSize.width <= 0 || imageSize.height <= 0) {
      CLogger.debug("Invalid image size", imageSize);
      return;
    }

    const handleResize = () => {
      if (!containerRef.current) return;

      const containerWidth = containerRef.current.offsetWidth;
      const containerHeight = containerRef.current.offsetHeight;

      if (containerWidth === 0 || containerHeight === 0) {
        CLogger.warn("Container dimensions are zero, will retry on resize");
        return;
      }

      // IMPORTANT: ML service already returns polygons in original image space
      // The ML service performs segmentation on 1024x1024 images internally,
      // but converts the polygon coordinates back to original image dimensions
      // before returning them. So we DON'T need to scale from 1024x1024.

      // Calculate scale factors for debugging
      const scaleX = containerWidth / imageSize.width;
      const scaleY = containerHeight / imageSize.height;
      const uniformScale = Math.min(scaleX, scaleY);
      const offsetX = (containerWidth - imageSize.width * uniformScale) / 2;
      const offsetY = (containerHeight - imageSize.height * uniformScale) / 2;

      // Polygons are already in original image space, so we only need to scale
      // from original image space to container space
      const scaled = scalePolygons(
        segmentationData.polygons,
        imageSize.width,
        imageSize.height,
        containerWidth,
        containerHeight,
      );

      if (
        scaled &&
        scaled.length > 0 &&
        scaled[0].points &&
        scaled[0].points.length > 0
      ) {
        const firstScaledPoint = scaled[0].points[0];
        const expectedX =
          segmentationData.polygons[0].points[0].x * uniformScale + offsetX;
        const expectedY =
          segmentationData.polygons[0].points[0].y * uniformScale + offsetY;
      }

      if (scaled && scaled.length > 0) {
        const displayPolygons: DisplayPolygon[] = scaled.map((poly) => ({
          ...poly,
          color: poly.type === "internal" ? "#3B82F6" : "#EF4444",
        }));

        setScaledPolygons(displayPolygons);
      } else {
        setScaledPolygons([]);
      }
    };

    // Initial scaling
    handleResize();

    // Set up ResizeObserver to handle container size changes
    const container = containerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver(() => {
        handleResize();
      });
      resizeObserver.observe(container);

      return () => {
        resizeObserver.disconnect();
      };
    }
  }, [segmentationData, imageSize]);

  // Handle image error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    // Prevent infinite error loop
    if (isFallback) {
      e.preventDefault();
      return;
    }

    try {
      // Only try direct URL once, and only if we're not already showing fallback
      if (
        !triedDirectUrl &&
        thumbnailUrl &&
        !thumbnailUrl.startsWith("blob:")
      ) {
        setTriedDirectUrl(true);

        const backendUrl =
          import.meta.env.VITE_API_URL || "http://localhost:5001";
        const thumbnailPath =
          thumbnailUrl && thumbnailUrl.includes("uploads/")
            ? thumbnailUrl.substring(thumbnailUrl.indexOf("uploads/") + 8)
            : thumbnailUrl || "";

        // Only try backendUrl if thumbnailPath is valid
        if (thumbnailPath && thumbnailPath.length > 0) {
          const directPath = `${backendUrl}/uploads/${thumbnailPath}`;
          e.currentTarget.src = directPath;
          return;
        }
      }
    } catch (_err) {
      CLogger.error("Error handling thumbnail fallback:", _err);
    }

    // Set to fallback mode
    setIsFallback(true);
    setImageSrc(fallbackSrc);
  };

  // Log render state for debugging
  CLogger.info(`SegmentationThumbnail render state for ${imageId}:`, {
    hasSegmentationData: !!segmentationData,
    polygonsCount: segmentationData?.polygons?.length || 0,
    scaledPolygonsCount: scaledPolygons?.length || 0,
    imageSize,
    containerWidth: containerRef.current?.offsetWidth,
    containerHeight: containerRef.current?.offsetHeight,
    componentProps: { width, height },
    isLoading,
    imageSrc: !!imageSrc,
    apiDimensions: {
      width: segmentationData?.imageWidth,
      height: segmentationData?.imageHeight,
    },
  });

  const showNoSegmentationMessage =
    !isLoading &&
    segmentationData &&
    Array.isArray(segmentationData.polygons) &&
    segmentationData.polygons.length === 0;

  // Render the image and SVG overlay
  // Note: viewBox might need adjustment if aspect ratios differ significantly
  // and we want to maintain polygon aspect ratio relative to the image content.
  // Current setup assumes image fills the container, and polygons are scaled to that container.
  return (
    <div
      ref={containerRef}
      className={`relative ${className}`}
      style={{ width: `${width}px`, height: `${height}px` }}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 opacity-50">
          <p>Loading Seg...</p>
        </div>
      )}
      <img
        src={
          imageSrc ||
          (isFallback
            ? fallbackSrc
            : "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=")
        } // 1x1 transparent pixel
        alt={altText}
        onLoad={handleImageLoad}
        onError={handleImageError}
        className="absolute inset-0 object-contain w-full h-full"
        style={{ display: "block" }} // Always show the image
      />
      {/* Render polygons only if scaledPolygons exist and have items */}
      {scaledPolygons && scaledPolygons.length > 0 && containerRef.current ? (
        <svg
          className="absolute top-0 left-0 w-full h-full pointer-events-none"
          viewBox={`0 0 ${containerRef.current.offsetWidth} ${containerRef.current.offsetHeight}`}
          preserveAspectRatio="xMidYMid meet"
          style={{ overflow: "visible", zIndex: 10 }}
        >
          {scaledPolygons.map((polygon) => (
            <path
              key={polygon.id}
              d={createSvgPath(polygon.points, polygon.holes)}
              fill="transparent"
              stroke={polygon.color || "#EF4444"}
              strokeWidth="2"
              strokeOpacity="0.8"
              vectorEffect="non-scaling-stroke"
            />
          ))}
        </svg>
      ) : (
        showNoSegmentationMessage && (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-xs text-gray-500 dark:text-gray-400 p-1 bg-white dark:bg-gray-800 bg-opacity-75 dark:bg-opacity-75 rounded">
              No seg
            </p>
          </div>
        )
      )}
      {/* Fallback text if everything fails */}
      {isFallback && imageSrc === fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center">
          <p className="text-xs text-red-500">Image N/A</p>
        </div>
      )}
    </div>
  );
};

export default SegmentationThumbnail;
