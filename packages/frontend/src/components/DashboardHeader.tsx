import React from "react";
import { useLocation, Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import Logo from "@/components/header/Logo";
import UserProfileDropdown from "@/components/header/UserProfileDropdown";
import { BookOpen } from "lucide-react";

const DashboardHeader = () => {
  const { user } = useAuth();
  const location = useLocation();

  // Skrýt header v segmentačním editoru
  const isSegmentationEditor =
    location.pathname.includes("/projects/") &&
    location.pathname.includes("/segmentation/");

  if (isSegmentationEditor) {
    return null;
  }

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center">
          <Logo />
        </div>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-4">
          <Link
            to="/documentation"
            className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            title="Documentation"
          >
            <BookOpen className="h-4 w-4" />
            <span>Documentation</span>
          </Link>
          <UserProfileDropdown
            username={user?.email?.split("@")[0] || "User"}
          />
        </div>
      </div>
    </header>
  );
};

export default React.memo(DashboardHeader);
