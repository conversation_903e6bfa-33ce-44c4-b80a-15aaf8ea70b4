/**
 * CSV Export Hook
 * Handles CSV export functionality
 */

import { format } from "date-fns";
import { toast } from "sonner";
import { saveAs } from "file-saver";
import logger from "@/utils/logging/unifiedLogger";
import type { ProjectImage } from "@/types";
import { calculateMetrics } from "@/pages/segmentation/utils/metricCalculations";

export interface CsvExportOptions {
  delimiter?: "," | ";" | "\t";
  includeHeaders?: boolean;
  includeMetrics?: boolean;
}

export function useCsvExport() {
  /**
   * Export data to CSV
   */
  const exportToCsv = (
    images: ProjectImage[],
    projectName: string,
    options: CsvExportOptions = {},
  ) => {
    try {
      const delimiter = options.delimiter || ",";
      const includeHeaders = options.includeHeaders !== false;

      // Build CSV headers
      const headers = [
        "Image ID",
        "Filename",
        "Upload Date",
        "Status",
        "Cell Count",
        "Average Area",
        "Total Area",
        "Min Area",
        "Max Area",
      ];

      // Build CSV rows
      const rows = images.map((image) => {
        const metrics = image.segmentation?.polygons
          ? calculateMetrics(image.segmentation.polygons)
          : null;

        return [
          image.id,
          image.filename,
          format(new Date(image.uploadDate), "yyyy-MM-dd HH:mm"),
          image.segmentationStatus,
          metrics?.cellCount || 0,
          metrics?.avgArea?.toFixed(2) || 0,
          metrics?.totalArea?.toFixed(2) || 0,
          metrics?.minArea?.toFixed(2) || 0,
          metrics?.maxArea?.toFixed(2) || 0,
        ]
          .map((value) => {
            // Escape values containing delimiter or quotes
            const strValue = String(value);
            if (strValue.includes(delimiter) || strValue.includes('"')) {
              return `"${strValue.replace(/"/g, '""')}"`;
            }
            return strValue;
          })
          .join(delimiter);
      });

      // Combine headers and rows
      let csvContent = "";
      if (includeHeaders) {
        csvContent = headers.join(delimiter) + "\n";
      }
      csvContent += rows.join("\n");

      // Create blob and save
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_export_${timestamp}.csv`;

      saveAs(blob, filename);

      toast.success(`Exported ${images.length} images to CSV`);
      logger.info("CSV export completed", { imageCount: images.length });
    } catch (error) {
      logger.error("CSV export failed:", error);
      toast.error("Failed to export CSV file");
      throw error;
    }
  };

  /**
   * Export metrics to CSV
   */
  const exportMetricsToCsv = (
    metrics: any[],
    projectName: string,
    options: CsvExportOptions = {},
  ) => {
    try {
      const delimiter = options.delimiter || ",";

      if (metrics.length === 0) {
        toast.warning("No metrics to export");
        return;
      }

      // Get headers from first object
      const headers = Object.keys(metrics[0]);

      // Build CSV content
      let csvContent = headers.join(delimiter) + "\n";

      metrics.forEach((row) => {
        const values = headers.map((header) => {
          const value = row[header];
          const strValue =
            value !== null && value !== undefined ? String(value) : "";

          // Escape values if needed
          if (strValue.includes(delimiter) || strValue.includes('"')) {
            return `"${strValue.replace(/"/g, '""')}"`;
          }
          return strValue;
        });

        csvContent += values.join(delimiter) + "\n";
      });

      // Save file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const timestamp = format(new Date(), "yyyyMMdd_HHmmss");
      const filename = `${projectName}_metrics_${timestamp}.csv`;

      saveAs(blob, filename);

      toast.success("Metrics exported to CSV");
      logger.info("Metrics CSV export completed");
    } catch (error) {
      logger.error("Metrics CSV export failed:", error);
      toast.error("Failed to export metrics");
      throw error;
    }
  };

  return {
    exportToCsv,
    exportMetricsToCsv,
  };
}
