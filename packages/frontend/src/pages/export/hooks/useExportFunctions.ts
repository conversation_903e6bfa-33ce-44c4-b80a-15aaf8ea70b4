/**
 * Main Export Functions Hook
 * Orchestrates all export functionality
 */

import { useState } from "react";
import { toast } from "sonner";
import { useLanguage } from "@/contexts/LanguageContext";
import type { ProjectImage } from "@/types";
import logger from "@/utils/logging/unifiedLogger";

// Import specialized export hooks
import { useExcelExport } from "./useExcelExport";
import { useJsonExport } from "./useJsonExport";
import { useCsvExport } from "./useCsvExport";
import { useZipExport } from "./useZipExport";

export interface ExportOptions {
  format: "excel" | "csv" | "json" | "coco" | "yolo" | "zip";
  includeMetadata?: boolean;
  includeImages?: boolean;
  includeMasks?: boolean;
  includeAnnotations?: boolean;
}

export function useExportFunctions() {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const { t } = useLanguage();

  // Get specialized export functions
  const { exportToExcel, exportMetricsToExcel } = useExcelExport();
  const { exportToJson } = useJsonExport();
  const { exportToCsv, exportMetricsToCsv } = useCsvExport();
  const { exportToZip } = useZipExport();

  /**
   * Main export function
   */
  const exportData = async (
    images: ProjectImage[],
    projectName: string,
    options: ExportOptions,
  ) => {
    if (images.length === 0) {
      toast.warning(t("export.noImages"));
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      logger.info("Starting export", {
        format: options.format,
        imageCount: images.length,
      });

      switch (options.format) {
        case "excel":
          await exportToExcel(images, projectName, {
            includeMetadata: options.includeMetadata,
            includeStatistics: true,
          });
          break;

        case "csv":
          await exportToCsv(images, projectName, {
            includeHeaders: true,
            includeMetrics: true,
          });
          break;

        case "json":
        case "coco":
        case "yolo":
          await exportToJson(images, projectName, {
            format: options.format,
            includeMetadata: options.includeMetadata,
            prettyPrint: true,
          });
          break;

        case "zip":
          await exportToZip(images, projectName, {
            includeImages: options.includeImages,
            includeMasks: options.includeMasks,
            includeMetadata: options.includeMetadata,
            includeAnnotations: options.includeAnnotations,
          });
          break;

        default:
          throw new Error(`Unsupported export format: ${options.format}`);
      }

      setExportProgress(100);
      logger.info("Export completed successfully");
    } catch (error) {
      logger.error("Export failed:", error);
      toast.error(t("export.failed"));
      throw error;
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  /**
   * Export selected images
   */
  const exportSelected = async (
    selectedImages: ProjectImage[],
    projectName: string,
    options: ExportOptions,
  ) => {
    if (selectedImages.length === 0) {
      toast.warning(t("export.noSelection"));
      return;
    }

    return exportData(selectedImages, projectName, options);
  };

  /**
   * Export metrics only
   */
  const exportMetrics = async (
    metrics: any[],
    projectName: string,
    format: "excel" | "csv" = "excel",
  ) => {
    try {
      if (format === "excel") {
        await exportMetricsToExcel(metrics, projectName);
      } else {
        await exportMetricsToCsv(metrics, projectName);
      }
    } catch (error) {
      logger.error("Metrics export failed:", error);
      toast.error(t("export.metricsFailed"));
      throw error;
    }
  };

  /**
   * Batch export multiple projects
   */
  const batchExport = async (
    projects: { name: string; images: ProjectImage[] }[],
    options: ExportOptions,
  ) => {
    setIsExporting(true);

    try {
      for (let i = 0; i < projects.length; i++) {
        const project = projects[i];
        setExportProgress((i / projects.length) * 100);

        await exportData(project.images, project.name, options);
      }

      toast.success(t("export.batchCompleted", { count: projects.length }));
    } catch (error) {
      logger.error("Batch export failed:", error);
      toast.error(t("export.batchFailed"));
      throw error;
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return {
    exportData,
    exportSelected,
    exportMetrics,
    batchExport,
    isExporting,
    exportProgress,
  };
}
