import { Point } from '@spheroseg/shared';
import { TransformState } from '../hooks/segmentation/types';
import { createLogger } from '@/lib/logger';

const logger = createLogger('segmentation:polygonVisibility');

// Local implementations of geometry utilities
interface BoundingBox {
  minX: number;
  minY: number;
  maxX: number;
  maxY: number;
}

/**
 * Calculate the bounding box of a polygon
 */
const calculateBoundingBox = (points: Point[]): BoundingBox => {
  if (!points.length) {
    return { minX: 0, minY: 0, maxX: 0, maxY: 0 };
  }

  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;

  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }

  return { minX, minY, maxX, maxY };
};

/**
 * Check if a bounding box is visible in the viewport
 */
const isBoxVisible = (
  box: BoundingBox,
  viewport: BoundingBox,
  margin: number = 100
): boolean => {
  const viewportWithMargin = {
    minX: viewport.minX - margin,
    minY: viewport.minY - margin,
    maxX: viewport.maxX + margin,
    maxY: viewport.maxY + margin,
  };

  return !(
    box.maxX < viewportWithMargin.minX ||
    box.minX > viewportWithMargin.maxX ||
    box.maxY < viewportWithMargin.minY ||
    box.minY > viewportWithMargin.maxY
  );
};

/**
 * Polygon bounding box cache class
 */
class PolygonBoundingBoxCache {
  private cache: Map<string, BoundingBox> = new Map();

  getBoundingBox(polygonId: string, points: Point[]): BoundingBox {
    if (!this.cache.has(polygonId)) {
      this.cache.set(polygonId, calculateBoundingBox(points));
    }
    return this.cache.get(polygonId)!;
  }

  invalidate(polygonId: string): void {
    this.cache.delete(polygonId);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// Create a singleton instance
const polygonBoundingBoxCache = new PolygonBoundingBoxCache();

/**
 * Calculate the viewport bounds based on the canvas dimensions and transform
 */
export const calculateViewportBounds = (
  canvasWidth: number,
  canvasHeight: number,
  transform: TransformState,
): { minX: number; minY: number; maxX: number; maxY: number } => {
  // Convert viewport corners to canvas coordinates
  const topLeft = {
    x: -transform.translateX / transform.zoom,
    y: -transform.translateY / transform.zoom,
  };

  const bottomRight = {
    x: (canvasWidth - transform.translateX) / transform.zoom,
    y: (canvasHeight - transform.translateY) / transform.zoom,
  };

  return {
    minX: topLeft.x,
    minY: topLeft.y,
    maxX: bottomRight.x,
    maxY: bottomRight.y,
  };
};

/**
 * Filter polygons to only include those visible in the viewport
 */
export const filterVisiblePolygons = <T extends { points: Point[]; id: string }>(
  polygons: T[],
  canvasWidth: number,
  canvasHeight: number,
  transform: TransformState,
): T[] => {
  // If there are few polygons, don't bother filtering
  if (polygons.length < 50) {
    return polygons;
  }

  const viewport = calculateViewportBounds(canvasWidth, canvasHeight, transform);
  const startTime = performance.now();

  // Calculate bounding boxes for all polygons (could be cached)
  const visiblePolygons = polygons.filter((polygon) => {
    const box = calculateBoundingBox(polygon.points);
    return isBoxVisible(box, viewport);
  });

  const endTime = performance.now();
  logger.debug(
    `Filtered ${polygons.length} polygons to ${visiblePolygons.length} visible (${(endTime - startTime).toFixed(2)}ms)`,
  );

  return visiblePolygons;
};

// Export other utility functions from our shared module
export { calculateBoundingBox, isBoxVisible, PolygonBoundingBoxCache, polygonBoundingBoxCache };

export default filterVisiblePolygons;
