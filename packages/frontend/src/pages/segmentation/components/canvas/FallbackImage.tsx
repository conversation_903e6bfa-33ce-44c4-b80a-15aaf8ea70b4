import React, { useState, useEffect, useRef } from "react";
import { constructUrl } from "@/lib/urlUtils";
import { createDataUrl } from "@spheroseg/shared/utils/imageUtils";
import { toast } from "sonner";

interface FallbackImageProps {
  src: string;
  alt?: string;
  width: number;
  height: number;
  fallbackSrc?: string;
  alternativeUrls?: string[];
  onLoad?: (width: number, height: number) => void;
}

/**
 * Renders an SVG image. If the primary `src` fails to load,
 * it falls back to displaying the `fallbackSrc`.
 */
const FallbackImage: React.FC<FallbackImageProps> = ({
  src,
  alt = "Image",
  width,
  height,
  fallbackSrc = "/placeholder.svg",
  alternativeUrls = [],
  onLoad,
}) => {
  const [imageSource, setImageSource] = useState<string>(src);
  const [hasErrored, setHasErrored] = useState<boolean>(false);
  const [loadAttempts, setLoadAttempts] = useState<number>(0);
  const isMounted = useRef(true); // Track mount status

  // Process the source URL when it changes
  useEffect(() => {
    const loadImage = async () => {
      if (!src) {
        console.log("No source provided, using fallback");
        setImageSource(fallbackSrc);
        return;
      }

      console.log(`Loading image: ${src} at ${new Date().toISOString()}`);

      // First try with constructUrl to ensure proper formatting
      try {
        const processedSrc = constructUrl(src);
        console.log(`Processed URL: ${processedSrc}`);

        // Reset state
        setHasErrored(false);
        setLoadAttempts(0);

        // Check if the URL is accessible with a HEAD request
        try {
          const response = await fetch(processedSrc, {
            method: "HEAD",
            cache: "no-cache",
            headers: {
              "Cache-Control": "no-cache",
              Pragma: "no-cache",
            },
          });

          if (response.ok) {
            console.log("HEAD request successful");
            const contentType = response.headers.get("Content-Type");
            console.log(`Content-Type: ${contentType}`);

            if (contentType && contentType.startsWith("image/")) {
              console.log("Valid image content type, using processed URL");
              setImageSource(processedSrc);
              return;
            }
          } else {
            console.log(`HEAD request failed with status: ${response.status}`);
          }
        } catch (headError) {
          console.log("HEAD request error:", headError);
        }

        // Try to create a data URL to avoid CORS issues
        try {
          console.log("Attempting to create data URL");
          const dataUrl = await createDataUrl(processedSrc);
          if (dataUrl) {
            console.log("Successfully created data URL");
            setImageSource(dataUrl);
            return;
          }
        } catch (_error) {
          console.log("Data URL creation failed:", _error);
        }
      } catch (urlError) {
        console.log("URL processing error:", urlError);
      }

      // If data URL creation fails, try with alternative URLs if available
      if (alternativeUrls && alternativeUrls.length > 0) {
        console.log(`Trying ${alternativeUrls.length} alternative URLs`);

        for (const altUrl of alternativeUrls) {
          try {
            const dataUrl = await createDataUrl(altUrl);
            if (dataUrl) {
              console.log(`Successfully loaded alternative URL: ${altUrl}`);
              setImageSource(dataUrl);
              return;
            }
          } catch (altError) {
            console.log(`Alternative URL failed: ${altUrl}`, altError);
          }
        }
      }

      // If all data URL creation attempts fail, use the processed URL directly
      console.log("All attempts failed, using processed URL directly");
      setImageSource(constructUrl(src));
    };

    loadImage();
  }, [src, alternativeUrls]);

  // Cleanup ref on unmount
  useEffect(() => {
    isMounted.current = true;
    return () => {
      isMounted.current = false;
    };
  }, []);

  const handleError = (event: React.SyntheticEvent<SVGImageElement, Event>) => {
    // Check if component is still mounted before updating state
    if (!isMounted.current) return;

    if (!hasErrored && imageSource !== fallbackSrc) {
      console.log(`Image load error for: ${imageSource}`);

      // Log additional information for debugging
      console.log(`Error event:`, event);
      console.log(`Current attempts: ${loadAttempts}`);
      console.log(`Image source: ${imageSource}`);
      console.log(`Original src: ${src}`);

      // Try to fetch the image with a GET request to see the actual error
      fetch(imageSource, {
        method: "GET",
        cache: "no-cache",
        headers: {
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
      })
        .then((response) => {
          console.log(`Fetch response status: ${response.status}`);
          response.headers.forEach((value, key) => {
            console.log(`Header ${key}: ${value}`);
          });
          return response.text();
        })
        .then((text) => {
          if (text.length < 1000) {
            console.log(`Response text: ${text}`);
          } else {
            console.log(
              `Response text (truncated): ${text.substring(0, 1000)}...`,
            );
          }
        })
        .catch((error) => {
          console.log("Fetch error:", error);
        });

      // Try alternative approaches before falling back
      if (loadAttempts < 15) {
        // Increase max attempts to try all alternatives
        const nextAttempt = loadAttempts + 1;
        setLoadAttempts(nextAttempt);

        // First try any provided alternative URLs
        if (
          alternativeUrls &&
          alternativeUrls.length > 0 &&
          nextAttempt <= alternativeUrls.length
        ) {
          const alternativeUrl = alternativeUrls[nextAttempt - 1];
          console.log(
            `Trying alternative URL ${nextAttempt}: ${alternativeUrl}`,
          );
          setImageSource(alternativeUrl);
          return;
        }

        // If we've exhausted alternative URLs or none were provided, try different URL formats
        const remainingAttempts = nextAttempt - (alternativeUrls?.length || 0);

        // Extract filename and project ID from the source URL
        const segments = src.split("/");
        const filename = segments.pop() || "";
        const projectId =
          segments.length > 1 ? segments[segments.length - 1] : "";

        // Try different URL formats based on remaining attempts
        if (remainingAttempts === 1) {
          // Try with direct URL
          const directUrl = `/uploads/${filename}`;
          console.log(`Trying direct URL: ${directUrl}`);
          setImageSource(directUrl);
          return;
        } else if (remainingAttempts === 2) {
          // Try with project-specific URL
          if (projectId) {
            const projectUrl = `/uploads/${projectId}/${filename}`;
            console.log(`Trying project URL: ${projectUrl}`);
            setImageSource(projectUrl);
            return;
          }
        } else if (remainingAttempts === 3) {
          // Try with API URL
          const apiUrl = `/api/uploads/${filename}`;
          console.log(`Trying API URL: ${apiUrl}`);
          setImageSource(apiUrl);
          return;
        } else if (remainingAttempts === 4) {
          // Try with project-specific API URL
          if (projectId) {
            const projectApiUrl = `/api/uploads/${projectId}/${filename}`;
            console.log(`Trying project API URL: ${projectApiUrl}`);
            setImageSource(projectApiUrl);
            return;
          }
        } else if (remainingAttempts === 5) {
          // Try with absolute URL
          const absoluteUrl = `${window.location.origin}${src}`;
          console.log(`Trying absolute URL: ${absoluteUrl}`);
          setImageSource(absoluteUrl);
          return;
        } else if (remainingAttempts === 6) {
          // Try with cache-busting parameter
          const cacheBustUrl = `${src}?_=${Date.now()}`;
          console.log(`Trying cache-bust URL: ${cacheBustUrl}`);
          setImageSource(cacheBustUrl);
          return;
        } else if (remainingAttempts === 7) {
          // Try with direct backend URL
          const backendUrl = `http://localhost:5001/uploads/${filename}`;
          console.log(`Trying backend URL: ${backendUrl}`);
          setImageSource(backendUrl);
          return;
        } else if (remainingAttempts === 8) {
          // Try with project-specific backend URL
          if (projectId) {
            const projectBackendUrl = `http://localhost:5001/uploads/${projectId}/${filename}`;
            console.log(`Trying project backend URL: ${projectBackendUrl}`);
            setImageSource(projectBackendUrl);
            return;
          }
        } else if (remainingAttempts === 9) {
          // Try with Docker network URL
          const dockerUrl = `http://cellseg-backend:5000/uploads/${filename}`;
          console.log(`Trying Docker URL: ${dockerUrl}`);
          setImageSource(dockerUrl);
          return;
        } else if (remainingAttempts === 10) {
          // Try with project-specific Docker network URL
          if (projectId) {
            const projectDockerUrl = `http://cellseg-backend:5000/uploads/${projectId}/${filename}`;
            console.log(`Trying project Docker URL: ${projectDockerUrl}`);
            setImageSource(projectDockerUrl);
            return;
          }
        }
      }

      // If all attempts fail, use fallback
      setHasErrored(true);
      setImageSource(fallbackSrc);
      toast.error("Failed to load image, using placeholder image");
    }
  };

  const handleLoad = (event: React.SyntheticEvent<SVGImageElement, Event>) => {
    // Check if component is still mounted before updating state or calling onLoad
    if (!isMounted.current) return;

    // Only call the onLoad prop if the image loaded successfully
    console.log(`Image loaded successfully: ${imageSource}`);
    console.log(`Image dimensions: ${width}x${height}`);
    console.log(`Load attempts: ${loadAttempts}`);

    // Try to get the actual dimensions of the loaded image
    try {
      const img = new Image();
      img.onload = () => {
        console.log(
          `Actual image dimensions: ${img.naturalWidth}x${img.naturalHeight}`,
        );

        // If the actual dimensions are different from the props, call onLoad with the actual dimensions
        if (img.naturalWidth !== width || img.naturalHeight !== height) {
          console.log("Using actual dimensions");
          if (onLoad) {
            onLoad(img.naturalWidth, img.naturalHeight);
          }
        } else {
          // If dimensions match, call onLoad with the props
          if (onLoad) {
            onLoad(width, height);
          }
        }
      };
      img.onerror = () => {
        console.log("Error getting actual dimensions, using props");
        // Fall back to using the props
        if (onLoad) {
          onLoad(width, height);
        }
      };
      img.src = imageSource;
    } catch (_error) {
      console.log("Exception getting actual dimensions:", _error);
      // Fall back to using the props
      if (onLoad) {
        onLoad(width, height);
      }
    }

    // Show success toast if we recovered from errors
    if (loadAttempts > 0 && !hasErrored) {
      toast.success("Image loaded successfully");
    }
  };

  return (
    <image
      href={imageSource} // Use the state variable which might be src or fallbackSrc
      width={width}
      height={height}
      onLoad={handleLoad}
      onError={handleError}
      style={{ imageRendering: "crisp-edges" }}
    />
  );
};

export default FallbackImage;
