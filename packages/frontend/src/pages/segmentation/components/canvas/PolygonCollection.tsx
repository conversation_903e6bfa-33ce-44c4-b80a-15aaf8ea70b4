import { useMemo } from "react";
import { Polygon } from "@/lib/segmentation";
import CanvasPolygon from "./CanvasPolygon";
import { VertexDragState } from "@/pages/segmentation/types";
import { EditMode } from "@/pages/segmentation/hooks/useSegmentationEditor";
import { simplifyPolygon } from '@spheroseg/shared/utils/polygonUtils.unified';

// Utility functions for polygon processing
const sortPolygons = (polygons: Polygon[]) =>
  [...polygons].sort((a, b) => a.id.localeCompare(b.id));

const simplifyPolygons = (polygons: Polygon[], epsilon: number = 1.0) => 
  polygons.map(polygon => ({
    ...polygon,
    points: simplifyPolygon(polygon.points, epsilon)
  }));

const separatePolygonsByType = (polygons: Polygon[]) => ({
  externalPolygons: polygons.filter((p) => p.type !== "internal"),
  internalPolygons: polygons.filter((p) => p.type === "internal"),
});
const createPolygonProps = (
  polygon: Polygon,
  selectedId: string | null,
  hoveredVtx: any,
  dragState: any,
  mode: any,
  handlers: any,
  allPolygons: Polygon[],
) => ({
  polygon,
  isSelected: polygon.id === selectedId,
  isHovered: false, // You may want to pass this from parent
  hoveredVertex: hoveredVtx,
  vertexDragState: dragState,
  editMode: mode,
  ...handlers,
  relatedPolygons: allPolygons,
}); // Placeholder

interface PolygonCollectionProps {
  polygons: Polygon[];
  selectedPolygonId: string | null;
  hoveredVertex: { polygonId: string | null; vertexIndex: number | null };
  vertexDragState: VertexDragState;
  editMode: EditMode;
  onSelectPolygon?: (id: string) => void;
  onDeletePolygon?: (id: string) => void;
  onSlicePolygon?: (id: string) => void;
  onEditPolygon?: (id: string) => void;
  onDeleteVertex?: (polygonId: string, vertexIndex: number) => void;
  onDuplicateVertex?: (polygonId: string, vertexIndex: number) => void;
}

// Use Omit to reflect removed props in the type signature
const PolygonCollection = ({
  polygons,
  selectedPolygonId,
  hoveredVertex,
  vertexDragState,
  editMode,
  onSelectPolygon,
  onDeletePolygon,
  onSlicePolygon,
  onEditPolygon,
  onDeleteVertex,
  onDuplicateVertex,
}: Omit<
  PolygonCollectionProps,
  "zoom" | "offset" | "canvasWidth" | "canvasHeight"
>) => {
  // --- Move Hook calls before early return ---
  const isHoveredPolygonId = hoveredVertex.polygonId;

  const visiblePolygons = useMemo(() => {
    if (!polygons) return [];

    // Use shared utilities for sorting and simplifying polygons
    const sortedPolygons = sortPolygons(polygons);
    return simplifyPolygons(sortedPolygons);
  }, [polygons, selectedPolygonId]);

  // Use shared utility for separating polygons by type
  const { externalPolygons, internalPolygons } = useMemo(
    () => separatePolygonsByType(visiblePolygons),
    [visiblePolygons],
  );
  // --- End moving Hook calls ---

  // Now perform the early return check
  if (!polygons) {
    return <g></g>;
  }

  // Create common handlers object to reduce duplication
  const handlers = {
    onSelectPolygon,
    onDeletePolygon,
    onSlicePolygon,
    onEditPolygon,
    onDeleteVertex,
    onDuplicateVertex,
  };

  return (
    <g>
      {/* Render external polygons first */}
      {externalPolygons.map((polygon) => (
        <CanvasPolygon
          key={polygon.id}
          {...createPolygonProps(
            polygon,
            selectedPolygonId,
            hoveredVertex,
            vertexDragState,
            editMode,
            handlers,
            visiblePolygons,
          )}
        />
      ))}

      {/* Render internal polygons (holes) */}
      {internalPolygons.map((polygon) => (
        <CanvasPolygon
          key={polygon.id}
          {...createPolygonProps(
            polygon,
            selectedPolygonId,
            hoveredVertex,
            vertexDragState,
            editMode,
            handlers,
            visiblePolygons,
          )}
        />
      ))}
    </g>
  );
};

export default PolygonCollection;
