import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { SegmentationEditorV2 } from '../../SegmentationEditorV2';
import { setupAllContextMocks } from '@/test-utils/contextMocks';
import { MemoryRouterWrapper } from '@/test-utils/test-wrapper';
import { toast } from 'sonner';

// Mock API_BASE_URL
vi.mock('@/config', () => ({
  API_BASE_URL: 'http://test-api',
}));

// Create a mock state object that can be modified between tests
let mockSegmentationState: any;

// Mock useSegmentationV2 hook with customizable state for testing
vi.mock('../../hooks/segmentation', () => ({
  useSegmentationV2: vi.fn(() => mockSegmentationState),
  EditMode: {
    View: 'View',
    EditVertices: 'EditVertices',
    AddPolygon: 'AddPolygon',
    DeletePolygon: 'DeletePolygon',
    SlicePolygon: 'SlicePolygon',
    MergePolygons: 'MergePolygons',
  },
}));

// Mock useSlicing hook
vi.mock('../../hooks/useSlicing', () => ({
  useSlicing: vi.fn(() => ({
    handleSliceAction: vi.fn(),
  })),
}));

// Mock react-router-dom's useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() => mockNavigate),
  };
});

// Mock CanvasV2 component
vi.mock('../../components/canvas/CanvasV2', () => ({
  default: vi.fn(
    ({
      imageData,
      segmentationData,
      transform,
      editMode,
      selectedPolygonId,
      hoveredVertex,
      tempPoints,
      interactionState,
      onMouseDown,
      onMouseMove,
      onMouseUp,
      getCanvasCoordinates,
    }: any) => {
      return (
        <div
          data-testid="canvas-v2"
          onMouseDown={onMouseDown}
          onMouseMove={onMouseMove}
          onMouseUp={onMouseUp}
        >
          <div data-testid="canvas-state">
            {editMode} - {selectedPolygonId || 'none'}
          </div>
        </div>
      );
    },
  ),
}));

// Mock the toolbar components
vi.mock('../../components/toolbar', () => ({
  Toolbar: vi.fn(({ onEditModeChange, editMode }: any) => (
    <div data-testid="toolbar">
      <button onClick={() => onEditModeChange('View')}>View</button>
      <button onClick={() => onEditModeChange('EditVertices')}>Edit Vertices</button>
      <button onClick={() => onEditModeChange('AddPolygon')}>Add Polygon</button>
      <button onClick={() => onEditModeChange('DeletePolygon')}>Delete</button>
      <button onClick={() => onEditModeChange('SlicePolygon')}>Slice</button>
      <div data-testid="current-mode">{editMode}</div>
    </div>
  )),
  ViewToolbar: vi.fn(({ onZoomIn, onZoomOut, onResetView }: any) => (
    <div data-testid="view-toolbar">
      <button data-testid="zoom-in-btn" onClick={onZoomIn}>
        Zoom In
      </button>
      <button data-testid="zoom-out-btn" onClick={onZoomOut}>
        Zoom Out
      </button>
      <button data-testid="reset-view-btn" onClick={onResetView}>
        Reset View
      </button>
    </div>
  )),
}));

// Mock the cell list component
vi.mock('../../components/CellList', () => ({
  default: vi.fn(({ polygons, selectedPolygonId, onPolygonSelect }: any) => (
    <div data-testid="cell-list">
      {polygons?.map((polygon: any) => (
        <div
          key={polygon.id}
          data-testid={`cell-${polygon.id}`}
          onClick={() => onPolygonSelect(polygon.id)}
          className={selectedPolygonId === polygon.id ? 'selected' : ''}
        >
          {polygon.label}
        </div>
      )) || 'No polygons'}
    </div>
  )),
}));

// Mock sonner toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}));

// Mock ResegmentationModal
vi.mock('../../components/ResegmentationModal', () => ({
  default: vi.fn(({ isOpen, onClose, onConfirm }: any) =>
    isOpen ? (
      <div data-testid="resegmentation-modal">
        <button onClick={onConfirm}>Confirm Resegmentation</button>
        <button onClick={onClose}>Cancel</button>
      </div>
    ) : null,
  ),
}));

// Mock the API client
const mockApiClient = {
  post: vi.fn(),
  get: vi.fn(),
  put: vi.fn(),
};

vi.mock('@/services/api/client', () => ({
  default: mockApiClient,
}));

// Create test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MemoryRouterWrapper initialEntries={['/projects/test-project/images/test-image/segmentation']}>
    {children}
  </MemoryRouterWrapper>
);

// Helper function to render component with context
const renderComponent = () => {
  return render(
    <TestWrapper>
      <SegmentationEditorV2 projectId="test-project" imageId="test-image" />
    </TestWrapper>,
  );
};

// Helper function to trigger wheel event on canvas
const triggerWheelEvent = (deltaY: number) => {
  const canvas = screen.getByTestId('canvas-v2');
  fireEvent.wheel(canvas, { deltaY, clientX: 400, clientY: 300 });
};

// Global mock for getBoundingClientRect
HTMLElement.prototype.getBoundingClientRect = vi.fn(() => ({
  width: 800,
  height: 600,
  top: 0,
  left: 0,
  right: 800,
  bottom: 600,
  x: 0,
  y: 0,
  toJSON: () => ({}),
}));

// Mock for ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock fetch for API calls
global.fetch = vi.fn();

// Mock location.reload
const mockReload = vi.fn();
Object.defineProperty(window, 'location', {
  value: {
    ...window.location,
    reload: mockReload,
  },
  writable: true,
});

describe('SegmentationEditorV2 Component (Advanced Tests)', () => {
  beforeEach(() => {
    // Setup all context mocks
    setupAllContextMocks();

    // Reset fetch mock
    vi.mocked(global.fetch).mockReset();
    vi.mocked(toast.success).mockReset();
    vi.mocked(toast.error).mockReset();
    vi.mocked(toast.info).mockReset();
    mockReload.mockReset();
    vi.mocked(mockNavigate).mockReset();

    // Reset the mock state for each test
    mockSegmentationState = {
      imageData: {
        id: 'test-image-id',
        actualId: 'test-image-id',
        name: 'test-image.jpg',
        url: 'https://example.com/test-image.jpg',
        width: 800,
        height: 600,
      },
      segmentationData: {
        polygons: [
          {
            id: 'polygon-1',
            points: [
              { x: 100, y: 100 },
              { x: 200, y: 100 },
              { x: 200, y: 200 },
              { x: 100, y: 200 },
            ],
            color: '#FF0000',
            label: 'Cell 1',
          },
        ],
        width: 800,
        height: 600,
      },
      transform: { zoom: 1, translateX: 0, translateY: 0 },
      editMode: 'View',
      selectedPolygonId: null,
      hoveredVertex: null,
      tempPoints: [],
      interactionState: null,
      isLoading: false,
      isSaving: false,
      error: null,
      canUndo: false,
      canRedo: false,
      setEditMode: vi.fn(),
      setSelectedPolygonId: vi.fn(),
      setTransform: vi.fn(),
      setTempPoints: vi.fn(),
      setInteractionState: vi.fn(),
      setSegmentationDataWithHistory: vi.fn(),
      handleSave: vi.fn(),
      undo: vi.fn(),
      redo: vi.fn(),
      onMouseDown: vi.fn(),
      onMouseMove: vi.fn(),
      onMouseUp: vi.fn(),
      getCanvasCoordinates: vi.fn(),
      handleWheelEvent: vi.fn(),
      fetchData: vi.fn(),
      handleResegment: vi.fn(),
      isResegmenting: false,
      setHoveredVertex: vi.fn(),
    };

    // Mock API client methods
    mockApiClient.post.mockReset();
    mockApiClient.get.mockReset();
    mockApiClient.put.mockReset();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render the component with all main sections', () => {
      renderComponent();

      expect(screen.getByTestId('canvas-v2')).toBeInTheDocument();
      expect(screen.getByTestId('toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('view-toolbar')).toBeInTheDocument();
      expect(screen.getByTestId('cell-list')).toBeInTheDocument();
    });

    it('should display the image name', () => {
      renderComponent();
      expect(screen.getByText('test-image.jpg')).toBeInTheDocument();
    });
  });

  describe('Image ID Resolution and URL Updates', () => {
    it('should update URL when actualId differs from imageId', async () => {
      // Update the mock state to return an actualId different from the imageId
      mockSegmentationState.imageData.actualId = 'actual-id-123';

      renderComponent();

      // Wait for the navigate to be called
      await waitFor(() => {
        expect(mockNavigate).toHaveBeenCalledWith(
          '/projects/test-project/images/actual-id-123/segmentation',
          { replace: true },
        );
      });
    });

    it('should not update URL when actualId matches imageId', async () => {
      // Update the mock state to have matching actualId and imageId
      mockSegmentationState.imageData.actualId = 'test-image-id';

      renderComponent();

      // Wait a bit to ensure navigate is not called
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should show loading state when isLoading is true', () => {
      mockSegmentationState.isLoading = true;

      renderComponent();

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('should handle failed resegmentation API call gracefully', async () => {
      mockApiClient.post.mockRejectedValueOnce(new Error('Network error'));

      renderComponent();

      // Click resegment button
      fireEvent.click(screen.getByText(/resegment/i));

      // Confirm resegmentation
      await waitFor(() => {
        expect(screen.getByTestId('resegmentation-modal')).toBeInTheDocument();
      });
      fireEvent.click(screen.getByText('Confirm Resegmentation'));

      // Check error toast
      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('error'));
      });
    });

    it('should handle server error during resegmentation', async () => {
      mockApiClient.post.mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      renderComponent();

      fireEvent.click(screen.getByText(/resegment/i));
      await waitFor(() => {
        expect(screen.getByTestId('resegmentation-modal')).toBeInTheDocument();
      });
      fireEvent.click(screen.getByText('Confirm Resegmentation'));

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalled();
      });
    });

    it('should handle missing data for resegmentation', async () => {
      mockSegmentationState.imageData = null;

      renderComponent();

      // Try to resegment without image data
      const resegmentButton = screen.queryByText(/resegment/i);
      expect(resegmentButton).toBeDisabled();
    });

    it('should handle successful resegmentation but failed processing', async () => {
      mockApiClient.post.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: false, error: 'Processing failed' }),
      });

      renderComponent();

      fireEvent.click(screen.getByText(/resegment/i));
      await waitFor(() => {
        expect(screen.getByTestId('resegmentation-modal')).toBeInTheDocument();
      });
      fireEvent.click(screen.getByText('Confirm Resegmentation'));

      await waitFor(() => {
        expect(toast.error).toHaveBeenCalledWith(expect.stringContaining('failed'));
      });
      expect(mockReload).not.toHaveBeenCalled();
    });
  });

  describe('Canvas Interactions', () => {
    it('should pass through mouse events to the canvas', () => {
      renderComponent();

      const canvas = screen.getByTestId('canvas-v2');

      fireEvent.mouseDown(canvas);
      fireEvent.mouseMove(canvas);
      fireEvent.mouseUp(canvas);

      expect(mockSegmentationState.onMouseDown).toHaveBeenCalled();
      expect(mockSegmentationState.onMouseMove).toHaveBeenCalled();
      expect(mockSegmentationState.onMouseUp).toHaveBeenCalled();
    });
  });

  describe('Transform and View', () => {
    it('should correctly handle zoom in', () => {
      renderComponent();

      // Click zoom in button
      fireEvent.click(screen.getByTestId('zoom-in-btn'));

      // Check that setTransform was called
      expect(mockSegmentationState.setTransform).toHaveBeenCalled();

      // Get the transform function that was passed to setTransform
      const transformFn = mockSegmentationState.setTransform.mock.calls[0][0];
      const result = transformFn({ zoom: 1, translateX: 0, translateY: 0 });

      // Check result
      expect(result).toEqual({
        zoom: 1.2,
        translateX: 0,
        translateY: 0,
      });
    });

    it('should correctly handle zoom out', () => {
      renderComponent();

      // Click zoom out button
      fireEvent.click(screen.getByTestId('zoom-out-btn'));

      // Check that setTransform was called
      expect(mockSegmentationState.setTransform).toHaveBeenCalled();

      // Get the transform function that was passed to setTransform
      const transformFn = mockSegmentationState.setTransform.mock.calls[0][0];
      const result = transformFn({ zoom: 1, translateX: 0, translateY: 0 });

      // Check result
      expect(result).toEqual({
        zoom: expect.closeTo(0.833, 3),
        translateX: 0,
        translateY: 0,
      });
    });

    it('should handle reset view with an Element.getBoundingClientRect mock', () => {
      renderComponent();

      // Click reset view button
      fireEvent.click(screen.getByTestId('reset-view-btn'));

      // Check that setTransform was called
      expect(mockSegmentationState.setTransform).toHaveBeenCalled();
    });
  });

  describe('Edit Modes', () => {
    it('should change edit mode when toolbar buttons are clicked', () => {
      renderComponent();

      fireEvent.click(screen.getByRole('button', { name: 'View' }));
      expect(mockSegmentationState.setEditMode).toHaveBeenCalledWith('View');

      fireEvent.click(screen.getByRole('button', { name: /edit/i }));
      expect(mockSegmentationState.setEditMode).toHaveBeenCalledWith('EditVertices');

      fireEvent.click(screen.getByRole('button', { name: /add.*polygon/i }));
      expect(mockSegmentationState.setEditMode).toHaveBeenCalledWith('AddPolygon');

      fireEvent.click(screen.getByRole('button', { name: /delete/i }));
      expect(mockSegmentationState.setEditMode).toHaveBeenCalledWith('DeletePolygon');

      fireEvent.click(screen.getByRole('button', { name: /slice/i }));
      expect(mockSegmentationState.setEditMode).toHaveBeenCalledWith('SlicePolygon');
    });
  });

  describe('Resegmentation Process', () => {
    it('should handle successful resegmentation with proper toast notifications', async () => {
      mockApiClient.post.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      renderComponent();

      // Click resegment button
      fireEvent.click(screen.getByText(/resegment/i));

      // Check modal appears
      await waitFor(() => {
        expect(screen.getByTestId('resegmentation-modal')).toBeInTheDocument();
      });

      // Confirm resegmentation
      fireEvent.click(screen.getByText('Confirm Resegmentation'));

      // Check API was called
      await waitFor(() => {
        expect(mockApiClient.post).toHaveBeenCalledWith(
          expect.stringContaining('test-image-id'),
          expect.any(Object),
        );
      });

      // Check success toast
      expect(toast.success).toHaveBeenCalled();

      // Check page reload was called
      expect(mockReload).toHaveBeenCalled();
    });
  });
});