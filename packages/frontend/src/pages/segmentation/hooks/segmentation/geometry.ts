import { Point, Polygon, SegmentationData } from "./types";

/**
 * Check if a point is inside a polygon
 */
export const isPointInPolygon = (point: Point, polygon: Point[]): boolean => {
  let isInside = false;
  for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
    if (((polygon[i].y > point.y) !== (polygon[j].y > point.y)) &&
        (point.x < (polygon[j].x - polygon[i].x) * (point.y - polygon[i].y) / (polygon[j].y - polygon[i].y) + polygon[i].x)) {
      isInside = !isInside;
    }
  }
  return isInside;
};

/**
 * Calculate distance from a point to a line segment
 */
export const distanceToSegment = (p: Point, v: Point, w: Point): number => {
  const l2 = Math.pow(v.x - w.x, 2) + Math.pow(v.y - w.y, 2);
  if (l2 === 0) return Math.sqrt(Math.pow(p.x - v.x, 2) + Math.pow(p.y - v.y, 2));
  
  let t = ((p.x - v.x) * (w.x - v.x) + (p.y - v.y) * (w.y - v.y)) / l2;
  t = Math.max(0, Math.min(1, t));
  
  const projection = {
    x: v.x + t * (w.x - v.x),
    y: v.y + t * (w.y - v.y)
  };
  
  return Math.sqrt(Math.pow(p.x - projection.x, 2) + Math.pow(p.y - projection.y, 2));
};


/**
 * Slice a polygon with a line
 * Note: This is a simplified implementation
 */
export const slicePolygon = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): { success: boolean; polygons: Polygon[] } => {
  // For now, return the original polygon as slicing is not implemented
  // This maintains functionality while we fix the build
  return { 
    success: false, 
    polygons: [polygon] 
  };
};

/**
 * Create a new polygon
 */
export const createPolygon = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  // Calculate area
  let area = 0;
  for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
    area += (points[j].x + points[i].x) * (points[j].y - points[i].y);
  }
  area = Math.abs(area) / 2;
  
  return {
    id: `polygon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    points,
    type,
    area
  };
};

/**
 * Update segmentation data with a new set of polygons
 */
export const updateSegmentationWithPolygons = (
  segmentationData: SegmentationData,
  polygons: Polygon[],
): SegmentationData => {
  return {
    ...segmentationData,
    polygons,
  };
};
