import { v4 as uuidv4 } from "uuid";
import { Point, Polygon, SegmentationData } from "./types";
import usePolygonWorker from "../usePolygonWorker";
import { createLogger } from "@/lib/logger";
// Local implementations until shared utilities are available
const calculatePolygonAreaAsync = async (points: Point[]): Promise<number> => {
  // Placeholder implementation using shoelace formula
  let area = 0;
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length;
    area += points[i].x * points[j].y;
    area -= points[j].x * points[i].y;
  }
  return Math.abs(area) / 2;
};

const calculatePolygonPerimeterAsync = async (
  points: Point[],
): Promise<number> => {
  // Placeholder implementation
  let perimeter = 0;
  for (let i = 0; i < points.length; i++) {
    const j = (i + 1) % points.length;
    const dx = points[j].x - points[i].x;
    const dy = points[j].y - points[i].y;
    perimeter += Math.sqrt(dx * dx + dy * dy);
  }
  return perimeter;
};

const calculateBoundingBoxAsync = async (
  points: Point[],
): Promise<{ x: number; y: number; width: number; height: number }> => {
  // Placeholder implementation
  if (points.length === 0) return { x: 0, y: 0, width: 0, height: 0 };
  let minX = points[0].x,
    minY = points[0].y,
    maxX = points[0].x,
    maxY = points[0].y;
  for (const point of points) {
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }
  return { x: minX, y: minY, width: maxX - minX, height: maxY - minY };
};

const executePolygonWorkerOperation = async (
  points: Point[],
  polygonWorker: any,
  operation: (pts: Point[]) => any,
  operationName: string,
  fallback: any,
): Promise<any> => {
  // Placeholder implementation
  try {
    if (polygonWorker.isReady) {
      return await operation(points);
    }
    return fallback;
  } catch {
    return fallback;
  }
};

const slicePolygonShared = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): Polygon[] => {
  // Placeholder implementation - return original polygon
  return [polygon];
};

const distanceToLineSegment = (p: Point, v: Point, w: Point): number => {
  // Placeholder implementation using point-to-line distance formula
  const l2 = (v.x - w.x) * (v.x - w.x) + (v.y - w.y) * (v.y - w.y);
  if (l2 === 0)
    return Math.sqrt((p.x - v.x) * (p.x - v.x) + (p.y - v.y) * (p.y - v.y));
  const t = Math.max(
    0,
    Math.min(1, ((p.x - v.x) * (w.x - v.x) + (p.y - v.y) * (w.y - v.y)) / l2),
  );
  const projection = { x: v.x + t * (w.x - v.x), y: v.y + t * (w.y - v.y) };
  return Math.sqrt(
    (p.x - projection.x) * (p.x - projection.x) +
      (p.y - projection.y) * (p.y - projection.y),
  );
};

const createPolygon = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  // Placeholder implementation
  return {
    id: `polygon-${Date.now()}`,
    points,
    type,
  };
};

const logger = createLogger("segmentation:geometry.worker");

/**
 * Check if a point is inside a polygon using WebWorker
 */
export const isPointInPolygonAsync = async (
  x: number,
  y: number,
  points: Point[],
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<boolean> => {
  try {
    if (!polygonWorker.isReady) {
      logger.warn(
        "Polygon worker not ready, falling back to synchronous implementation",
      );
      return isPointInPolygonSync(x, y, points);
    }

    return await polygonWorker.isPointInPolygon({ x, y }, points);
  } catch (_error) {
    logger.error("Error in isPointInPolygonAsync:", _error);
    return isPointInPolygonSync(x, y, points);
  }
};

/**
 * Synchronous fallback for point in polygon check
 */
export const isPointInPolygonSync = (
  x: number,
  y: number,
  points: Point[],
): boolean => {
  let inside = false;
  for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
    const xi = points[i].x;
    const yi = points[i].y;
    const xj = points[j].x;
    const yj = points[j].y;

    const intersect =
      yi > y !== yj > y && x < ((xj - xi) * (y - yi)) / (yj - yi) + xi;
    if (intersect) inside = !inside;
  }
  return inside;
};

/**
 * Calculate distance from a point to a line segment
 */
export const distanceToSegment = (p: Point, v: Point, w: Point): number => {
  return distanceToLineSegment(p, v, w);
};

/**
 * Slice a polygon with a line using WebWorker
 */
export const slicePolygonAsync = async (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<{ success: boolean; polygons: Polygon[] }> => {
  try {
    if (!polygonWorker.isReady) {
      logger.warn(
        "Polygon worker not ready, falling back to synchronous implementation",
      );
      return slicePolygonSync(polygon, sliceStart, sliceEnd);
    }

    const result = await polygonWorker.slicePolygon(
      polygon.points,
      sliceStart,
      sliceEnd,
    );

    if (!result || result.length < 2) {
      return { success: false, polygons: [] };
    }

    // Create new polygon objects from the result
    const newPolygons = result.map((points) => ({
      id: uuidv4(),
      points,
      type: polygon.type || "external",
    }));

    return { success: true, polygons: newPolygons };
  } catch (_error) {
    logger.error("Error in slicePolygonAsync:", _error);
    return slicePolygonSync(polygon, sliceStart, sliceEnd);
  }
};

/**
 * Synchronous fallback for slicing a polygon
 */
export const slicePolygonSync = (
  polygon: Polygon,
  sliceStart: Point,
  sliceEnd: Point,
): { success: boolean; polygons: Polygon[] } => {
  const result = slicePolygonShared(polygon, sliceStart, sliceEnd);

  if (!result) {
    return { success: false, polygons: [] };
  }

  return { success: true, polygons: result };
};

/**
 * Create a new polygon
 */
export const createPolygonFn = (
  points: Point[],
  type: "external" | "internal" = "external",
): Polygon => {
  return createPolygon(points, type);
};

/**
 * Update segmentation data with a new set of polygons
 */
export const updateSegmentationWithPolygons = (
  segmentationData: SegmentationData,
  polygons: Polygon[],
): SegmentationData => {
  return {
    ...segmentationData,
    polygons,
  };
};

/**
 * Simplify a polygon using the Ramer-Douglas-Peucker algorithm with WebWorker
 */
export const simplifyPolygonAsync = async (
  points: Point[],
  epsilon: number,
  polygonWorker: ReturnType<typeof usePolygonWorker>,
): Promise<Point[]> => {
  return executePolygonWorkerOperation(
    points,
    polygonWorker,
    (pts) => polygonWorker.simplifyPolygon(pts, epsilon),
    "simplifyPolygonAsync",
    points,
  );
};

// Export shared utility functions
export {
  calculatePolygonAreaAsync,
  calculatePolygonPerimeterAsync,
  calculateBoundingBoxAsync,
};
