import apiClient from "@/services/api/client";
import { projectsApi, imagesApi } from "@/services/api/endpoints";
import { ImageData, SegmentationData, Polygon } from "./types";
import { loadImageDirectly } from "@/pages/segmentation/utils/directImageLoader";
import { getLogger } from "@/utils/logging/unifiedLogger";

const logger = getLogger("segmentation:api");
import {
  processImageUrl,
  generateCacheBuster,
  createPlaceholderImageData,
} from "@/utils/urlNormalization";


/**
 * Fetch image data by ID or name
 */
export const fetchImageData = async (
  projectId: string,
  imageId: string,
  signal?: AbortSignal,
): Promise<ImageData> => {
  logger.info(`Starting fetch for projectId=${projectId}, imageId=${imageId}`);

  // Check if this is a local image (starts with 'img-')
  const isLocalImage = String(imageId).startsWith("img-");

  // For local images, try to get from localStorage first
  if (isLocalImage) {
    try {
      const storageKey = `spheroseg_uploaded_images_${projectId}`;
      const storedImagesJson = localStorage.getItem(storageKey);

      if (storedImagesJson) {
        const storedImages = JSON.parse(storedImagesJson);
        const localImage = storedImages.find(
          (img: unknown) =>
            typeof img === "object" &&
            img !== null &&
            "id" in img &&
            img.id === imageId,
        );

        if (localImage) {
          logger.info(`Found local image ${imageId} in localStorage`);

          // Create image data from the localStorage result
          const localImageData: ImageData = {
            id: imageId,
            name: localImage.name || `Image ${imageId}`,
            width: localImage.width || 800,
            height: localImage.height || 600,
            src: localImage.url,
            storage_path: localImage.url,
            project_id: projectId,
            created_at: localImage.createdAt || new Date().toISOString(),
            updated_at: localImage.updatedAt || new Date().toISOString(),
            status: "completed",
            alternativeUrls: [localImage.url],
          };

          return processImageUrl(localImageData);
        }
      }
    } catch (localStorageError) {
      logger.warn(
        `Error fetching image from localStorage: ${localStorageError instanceof Error ? localStorageError.message : String(localStorageError)}`,
      );
    }
  }

  // Add cache-busting parameter to prevent browser caching
  const cacheBuster = generateCacheBuster();

  // First try to get all images in the project and find the one with matching ID
  try {
    logger.debug(`Fetching all images in project ${projectId}`);
    const allImagesResponse = await imagesApi.list(projectId, {});

    if (allImagesResponse.data && Array.isArray(allImagesResponse.data.data)) {
      logger.debug(`Found ${allImagesResponse.data.data.length} images in project`);

      // Find the image with the matching ID
      const matchingImage = allImagesResponse.data.data.find(
        (img) => img.id === imageId,
      );

      if (matchingImage) {
        logger.info(
          `Found matching image by ID in project images: ${matchingImage.id}`,
        );
        logger.debug("Raw image data from API:", JSON.stringify(matchingImage));
        return processImageUrl(matchingImage);
      } else {
        logger.warn(`No image with ID ${imageId} found in project images`);
      }
    }
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (_error instanceof Error && _error.message !== "canceled") {
      logger.error(
        `Error fetching all project images: ${_error instanceof Error ? _error.message : String(_error)}`,
      );
    }
  }

  // If not found in all images, try direct fetch by ID
  try {
    logger.debug(
      `Trying direct fetch by ID: /api/projects/${projectId}/images/${imageId}?${cacheBuster}`,
    );
    const imageResponse = await imagesApi.get(imageId);

    if (imageResponse.data) {
      logger.info(
        `Successfully fetched image by ID: ${(imageResponse.data as any)?.id}`,
      );
      return processImageUrl(imageResponse.data as ImageData);
    }
  } catch (idError) {
    // Only log if it's not a cancellation error
    if (
      idError instanceof Error &&
      idError.message !== "canceled" &&
      idError.name !== "AbortError"
    ) {
      logger.error(
        `Error fetching image by ID: ${idError instanceof Error ? idError.message : String(idError)}`,
      );
    }
  }

  // If still not found, try to load directly from filesystem
  try {
    logger.debug(`Attempting to load image directly from filesystem`);
    const directImageResult = await loadImageDirectly(projectId, imageId);

    if (directImageResult) {
      logger.info(
        `Successfully loaded image directly: ${directImageResult.url}`,
      );

      // Create image data from the direct load result
      const directImageData: ImageData = {
        id: imageId,
        name: `Image ${imageId}`,
        width: directImageResult.width,
        height: directImageResult.height,
        src: directImageResult.url,
        storage_path: directImageResult.url,
        project_id: projectId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        status: "completed",
        alternativeUrls: [directImageResult.url],
      };

      return processImageUrl(directImageData);
    }
  } catch (directError) {
    // Only log if it's not a cancellation error
    if (
      directError instanceof Error &&
      directError.message !== "canceled" &&
      directError.name !== "AbortError"
    ) {
      logger.error(
        `Error loading image directly: ${directError instanceof Error ? directError.message : String(directError)}`,
      );
    }
  }

  // If all attempts fail, create a placeholder (for any image, not just local ones)
  logger.info(`Creating placeholder for image ${imageId}`);

  // Log the fallback to placeholder
  logger.warn(
    `All attempts to fetch image failed, using placeholder: projectId=${projectId}, imageId=${imageId}`,
  );
  
  return createPlaceholderImageData(imageId, projectId) as ImageData;
};

// URL processing is now handled by the dedicated utility module

/**
 * Fetch segmentation data for an image
 */
export const fetchSegmentationData = async (
  imageId: string,
  signal?: AbortSignal,
  projectId?: string,
): Promise<SegmentationData> => {
  logger.info(
    `Starting fetch for segmentation data, imageId=${imageId}, projectId=${projectId || "not provided"}`,
  );

  // Add cache-busting parameter to prevent browser caching
  const cacheBuster = generateCacheBuster();

  // Try multiple endpoint formats in sequence
  const endpointsToTry = [`/images/${imageId}/segmentation?${cacheBuster}`];

  for (const endpoint of endpointsToTry) {
    try {
      logger.debug(`Fetching from: ${endpoint}`);
      const segmentationResponse = await apiClient.get(endpoint, {
        signal,
        deduplicate: true, // Use API client's built-in deduplication
      });
      const fetchedSegmentation = segmentationResponse.data;

      logger.debug(
        `Received segmentation data from ${endpoint} for imageId=${imageId}`,
      );

      // Convert API data to the format expected by our application
      if (
        fetchedSegmentation &&
        (fetchedSegmentation as any).result_data &&
        (fetchedSegmentation as any).result_data.polygons
      ) {
        // If we have data in format { result_data: { polygons: [...] } }
        logger.info(
          `Found ${(fetchedSegmentation as any).result_data.polygons.length} polygons in result_data for imageId=${imageId}`,
        );

        // Process polygons to ensure they have the correct format
        const processedPolygons = (
          fetchedSegmentation as any
        ).result_data.polygons
          .filter(
            (polygon: Polygon) =>
              polygon &&
              Array.isArray(polygon.points) &&
              polygon.points.length >= 3,
          )
          .map((polygon: Polygon) => ({
            ...polygon,
            id:
              polygon.id ||
              `poly-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: polygon.type || "external",
            points: polygon.points.map((p: unknown) => {
              // Handle both {x,y} and [x,y] formats
              if (Array.isArray(p)) {
                return { x: p[0], y: p[1] };
              } else if (typeof p === "object" && "x" in p && "y" in p) {
                return { x: p.x, y: p.y };
              }
              return p;
            }),
          }));

        logger.info(
          `Processed ${processedPolygons.length} valid polygons for imageId=${imageId}`,
        );
        (fetchedSegmentation as any).polygons = processedPolygons;
      } else if (
        fetchedSegmentation &&
        Array.isArray((fetchedSegmentation as any).polygons)
      ) {
        // If we have data in format { polygons: [...] }
        logger.info(
          `Found ${(fetchedSegmentation as any).polygons.length} polygons directly for imageId=${imageId}`,
        );

        // Process polygons to ensure they have the correct format
        const processedPolygons = (fetchedSegmentation as any).polygons
          .filter(
            (polygon: Polygon) =>
              polygon &&
              Array.isArray(polygon.points) &&
              polygon.points.length >= 3,
          )
          .map((polygon: Polygon) => ({
            ...polygon,
            id:
              polygon.id ||
              `poly-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            type: polygon.type || "external",
            points: polygon.points.map((p: unknown) => {
              // Handle both {x,y} and [x,y] formats
              if (Array.isArray(p)) {
                return { x: p[0], y: p[1] };
              } else if (typeof p === "object" && "x" in p && "y" in p) {
                return { x: p.x, y: p.y };
              }
              return p;
            }),
          }));

        logger.info(
          `Processed ${processedPolygons.length} valid polygons for imageId=${imageId}`,
        );
        (fetchedSegmentation as any).polygons = processedPolygons;
      } else if (
        fetchedSegmentation &&
        !(fetchedSegmentation as any).polygons
      ) {
        // If we don't have polygons, create an empty array
        logger.warn(
          `No polygons found for imageId=${imageId}, creating empty array`,
        );
        (fetchedSegmentation as any).polygons = [];
      }

      return fetchedSegmentation as SegmentationData;
    } catch (_error) {
      // Only log if it's not a cancellation _error
      if (
        _error instanceof Error &&
        _error.message !== "canceled" &&
        _error.name !== "AbortError"
      ) {
        logger.error(
          `Error fetching from ${endpoint} for imageId=${imageId}: ${_error instanceof Error ? _error.message : String(_error)}`,
        );
      }
      // Continue to the next endpoint
    }
  }

  // If we get here, all endpoints failed - create empty segmentation data instead of throwing
  logger.error(
    `All endpoints failed for imageId=${imageId}, creating empty segmentation data`,
  );
  return createEmptySegmentation(imageId);
};

/**
 * Create empty segmentation data
 */
export const createEmptySegmentation = (imageId: string): SegmentationData => {
  logger.info(`Creating empty segmentation for imageId=${imageId}`);

  const timestamp = new Date().toISOString();

  return {
    image_id: imageId,
    status: "completed", // Mark as completed so it can be edited
    result_data: {
      polygons: [],
    },
    polygons: [],
    created_at: timestamp,
    updated_at: timestamp,
  };
};

/**
 * Save segmentation data
 */
export const saveSegmentationData = async (
  projectId: string,
  imageId: string,
  actualId: string | undefined,
  segmentationData: SegmentationData,
): Promise<void> => {
  // Process polygons to ensure they have the correct format
  const processedPolygons = segmentationData.polygons
    .filter(
      (polygon) =>
        polygon && Array.isArray(polygon.points) && polygon.points.length >= 3,
    )
    .map((polygon) => ({
      ...polygon,
      id:
        polygon.id ||
        `poly-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      type: polygon.type || "external",
    }));

  logger.info(
    `Saving ${processedPolygons.length} valid polygons for imageId=${imageId}`,
  );

  // Prepare the data for saving - backend expects status field
  const dataToSave = {
    status: "completed", // Backend requires this field
    result_data: {
      polygons: processedPolygons,
      metadata: {
        processedAt: new Date().toISOString(),
        modelType: "resunet",
        source: "editor",
      },
    },
  };

  // Get the correct ID to use for saving
  const saveId = actualId || imageId;

  // Try multiple endpoint formats in sequence
  const endpointsToTry = [`/images/${saveId}/segmentation`];

  let savedSuccessfully = false;

  // Try each endpoint in sequence until one succeeds
  for (const endpoint of endpointsToTry) {
    try {
      logger.debug(`Attempting to save to endpoint: ${endpoint}`);
      await apiClient.put(endpoint, dataToSave);
      logger.info(`Successfully saved to ${endpoint}`);
      savedSuccessfully = true;
      break; // Exit the loop on success
    } catch (_error) {
      // Only log if it's not a cancellation _error
      if (
        _error instanceof Error &&
        _error.message !== "canceled" &&
        _error.name !== "AbortError"
      ) {
        logger.error(
          `Error saving to ${endpoint}: ${_error instanceof Error ? _error.message : String(_error)}`,
        );
      }
      // Continue to the next endpoint
    }
  }

  // If no endpoint succeeded, throw the last error
  if (!savedSuccessfully) {
    // Create a synthetic axios-like error for permission detection
    const error = new Error(
      `Failed to save segmentation data for imageId=${imageId}`,
    );
    // Permission errors are handled by the API client, just throw the error
    throw error;
  }
};

/**
 * Delete segmentation data
 */
export const deleteSegmentationData = async (
  projectId: string,
  imageId: string,
): Promise<void> => {
  logger.info(
    `Deleting segmentation data for imageId=${imageId} in projectId=${projectId}`,
  );

  // Try multiple endpoint formats in sequence
  const endpointsToTry = [`/images/${imageId}/segmentation`];

  let deletedSuccessfully = false;

  // Try each endpoint in sequence until one succeeds
  for (const endpoint of endpointsToTry) {
    try {
      logger.debug(`Attempting to delete from endpoint: ${endpoint}`);
      await apiClient.delete(endpoint);
      logger.info(`Successfully deleted from ${endpoint}`);
      deletedSuccessfully = true;
      break; // Exit the loop on success
    } catch (_error) {
      // Only log if it's not a cancellation _error
      if (
        _error instanceof Error &&
        _error.message !== "canceled" &&
        _error.name !== "AbortError"
      ) {
        logger.error(
          `Error deleting from ${endpoint}: ${_error instanceof Error ? _error.message : String(_error)}`,
        );
      }
      // Continue to the next endpoint
    }
  }

  // If no endpoint succeeded, throw the last error
  if (!deletedSuccessfully) {
    throw new Error(
      `Failed to delete segmentation data for imageId=${imageId}`,
    );
  }
};

/**
 * Fetch project data
 */
interface ProjectData {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  [key: string]: unknown;
}

export const fetchProjectData = async (
  projectId: string,
): Promise<ProjectData> => {
  logger.info(`Fetching project data for projectId=${projectId}`);

  try {
    const response = await projectsApi.get(projectId);
    return response.data as ProjectData;
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (
      _error instanceof Error &&
      _error.message !== "canceled" &&
      _error.name !== "AbortError"
    ) {
      logger.error(
        `Error fetching project data: ${_error instanceof Error ? _error.message : String(_error)}`,
      );
    }
    throw _error;
  }
};

/**
 * Fetch project images
 */
export const fetchProjectImages = async (
  projectId: string,
): Promise<ImageData[]> => {
  logger.info(`Fetching images for projectId=${projectId}`);

  try {
    const response = await imagesApi.list(projectId, {});
    return response.data.data as ImageData[];
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (
      _error instanceof Error &&
      _error.message !== "canceled" &&
      _error.name !== "AbortError"
    ) {
      logger.error(
        `Error fetching project images: ${_error instanceof Error ? _error.message : String(_error)}`,
      );
    }
    throw _error;
  }
};

/**
 * Fetch image segmentation status
 */
export const fetchImageSegmentationStatus = async (
  imageId: string,
): Promise<string> => {
  logger.info(`Fetching segmentation status for imageId=${imageId}`);

  try {
    const response = await apiClient.get(
      `/images/${imageId}/segmentation/status`,
    );
    return (response.data as any)?.status;
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (
      _error instanceof Error &&
      _error.message !== "canceled" &&
      _error.name !== "AbortError"
    ) {
      logger.error(
        `Error fetching segmentation status: ${_error instanceof Error ? _error.message : String(_error)}`,
      );
    }
    return "unknown";
  }
};

/**
 * Trigger segmentation for an image
 */
interface SegmentationResponse {
  status: string;
  message?: string;
  task_id?: string;
  [key: string]: unknown;
}

export const triggerSegmentation = async (
  imageId: string,
  parameters?: Record<string, unknown>,
  projectId?: string,
): Promise<SegmentationResponse> => {
  logger.info(
    `Triggering segmentation for imageId=${imageId}, projectId=${projectId}`,
  );

  try {
    // Always use ResUNet model
    const segmentationParams = {
      ...parameters,
      model_type: "resunet",
    };

    // Use the same endpoint as resegment button in image card
    const response = await apiClient.post(
      `/segmentation/${imageId}/resegment`,
      {
        project_id: projectId,
        ...segmentationParams,
      },
    );

    return response.data as SegmentationResponse;
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (
      _error instanceof Error &&
      _error.message !== "canceled" &&
      _error.name !== "AbortError"
    ) {
      logger.error(
        `Error triggering segmentation: ${_error instanceof Error ? _error.message : String(_error)}`,
      );
    }
    throw _error;
  }
};

/**
 * Fetch segmentation queue status
 */
interface SegmentationQueueStatus {
  queueLength: number;
  runningTasks: string[];
  queuedTasks?: string[];
  processingImages?: Array<{ id: string; name: string; projectId?: string }>;
  [key: string]: unknown;
}

export const fetchSegmentationQueueStatus =
  async (): Promise<SegmentationQueueStatus> => {
    logger.info("Fetching segmentation queue status");

    try {
      const response = await apiClient.get("/segmentation/queue");
      return response.data as SegmentationQueueStatus;
    } catch (_error) {
      // Only log if it's not a cancellation _error
      if (
        _error instanceof Error &&
        _error.message !== "canceled" &&
        _error.name !== "AbortError"
      ) {
        logger.error(
          `Error fetching segmentation queue status: ${_error instanceof Error ? _error.message : String(_error)}`,
        );
      }
      throw _error;
    }
  };

/**
 * Get polygons for an image
 */
export const getPolygonsForImage = async (
  imageId: string,
  projectId: string,
): Promise<Polygon[]> => {
  try {
    logger.info(
      `Fetching polygons for image ${imageId} in project ${projectId}`,
    );

    // Fetch segmentation data from API
    const segmentationData = await fetchSegmentationData(
      imageId,
      undefined,
      projectId,
    );

    if (!segmentationData) {
      logger.warn(`No segmentation data found for image ${imageId}`);
      return [];
    }

    // Extract polygons from segmentation data
    let polygons: Polygon[] = [];

    if (segmentationData.result_data && segmentationData.result_data.polygons) {
      logger.info(
        `Found ${segmentationData.result_data.polygons.length} polygons in result_data for image ${imageId}`,
      );
      polygons = segmentationData.result_data.polygons;
    } else if (segmentationData.polygons) {
      logger.info(
        `Found ${segmentationData.polygons.length} polygons at root level for image ${imageId}`,
      );
      polygons = segmentationData.polygons;
    }

    // Validate polygons
    if (!polygons || !Array.isArray(polygons)) {
      logger.warn(`Invalid polygons data for image ${imageId}`);
      return [];
    }

    // Filter and process polygons
    const validPolygons = polygons.filter(
      (polygon) =>
        polygon && Array.isArray(polygon.points) && polygon.points.length >= 3,
    );

    logger.info(
      `Found ${validPolygons.length} valid polygons for image ${imageId}`,
    );

    return validPolygons;
  } catch (_error) {
    // Only log if it's not a cancellation _error
    if (
      _error instanceof Error &&
      _error.message !== "canceled" &&
      _error.name !== "AbortError"
    ) {
      logger.error(`Error fetching polygons for image ${imageId}:`, _error);
    }
    return [];
  }
};
