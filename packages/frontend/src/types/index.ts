// Re-export all types from shared package for SSOT
export type {
  // User types
  User,
  UserProfile,
  UserProfileUpdatePayload,
  
  // Project types  
  Project,
  ProjectWithPermissions,
  ProjectCreatePayload,
  ProjectUpdatePayload,
  ProjectShare,
  ProjectStats,
  ProjectImage,
  
  // Image types
  Image,
  ImageWithSegmentation,
  ImageStatus,
  
  // Segmentation types
  SegmentationTask,
  SegmentationResult,
  SegmentationResultData,
  SegmentationApiResponse,
  CanvasSegmentationData,
  
  // Auth types
  LoginCredentials,
  RegisterCredentials,
  LoginResponse,
  RefreshTokenResponse,
  PasswordResetRequest,
  PasswordResetConfirm,
  JWTPayload,
  JWK,
  JWKS,
  
  // Access Request types
  AccessRequest,
  AccessRequestPayload,
  
  // API Response types
  ApiResponse,
  PaginatedResponse,
  
  // Common types
  Timestamped,
  Identifiable,
  WithTimestamps,
  WithId,
  
  // Utility types
  Optional,
  RequiredFields,
  Nullable,
  Undefinable,
} from '@spheroseg/shared';

// Re-export geometry types from shared package
export type { Point, Polygon } from '@spheroseg/shared';

// Maintain backwards compatibility for Project type with permissions
export type { ProjectWithPermissions as Project } from '@spheroseg/shared';