/**
 * Centralized API path configuration
 * This file provides a single point of truth for all API endpoints
 */

// Core API endpoints (without /api prefix - added by API client)
export const API_PATHS = {
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    REFRESH: "/auth/refresh",
    ME: "/users/me",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
  },
  USERS: {
    ME: "/users/me",
    PROFILE: "/users/me/profile",
    SETTINGS: "/users/me/settings",
    STATS: "/user-stats/stats",
    BY_ID: (id: string) => `/users/${id}`,
  },
  PROJECTS: {
    LIST: "/projects",
    CREATE: "/projects",
    BY_ID: (id: string) => `/projects/${id}`,
    IMAGES: (id: string) => `/projects/${id}/images`,
    EXPORT: (id: string) => `/projects/${id}/export`,
    SHARE: (id: string) => `/projects/${id}/share`,
  },
  IMAGES: {
    UPLOAD: "/images/upload",
    BY_ID: (id: string) => `/images/${id}`,
    SEGMENT: (id: string) => `/images/${id}/segment`,
  },
  SEGMENTATION: {
    START: "/segmentation/start",
    STATUS: (id: string) => `/segmentation/${id}/status`,
    RESULT: (id: string) => `/segmentation/${id}/result`,
    BATCH: "/segmentation/batch",
  },
  ACCESS_REQUESTS: {
    CREATE: "/access-requests",
  },
  SYSTEM: {
    HEALTH: "/health",
    STATUS: "/status",
    METRICS: "/metrics",
  },
};

/**
 * Ensures API path always uses the correct format
 * Handles both direct API calls and proxied calls
 *
 * @param path - The API path to format
 * @returns The formatted API path
 */
export const formatApiPath = (path: string): string => {
  // If path already starts with /api, just return it
  if (path.startsWith("/api/")) {
    return path;
  }

  // Otherwise, ensure path starts with / and add /api prefix
  const normalizedPath = path.startsWith("/") ? path : `/${path}`;
  return `/api${normalizedPath}`;
};

/**
 * Utility function for building URL parameters
 * @param params - The parameters to include in the URL
 * @returns A string of URL parameters
 */
export const buildUrlParams = (
  params: Record<string, string | number | boolean | undefined>,
): string => {
  const validParams = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`,
    );

  return validParams.length ? `?${validParams.join("&")}` : "";
};

export default API_PATHS;
