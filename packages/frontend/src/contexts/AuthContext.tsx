import React, {
  create<PERSON>ontext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
} from "react";
// Import jwt-decode, which is now installed
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import apiClient from "@/services/api/client";
import axios from "axios"; // Keep for error checking only
import { Language } from "@/types/language"; // Import Language type from separate file
import logger from "@/utils/logging/unifiedLogger"; // Import centralized logger
import { safeAsync } from "@/utils/error/unifiedErrorHandler"; // Import unified error handling
import {
  getAccessToken,
  getRefreshToken,
  setTokens,
  removeTokens,
  isAccessTokenExpired,
  refreshAccessToken,
  saveCurrentRoute,
  getLastRoute,
  shouldPersistSession,
} from "@/services/authService"; // Import token services
import { API_PATHS } from "@/lib/apiPaths"; // Import centralized API paths
import userProfileService from "../services/userProfileService";
import { markPerformance, measurePerformance } from "@/utils/performance";

// Define simplified User type
interface User {
  id: string;
  email: string;
  // Add other relevant fields if returned by /users/me or needed globally
}

// Define API response types (adjust based on actual backend responses)
interface AuthResponse {
  accessToken: string;
  refreshToken?: string; // Made optional as some endpoints might not return it
  user: User;
  message?: string;
  tokenType?: string; // Made optional with default value "Bearer"
}

interface UserProfileResponse {
  user_id: string;
  email?: string; // email might be here or directly in User object
  // Add other profile fields
}

interface AuthContextType {
  // session: Session | null; // Removed
  user: User | null;
  token: string | null;
  // profile: unknown | null; // Removed profile from AuthContext
  loading: boolean;
  signIn: (email: string, password: string) => Promise<boolean>; // Return boolean success
  signUp: (email: string, password: string, name: string) => Promise<boolean>; // Add name parameter
  signOut: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Helper function to extract user ID from JWT token
const getUserIdFromToken = (token: string): string | null => {
  try {
    // JWT format: header.payload.signature
    const parts = token.split(".");
    if (parts.length !== 3) return null;

    // Decode the payload (base64)
    const payload = JSON.parse(atob(parts[1]));
    return payload.userId || payload.id || null;
  } catch (error) {
    logger.error("Error decoding JWT token:", error);
    return null;
  }
};

// Helper function to validate user data matches JWT token
const validateUserData = (user: User | null, token: string | null): boolean => {
  if (!user || !token) return true; // Nothing to validate

  const tokenUserId = getUserIdFromToken(token);
  if (!tokenUserId) return false; // Invalid token

  if (user.id !== tokenUserId) {
    logger.warn("User ID mismatch detected!", {
      storedUserId: user.id,
      tokenUserId: tokenUserId,
      userEmail: user.email,
    });
    return false;
  }

  return true;
};

// Helper functions to persist user data
const saveUserToStorage = (user: User | null) => {
  if (user) {
    // Save to localStorage
    localStorage.setItem("spheroseg_user", JSON.stringify(user));

    // Also save to cookie for better resilience
    try {
      const userJson = JSON.stringify(user);
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + 7); // 7 dní

      const domain =
        window.location.hostname === "localhost"
          ? "localhost"
          : window.location.hostname;
      const sameSiteValue =
        process.env.NODE_ENV === "production" ? "lax" : "lax";
      const secure = window.location.protocol === "https:" ? "; secure" : "";

      document.cookie =
        "spheroseg_user=" +
        encodeURIComponent(userJson) +
        "; path=/; expires=" +
        expirationDate.toUTCString() +
        "; domain=" +
        domain +
        "; samesite=" +
        sameSiteValue +
        secure;
    } catch (_error) {
      logger.error("Error setting user cookie:", _error);
    }
  } else {
    // Clear from both localStorage and cookie
    localStorage.removeItem("spheroseg_user");

    try {
      const domain =
        window.location.hostname === "localhost"
          ? "localhost"
          : window.location.hostname;
      const sameSiteValue =
        process.env.NODE_ENV === "production" ? "lax" : "lax";
      const secure = window.location.protocol === "https:" ? "; secure" : "";

      document.cookie =
        "spheroseg_user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; domain=" +
        domain +
        "; samesite=" +
        sameSiteValue +
        secure;
    } catch (_error) {
      logger.error("Error removing user cookie:", _error);
    }
  }
};

const loadUserFromStorage = (
  validateAgainstToken: boolean = true,
): User | null => {
  try {
    // Get the current access token for validation
    const currentToken = validateAgainstToken ? getAccessToken() : null;

    // First try to load from localStorage
    const userData = localStorage.getItem("spheroseg_user");
    if (userData) {
      const user = JSON.parse(userData);

      // Validate if requested
      if (
        validateAgainstToken &&
        currentToken &&
        !validateUserData(user, currentToken)
      ) {
        logger.warn(
          "Stored user data does not match JWT token, clearing stale data",
        );
        saveUserToStorage(null); // Clear invalid data
        return null;
      }

      return user;
    }

    // If not in localStorage, try cookie
    const cookieStr = document.cookie;
    const userCookieMatch = cookieStr.match(/spheroseg_user=([^;]+)/);
    if (userCookieMatch && userCookieMatch[1]) {
      try {
        const decodedUserJson = decodeURIComponent(userCookieMatch[1]);
        const user = JSON.parse(decodedUserJson);

        // Validate if requested
        if (
          validateAgainstToken &&
          currentToken &&
          !validateUserData(user, currentToken)
        ) {
          logger.warn(
            "Cookie user data does not match JWT token, clearing stale data",
          );
          saveUserToStorage(null); // Clear invalid data
          return null;
        }

        // Synchronizujeme do localStorage pro příští použití
        localStorage.setItem("spheroseg_user", decodedUserJson);

        return user;
      } catch (_e) {
        logger.error("Error parsing user data from cookie:", _e);
      }
    }
  } catch (_error) {
    logger.error("Error loading user data:", _error);
  }
  return null;
};

export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Vytvoříme persistentní hodnotu v sessionStorage, aby se neztratila při re-renderingu
  const getPersistedUserFromSession = (): User | null => {
    try {
      const sessionUserData = sessionStorage.getItem(
        "spheroseg_persisted_user",
      );
      if (sessionUserData) {
        return JSON.parse(sessionUserData);
      }
    } catch (_e) {
      logger.error("Error reading persisted user from session storage:", _e);
    }
    return null;
  };

  // CRITICAL: Ensure that user is never null if they were previously logged in
  const ensureUserPersistence = (loadedUser: User | null): User | null => {
    const persistedUser = getPersistedUserFromSession();

    if (loadedUser) {
      // Pokud máme uživatele, uložíme ho do session pro případ ztráty
      try {
        sessionStorage.setItem(
          "spheroseg_persisted_user",
          JSON.stringify(loadedUser),
        );
      } catch (_e) {
        logger.error("Error saving user to session storage:", _e);
      }
      return loadedUser;
    } else if (persistedUser) {
      // Pokud nemáme uživatele, ale máme ho v session, použijeme ho jako fallback
      logger.debug("Using persisted user from session as fallback");
      return persistedUser;
    }

    // Jinak vrátíme null
    return null;
  };

  // Initialize user from localStorage with persistence protection and validation
  const initialUser = ensureUserPersistence(loadUserFromStorage(true)); // Validate against token
  logger.debug(
    "Initial user from storage (with persistence and validation):",
    initialUser,
  );

  const [user, setUser] = useState<User | null>(initialUser);
  // Initialize token from storage with better error handling and expiration check
  const getInitialToken = (): string | null => {
    try {
      // First try to get from authService (checks localStorage and cookies)
      const accessToken = getAccessToken();
      if (accessToken) {
        logger.debug("Found access token from authService:", {
          tokenLength: accessToken.length,
        });

        // Check if token is expired
        if (isAccessTokenExpired()) {
          logger.warn("Found token but it is expired, will need to refresh");
          // Still return it so we can try to refresh it
          return accessToken;
        }

        return accessToken;
      }

      // Fallback to direct cookie check
      const cookieToken = document.cookie.match(/auth_token=([^;]+)/)?.[1];
      if (cookieToken) {
        logger.debug("Found access token from cookie:", {
          tokenLength: cookieToken.length,
        });
        return cookieToken;
      }

      logger.debug("No access token found during initialization");
      return null;
    } catch (_error) {
      logger.error("Error getting initial token:", _error);
      return null;
    }
  };

  const [token, setToken] = useState<string | null>(getInitialToken());
  const [loading, setLoading] = useState(true);

  // Log auth state changes for debugging
  useEffect(() => {
    logger.debug("Auth state changed:", {
      user: !!user,
      token: !!token,
      loading,
    });
  }, [user, token, loading]);

  // Make navigate optional for testing purposes
  // Získáváme funkci pro navigaci a aktuální lokaci
  // Pozor: useNavigate musí být volán jen v komponentě, nemůže být v callbacku
  const navigate = useNavigate(); // Call hook unconditionally
  const location = useLocation(); // Get current location and route

  // Pro detekci, zda jsme právě načetli stránku
  const isPageLoadRef = useRef(true);

  // Custom setter for user that also updates localStorage and sessionStorage
  const setUserWithStorage = (newUser: User | null) => {
    setUser(newUser);
    saveUserToStorage(newUser);

    // Update persisted user in sessionStorage
    if (newUser) {
      try {
        sessionStorage.setItem(
          "spheroseg_persisted_user",
          JSON.stringify(newUser),
        );
      } catch (_e) {
        logger.error("Error saving user to session storage:", _e);
      }
    } else {
      // Only clear session storage if we're explicitly logging out
      // Pokud jsme došli sem voláním signOut funkce, vymažeme i z session
      const isLogout = new Error().stack?.includes("signOut");
      if (isLogout) {
        logger.debug("Clearing persisted user due to explicit logout");
        sessionStorage.removeItem("spheroseg_persisted_user");
      } else {
        logger.debug("Not clearing persisted user (not an explicit logout)");
      }
    }
  };

  // Save the current route whenever location changes (for restoration after refresh)
  useEffect(() => {
    if (token && location.pathname) {
      saveCurrentRoute(location.pathname);
    }
  }, [location.pathname, token]);

  // CRITICAL: Prevent redirect on the FIRST page load
  useEffect(() => {
    // This will be executed only once when the page loads
    if (isPageLoadRef.current) {
      logger.info("Setting up page reload blocker...");
      // Set a flag that we are in the page loading process
      // Block all navigation operations during loading
      window.sessionStorage.setItem("spheroseg_page_loading", "true");

      // After 2 seconds, remove the block (reduced from 5 seconds for faster loading)
      setTimeout(() => {
        isPageLoadRef.current = false;
        window.sessionStorage.removeItem("spheroseg_page_loading");
        logger.info("Page reload blocker disabled");
      }, 2000);
    }
  }, []);

  // Check token validity and fetch user data on initial load or token change
  useEffect(() => {
    const initializeAuth = async () => {
      // If we already have a user from localStorage, don't set loading to true
      // This prevents the UI from showing loading state when user data is already available
      if (!user) {
        setLoading(true);
      }

      // Skip auth initialization only on sign-in/sign-up pages
      if (
        location.pathname === "/sign-in" ||
        location.pathname === "/sign-up"
      ) {
        logger.debug("Skipping auth initialization on auth page");
        setLoading(false);
        return;
      }

      // Try to get tokens (will check both localStorage and cookies)
      const currentToken = getAccessToken();
      const currentRefreshToken = getRefreshToken();

      logger.info("[AuthContext] Auth initialization check:", {
        hasToken: !!currentToken,
        hasRefreshToken: !!currentRefreshToken,
        tokenLength: currentToken?.length || 0,
        pathname: location.pathname,
      });

      if (currentToken) {
        logger.info("Found authentication tokens, verifying...");

        // IMPORTANT: Set the token state immediately to ensure it's available for API calls
        setToken(currentToken);

        // Check if access token is expired and refresh token is available
        if (isAccessTokenExpired()) {
          if (currentRefreshToken) {
            logger.info("Access token expired, attempting refresh...");
            const refreshSuccess = await refreshAccessToken();

            if (!refreshSuccess) {
              logger.warn("Token refresh failed during initialization");
              removeTokens();
              setUserWithStorage(null);
              setToken(null);
              setLoading(false);
              return;
            }

            // Update token state with newly refreshed token
            const newToken = getAccessToken();
            setToken(newToken);
          } else {
            logger.info("Access token expired and no refresh token available");
            removeTokens();
            setUserWithStorage(null);
            setToken(null);
            setLoading(false);
            return;
          }
        }

        try {
          // Enhanced timeout handling for auth verification
          const timeoutPromise = new Promise<null>((resolve) => {
            setTimeout(() => {
              logger.warn(
                "Auth API verification timed out after 8 seconds, continuing with stored token and user data",
              );
              resolve(null);
            }, 3000); // 3 seconds timeout for auth verification
          });

          // Try to load user from storage first (with validation)
          const storedUser = loadUserFromStorage(true);
          if (storedUser) {
            // Pre-set user from storage to avoid UI flashing if verification is slow
            setUserWithStorage(storedUser);
            logger.info("Pre-loaded user from storage:", {
              userId: storedUser.id,
              email: storedUser.email,
            });
          }

          // Verify token by fetching user data using the centralized HTTP client
          const controller = new AbortController();
          const fetchUserPromise = (async () => {
            let retries = 1; // 1 retry for auth verification
            const baseRetryDelay = 500; // Base delay between retries

            while (retries > 0) {
              try {
                return await apiClient.get<User>(API_PATHS.USERS.ME, {
                  signal: controller.signal,
                  timeout: 3000, // 3 second timeout per request
                  credentials: "include", // Important for cookie transmission
                });
              } catch (_error) {
                // Don't retry if token is invalid (401 Unauthorized)
                if (
                  axios.isAxiosError(_error) &&
                  (_error as any).response?.status === 401
                ) {
                  logger.warn(
                    "Token is invalid (401), not retrying auth verification",
                  );
                  throw _error;
                }

                // Don't retry if user is forbidden (403)
                if (
                  axios.isAxiosError(_error) &&
                  (_error as any).response?.status === 403
                ) {
                  logger.warn(
                    "User is forbidden (403), not retrying auth verification",
                  );
                  throw _error;
                }

                retries--;
                if (retries === 0) {
                  logger.warn("User data fetch failed after all retries", {
                    error:
                      _error instanceof Error ? _error.message : String(_error),
                    isTimeout:
                      _error instanceof Error &&
                      (_error.message.includes("timeout") ||
                        _error.message.includes("ECONNABORTED")),
                    isNetworkError:
                      axios.isAxiosError(_error) && !_error.response,
                  });
                  throw _error;
                }

                // Exponential backoff with jitter
                const jitter = Math.random() * 500; // Add up to 500ms random jitter
                const retryDelay = baseRetryDelay * (4 - retries) + jitter; // Exponential increase

                logger.warn(
                  "Auth verification failed, will retry in " +
                    Math.round(retryDelay) +
                    "ms. Retries left: " +
                    retries,
                  {
                    error:
                      _error instanceof Error ? _error.message : String(_error),
                    retryDelay: Math.round(retryDelay),
                  },
                );

                await new Promise((resolve) => setTimeout(resolve, retryDelay));
              }
            }
          })();

          // Použijeme Promise.race, abychom pokračovali, pokud nastane timeout
          const response = await Promise.race([
            fetchUserPromise,
            timeoutPromise,
          ]);

          if (response === null) {
            // Timeout nastal, ale pokračujeme s uloženým tokenem a uživatelem, pokud je k dispozici
            logger.warn(
              "API verification timed out - continuing with cached user data",
            );

            // Use stored user data if available, otherwise use existing user state
            if (storedUser) {
              logger.info("Using cached user data from storage");
              setUserWithStorage(storedUser);
              setToken(currentToken);
            } else if (user) {
              // If we already have a user in state, keep it
              logger.info("Keeping existing user data in state");
              setToken(currentToken);
            } else {
              logger.warn(
                "No cached user data available - clearing auth state",
              );
              removeTokens();
              setUserWithStorage(null);
              setToken(null);

              // Show a more informative toast about auth timeout
              toast.error("Authentication server not responding", {
                id: "auth-server-timeout",
                duration: 6000,
                description:
                  "Your session may be temporarily unavailable. The app will retry automatically.",
              });

              // Redirect to login if there's a navigation function
              if (navigate) {
                // Save the current location before redirecting
                if (location.pathname && location.pathname !== "/sign-in") {
                  saveCurrentRoute(location.pathname);
                }
                navigate("/sign-in");
              }
            }
          } else {
            // API odpovědělo úspěšně
            logger.info("Token verification successful");
            // Extract user from response - API returns {success: true, user: {...}}
            const userData = (response.data as any).user || response.data;
            setUserWithStorage(userData);
            setToken(currentToken); // Ensure token state matches localStorage/cookie

            // After successful verification, check if we should restore the previous route
            const savedRoute = getLastRoute();
            if (savedRoute && navigate && location.pathname === "/sign-in") {
              logger.info("Restoring last route: " + savedRoute);
              setTimeout(() => navigate(savedRoute), 100);
            }
          }
        } catch (_error) {
          logger.error("Token verification failed:", { _error: _error });

          // Try to use stored user data as fallback (with validation)
          const storedUser = loadUserFromStorage(true);
          if (storedUser && !isAccessTokenExpired()) {
            logger.info("Using cached user data despite verification failure");
            setUserWithStorage(storedUser);
            setToken(currentToken);
          } else if (user && !isAccessTokenExpired()) {
            // If we already have a user in state and token is not expired, keep it
            logger.info(
              "Keeping existing user data despite verification failure",
            );
            setToken(currentToken);
          } else {
            removeTokens(); // Remove invalid tokens
            setUserWithStorage(null);
            setToken(null);
          }
        }
      } else {
        logger.info("No access token found in localStorage or cookies");
        setUserWithStorage(null); // Ensure user is null if no token
        setToken(null); // Ensure token is null if no token
      }

      setLoading(false);
    };

    initializeAuth();

    // Add event listener for token expiration events
    const handleTokenExpired = () => {
      logger.warn("Token expired event received");

      // Pokud je uživatel stále přihlášen, pokusíme se o refresh tokenu
      if (user) {
        // Zkusíme znovu získat platný token
        // a pokud se podaří, nebudeme odhlašovat
        logger.info("Trying to refresh token after expiration");
        refreshAccessToken()
          .then((refreshSuccess) => {
            if (refreshSuccess) {
              logger.info("Successfully refreshed token after expiration");
              return; // Pokud se úspěšně obnovil token, nebude se pokračovat v odhlášení
            }

            // Pokud se refresh nezdařil, pokračuj v odhlášení
            logger.warn("Token refresh failed after expiration");
            completeExpiration();
          })
          .catch(() => {
            completeExpiration();
          });
      } else {
        completeExpiration();
      }
    };

    // Funkce pro dokončení procesu expirace, když refresh selže
    const completeExpiration = () => {
      setUserWithStorage(null);
      setToken(null);

      // Show a message to the user
      toast.error("Your session has expired. Please sign in again.", {
        id: "session-expired",
        duration: 5000,
      });

      // Redirect to login page only if we're not already on login page
      if (location.pathname !== "/sign-in") {
        // Save current location before redirecting
        if (location.pathname) {
          saveCurrentRoute(location.pathname);
        }
        navigate("/sign-in");
      }
    };

    window.addEventListener("auth:expired", handleTokenExpired);

    // Clean up event listener
    return () => {
      window.removeEventListener("auth:expired", handleTokenExpired);
    };
  }, []); // Run only once on mount

  const handleAuthSuccess = useCallback(
    (data: AuthResponse, rememberMe: boolean = false) => {
      // Clear any stale user data first
      saveUserToStorage(null);

      // Store both access and refresh tokens with remember me preference
      setTokens(data.accessToken, data.refreshToken || "", rememberMe);
      setToken(data.accessToken);

      // Validate that the user data matches the token
      if (!validateUserData(data.user, data.accessToken)) {
        logger.error("User data from API does not match JWT token!");
        toast.error("Authentication error: Invalid user data");
        removeTokens();
        setUserWithStorage(null);
        setToken(null);
        return;
      }

      // Store the validated user data
      setUserWithStorage(data.user);
      toast.success(data.message || "Success!");

      // Only migrate if user data is available
      if (data.user) {
        // Migrate localStorage data to database for authenticated user
        (async () => {
          try {
            logger.info(
              "Migrating localStorage data to database for user " +
                (data.user?.id || "unknown"),
            );
            await userProfileService.migrateLocalStorageToDatabase();

            // Also initialize user settings from database
            await userProfileService.initializeUserSettings();

            logger.info(
              "Data migration completed for user " +
                (data.user?.id || "unknown"),
            );
          } catch (_error) {
            logger.error("Error during data migration:", {
              _error: _error,
              userId: data.user?.id || "unknown",
            });

            // Fallback to legacy language update
            const currentLanguage = localStorage.getItem(
              "language",
            ) as Language | null;
            if (currentLanguage) {
              try {
                await axios.put(
                  API_PATHS.USERS.ME,
                  { preferred_language: currentLanguage },
                  {
                    timeout: 2000,
                    headers: {
                      Authorization: "Bearer " + data.accessToken,
                      "Content-Type": "application/json",
                    },
                  },
                );
                logger.info(
                  "Language preference updated via fallback for user " +
                    (data.user?.id || "unknown"),
                );
              } catch (_fallbackErr) {
                logger.error(
                  "Fallback language update also failed:",
                  _fallbackErr,
                );
              }
            }
          }
        })();
      }
    },
    [setUserWithStorage],
  );

  const signIn = useCallback(
    async (
      email: string,
      password: string,
      rememberMe: boolean = false,
    ): Promise<boolean> => {
      setLoading(true);

      try {
        // Log the attempt and mark performance
        logger.info(
          "Attempting to sign in with email: " +
            email +
            ", rememberMe: " +
            rememberMe,
        );
        markPerformance("auth-signin-start");

        // Set up timeout for the entire sign-in process
        const loginTimeoutPromise = new Promise<boolean>((_, reject) => {
          setTimeout(() => {
            logger.debug(
              "[authContext] Sign-in operation timed out after 12 seconds",
            );
            reject(
              new Error(
                "Authentication is taking longer than expected. Please check your connection and try again.",
              ),
            );
          }, 12000); // 12 second timeout for the entire process (slightly longer than request timeout)
        });

        // Create a promise for the login process with direct axios call
        const loginProcess = (async () => {
          // Set up abort controller for the login request
          const controller = new AbortController();
          const timeoutId = setTimeout(() => {
            logger.debug(
              "[authContext] Login request timed out after 8 seconds",
            );
            controller.abort();
          }, 8000); // 8 second timeout for the request

          try {
            // Use apiClient for proper error handling and interceptors
            const response = await apiClient.post(
              API_PATHS.AUTH.LOGIN,
              { email, password, remember_me: rememberMe },
              {
                cancelToken: controller,
                timeout: 8000, // 8 second request timeout (matches auth API timeout)
                skipAuth: true, // Skip auth header since we're logging in
                showErrorToast: false, // We'll handle errors ourselves
              },
            );

            markPerformance("auth-signin-success");
            logger.info("Login successful");
            const loginDuration = measurePerformance(
              "signin-total",
              "auth-signin-start",
              "auth-signin-success",
            );
            logger.info(
              "Authentication completed in " +
                (typeof loginDuration === "number"
                  ? loginDuration.toFixed(0)
                  : "unknown") +
                "ms",
            );

            // Debug: log the response structure
            logger.debug("[authContext] Login response structure:", {
              hasData: "data" in response,
              dataKeys: response.data ? Object.keys(response.data) : [],
              fullResponse: response,
            });

            // ApiClient wraps response, so we need to access the actual data
            // The backend returns { message, accessToken, user } directly
            handleAuthSuccess(response.data as AuthResponse, rememberMe);

            if (navigate) {
              navigate("/dashboard");
            }
            return true;
          } catch (_error: any) {
            if (_error?.message?.includes("cancelled")) {
              logger.debug(
                "[authContext] Login request aborted due to timeout",
              );
              throw new Error(
                "Request timed out. The server may be slow or unreachable.",
              );
            }

            if (_error?.status === 401) {
              logger.warn("Invalid credentials provided");
              // Customize error message for better UX
              const errorMessage =
                _error?.data?.error ||
                _error?.data?.message ||
                "Invalid email or password";
              throw new Error(errorMessage);
            }

            logger.error("Login attempt failed", { error: _error });
            throw _error;
          } finally {
            clearTimeout(timeoutId);
          }
        })();

        // Race the login process against the timeout
        const success = await Promise.race([loginProcess, loginTimeoutPromise]);
        setLoading(false);
        return success;
      } catch (_error) {
        logger.error("Error signing in:", { _error: _error });

        // Special handling for auth errors
        if (axios.isAxiosError(_error)) {
          const status = (_error as any).response?.status;
          const errorMessage =
            (_error as any).response?.data?.message || _error.message;

          if (status === 401) {
            // Invalid credentials (wrong password)
            toast.error("Invalid password", {
              duration: 5000,
              description: "Please check your password and try again.",
            });
          } else if (status === 404) {
            // Email not found
            toast.error("Invalid email/username", {
              duration: 5000,
              description:
                errorMessage ||
                "This email address is not registered. Please check your email or sign up for a new account.",
            });
          } else if (status === 400) {
            // Bad request - usually invalid email format or missing credentials
            toast.error("Invalid email/username", {
              duration: 5000,
              description:
                errorMessage ||
                "Please check your email address and try again.",
            });
          } else if (status === 502) {
            // Bad Gateway - server is down or unreachable
            toast.error("Server unavailable", {
              duration: 7000,
              description:
                "The authentication server is temporarily unavailable. Please try again in a few moments.",
            });
          } else if (status === 503) {
            // Service Unavailable
            toast.error("Service maintenance", {
              duration: 7000,
              description:
                "The authentication service is undergoing maintenance. Please try again later.",
            });
          } else {
            // Other errors
            toast.error("Login failed", {
              duration: 5000,
              description:
                errorMessage ||
                "An error occurred during login. Please try again.",
            });
          }
        } else {
          // Non-axios errors (including timeouts)
          const isTimeout =
            _error.message?.includes("timed out") ||
            _error.message?.includes("timeout") ||
            _error.message?.includes("longer than expected") ||
            _error.name === "AbortError";

          if (isTimeout) {
            toast.error("Authentication timeout", {
              duration: 7000,
              description:
                "The authentication server is taking too long to respond. Please check your connection and try again.",
            });
          } else {
            toast.error("Connection error", {
              duration: 5000,
              description:
                _error.message ||
                "Unable to connect to the authentication server. Please try again.",
            });
          }
        }

        setUserWithStorage(null);
        setToken(null);
        localStorage.removeItem("authToken");
        setLoading(false);
        return false;
      }
    },
    [navigate, handleAuthSuccess],
  );

  const signUp = useCallback(
    async (email: string, password: string, name: string): Promise<boolean> => {
      setLoading(true);

      // Use safeAsync for better error handling
      const result = await safeAsync(
        async () => {
          // Uložíme aktuální nastavení jazyka před registrací
          const currentLanguage = localStorage.getItem("language");

          const userData = {
            email,
            password,
            name,
            language: currentLanguage || "en", // Přidáme jazyk do registračních údajů
          };

          // Log the attempt
          logger.info("Attempting to sign up with email: " + email);

          // Ensure user data contains the required fields
          if (!email || !password) {
            throw new Error("Email and password are required");
          }

          try {
            // Use the centralized HTTP client with retry for actual API calls
            const response = await (async () => {
              let retries = 2; // 2 retries
              const retryDelay = 1000; // 1 second delay between retries

              while (retries >= 0) {
                try {
                  // Use apiClient for consistent handling
                  return await apiClient.post(
                    API_PATHS.AUTH.REGISTER,
                    userData,
                    {
                      signal: new AbortController().signal,
                      timeout: 3000,
                    },
                  );
                } catch (_error) {
                  // Don't retry if user already exists (409 Conflict)
                  if (axios.isAxiosError(_error) && _error.status === 409) {
                    logger.warn("User already exists, not retrying", { email });
                    throw _error;
                  }

                  retries--;
                  if (retries < 0) {
                    logger.warn(
                      "Auth signup attempt failed after all retries",
                      { error: _error },
                    );
                    throw _error;
                  }

                  logger.warn(
                    "Auth signup attempt failed, will retry. Retries left: " +
                      retries,
                    { error: _error },
                  );
                  await new Promise((resolve) =>
                    setTimeout(resolve, retryDelay),
                  );
                }
              }
            })();

            logger.info("Signup successful");
            // Don't show toast here - let the signup component handle success messages
            return true;
          } catch (_error) {
            // Propagate all errors without mock workarounds

            // Otherwise, propagate the _error
            throw _error;
          }
        },
        {
          // Don't use custom error handler - let errors bubble up to SignUp component
          showToast: false, // Let SignUp component handle toasts
          // rethrow: true, // Removed, as it's not a valid property for safeAsync
        },
      );

      setLoading(false);
      return (result as any) || false;
    },
    [navigate],
  );

  const signOut = useCallback(async () => {
    logger.info("Signing out...");
    const refreshToken = getRefreshToken();

    // Clear tokens and user state
    removeTokens();
    setUserWithStorage(null);
    setToken(null);

    // Clear ALL user-related localStorage data
    localStorage.removeItem("userProfile");
    localStorage.removeItem("userAvatar");
    localStorage.removeItem("userAvatarUrl");
    sessionStorage.removeItem("spheroseg_persisted_user");

    // Clear context initialization markers to allow fresh initialization on next login
    sessionStorage.removeItem("spheroseg_language_last_user");
    sessionStorage.removeItem("spheroseg_theme_last_user");
    sessionStorage.removeItem("spheroseg_profile_last_user");
    sessionStorage.removeItem("spheroseg_last_autherror");

    // Clear user-specific caches to prevent data leakage between users
    try {
      const { deleteByTag } = await import("@/services/unifiedCacheService");
      await deleteByTag("user-data");
      await deleteByTag("user-statistics");
      await deleteByTag("dashboard-data");

      // Clear legacy cache keys that might not be user-specific
      const keysToRemove = [
        "spheroseg_recent_activities",
        "spheroseg_recent_activities_timestamp",
      ];

      // Also clear any user-specific activity cache keys
      if (user?.id) {
        keysToRemove.push(
          "spheroseg_recent_activities_" + user.id,
          "spheroseg_recent_activities_timestamp_" + user.id,
        );
      }

      keysToRemove.forEach((key) => {
        try {
          localStorage.removeItem(key);
        } catch (_e) {
          logger.warn("Failed to remove cache key: " + key, _e);
        }
      });

      logger.info("Cleared user-specific caches");
    } catch (_error) {
      logger.error("Failed to clear user caches:", _error);
    }

    // Call the backend logout endpoint to invalidate the refresh token
    if (refreshToken) {
      try {
        // Use apiClient for consistent API calls
        await apiClient.post(
          API_PATHS.AUTH.LOGOUT,
          { refreshToken },
          {
            timeout: 2000,
            skipAuth: true, // Skip auth since we're logging out
          },
        );
        logger.info("Backend logout successful");
      } catch (_error) {
        // Non-critical _error, just log it
        logger.warn("Backend logout failed, but local session was cleared", {
          _error: _error,
        });
      }
    }

    if (navigate) {
      navigate("/");
    }
    toast.success("Signed out successfully");
  }, [navigate]);

  // Handle window events for session persistence
  useEffect(() => {
    const handleWindowFocus = () => {
      // DISABLED: This was causing users to be logged out when opening file dialogs
      // File dialogs cause the window to lose and regain focus, which shouldn't log users out
      // if (!shouldPersistSession() && user) {
      //   logger.info('Session should not persist and window gained focus, logging out');
      //   signOut();
      // }
    };

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      // IMPORTANT: We need to distinguish between page refresh and actual window/tab close
      // Unfortunately, there's no reliable way to detect this in modern browsers
      // So we'll use a different approach:

      // Don't clear tokens on page refresh - only clear them when:
      // 1. User explicitly logs out
      // 2. Tokens expire
      // 3. Server returns 401 unauthorized

      // For now, we'll disable automatic token clearing on beforeunload
      // This prevents the issue where tokens are cleared on page refresh

      // OLD LOGIC (disabled to fix refresh issue):
      // if (!shouldPersistSession()) {
      //   logger.info('Remember me not checked, clearing session on window close');
      //   removeTokens();
      //   sessionStorage.removeItem('spheroseg_persisted_user');
      // }

      // NEW LOGIC: Mark that we're unloading, but don't clear tokens
      if (!shouldPersistSession()) {
        // Just log that we're in non-persistent mode, but don't clear tokens
        logger.debug("Page unloading in non-persistent session mode");
        // We'll rely on token expiration and server validation instead
      }
    };

    const handleVisibilityChange = () => {
      // When tab becomes visible again
      if (document.visibilityState === "visible" && user) {
        // Only check token validity if we're in non-persistent mode
        if (!shouldPersistSession()) {
          const token = getAccessToken();
          if (!token) {
            logger.info("No token found when tab became visible, logging out");
            setUserWithStorage(null);
            setToken(null);
          } else if (isAccessTokenExpired()) {
            // Token expired while tab was hidden
            logger.info(
              "Token expired while tab was hidden, attempting refresh",
            );
            refreshAccessToken().then((success) => {
              if (!success) {
                logger.info("Token refresh failed, logging out");
                setUserWithStorage(null);
                setToken(null);
              }
            });
          }
        }
      }
    };

    // Add event listeners
    // Removed focus event listener - it was causing logout when opening file dialogs
    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Cleanup
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [user, signOut]);

  // The context value that will be supplied to consuming components
  const value = {
    user,
    token,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export const useAuth = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    // Instead of throwing an error, provide a fallback context for development
    // This helps prevent crashes when components are tested in isolation
    if (process.env.NODE_ENV !== "production") {
      logger.warn(
        "useAuth was called outside of AuthProvider. Using fallback context.",
      );

      // Return a fallback context with dummy values
      return {
        user: { id: "fallback-user-id", email: "<EMAIL>" },
        token: "fallback-token",
        loading: false,
        signIn: async () => false,
        signUp: async () => false,
        signOut: () => {},
      };
    }

    // In production, still throw the error to help identify issues
    throw new Error("useAuth must be used within an AuthProvider");
  }

  return context;
};
