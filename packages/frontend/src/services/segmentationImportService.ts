import { Point, Polygon, SegmentationResultData } from '@spheroseg/shared';
import { validatePolygons } from '../lib/segmentation/polygonValidator';
import { v4 as uuidv4 } from 'uuid';

// ============================================================================
// TYPES FOR IMPORT FORMATS
// ============================================================================

export interface ImportResult {
  success: boolean;
  polygons: Polygon[];
  errors: string[];
  warnings: string[];
}

export interface ImportOptions {
  mergeWithExisting?: boolean;
  replaceExisting?: boolean;
  validatePolygons?: boolean;
  imageWidth: number;
  imageHeight: number;
}

// COCO format types
interface COCOAnnotation {
  id: number;
  image_id: number;
  category_id: number;
  segmentation: number[][] | { counts: number[]; size: [number, number] }; // polygon or RLE
  area?: number;
  bbox?: [number, number, number, number];
  iscrowd?: number;
}

interface COCOCategory {
  id: number;
  name: string;
  supercategory?: string;
}

interface COCOImage {
  id: number;
  width: number;
  height: number;
  file_name?: string;
}

interface COCOFormat {
  annotations: COCOAnnotation[];
  categories?: COCOCategory[];
  images?: COCOImage[];
}

// CSV format types
interface CSVRow {
  polygon_id?: string;
  x: number;
  y: number;
  type?: 'external' | 'internal';
}

// ============================================================================
// MAIN IMPORT SERVICE
// ============================================================================

export class SegmentationImportService {
  private static instance: SegmentationImportService;

  public static getInstance(): SegmentationImportService {
    if (!SegmentationImportService.instance) {
      SegmentationImportService.instance = new SegmentationImportService();
    }
    return SegmentationImportService.instance;
  }

  /**
   * Import polygons from a file based on its format
   */
  async importFromFile(file: File, options: ImportOptions): Promise<ImportResult> {
    try {
      const extension = file.name.toLowerCase().split('.').pop();
      
      switch (extension) {
        case 'json':
          return await this.importFromJSON(file, options);
        case 'csv':
          return await this.importFromCSV(file, options);
        case 'png':
        case 'jpg':
        case 'jpeg':
          return await this.importFromBinaryMask(file, options);
        default:
          // Try to detect format based on content
          return await this.autoDetectAndImport(file, options);
      }
    } catch (error) {
      return {
        success: false,
        polygons: [],
        errors: [`Failed to import file: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }

  /**
   * Auto-detect file format and import
   */
  private async autoDetectAndImport(file: File, options: ImportOptions): Promise<ImportResult> {
    const text = await this.readFileAsText(file);
    
    try {
      // Try JSON first
      const jsonData = JSON.parse(text);
      return await this.importFromJSONData(jsonData, options);
    } catch {
      // Try CSV format
      return await this.importFromCSVText(text, options);
    }
  }

  // ============================================================================
  // JSON FORMAT IMPORT
  // ============================================================================

  private async importFromJSON(file: File, options: ImportOptions): Promise<ImportResult> {
    try {
      const text = await this.readFileAsText(file);
      const jsonData = JSON.parse(text);
      return await this.importFromJSONData(jsonData, options);
    } catch (error) {
      return {
        success: false,
        polygons: [],
        errors: [`Invalid JSON file: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }

  private async importFromJSONData(data: any, options: ImportOptions): Promise<ImportResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let polygons: Polygon[] = [];

    // Check if it's COCO format
    if (data.annotations && Array.isArray(data.annotations)) {
      return await this.importFromCOCOData(data as COCOFormat, options);
    }

    // Check if it's standard polygon format
    if (data.polygons && Array.isArray(data.polygons)) {
      polygons = this.parseStandardPolygonFormat(data.polygons, errors, warnings);
    } else if (Array.isArray(data)) {
      // Direct array of polygons
      polygons = this.parseStandardPolygonFormat(data, errors, warnings);
    } else if (data.contours && Array.isArray(data.contours)) {
      // OpenCV contour format
      polygons = this.parseContoursFormat(data.contours, data.hierarchy, errors, warnings);
    } else {
      errors.push('Unrecognized JSON format. Expected format: { "polygons": [...] } or COCO format');
    }

    // Validate polygons if requested
    if (options.validatePolygons && polygons.length > 0) {
      const validationResult = validatePolygons(polygons);
      if (!validationResult.isValid) {
        errors.push(...validationResult.errors);
      }
    }

    return {
      success: errors.length === 0,
      polygons,
      errors,
      warnings
    };
  }

  private parseStandardPolygonFormat(polygonData: any[], errors: string[], warnings: string[]): Polygon[] {
    const polygons: Polygon[] = [];

    polygonData.forEach((poly, index) => {
      try {
        if (!poly.points || !Array.isArray(poly.points)) {
          errors.push(`Polygon ${index + 1}: Missing or invalid points array`);
          return;
        }

        const points: Point[] = poly.points.map((point: any, pointIndex: number) => {
          if (Array.isArray(point) && point.length >= 2) {
            // Point as [x, y] array
            return { x: Number(point[0]), y: Number(point[1]) };
          } else if (point.x !== undefined && point.y !== undefined) {
            // Point as {x, y} object
            return { x: Number(point.x), y: Number(point.y) };
          } else {
            throw new Error(`Invalid point format at index ${pointIndex}`);
          }
        });

        const polygon: Polygon = {
          id: poly.id || uuidv4(),
          points,
          type: poly.type === 'internal' ? 'internal' : 'external',
          class: poly.class,
          color: poly.color,
          parentId: poly.parentId
        };

        polygons.push(polygon);
      } catch (error) {
        errors.push(`Polygon ${index + 1}: ${error instanceof Error ? error.message : String(error)}`);
      }
    });

    return polygons;
  }

  private parseContoursFormat(contours: number[][][], hierarchy: number[][] | undefined, errors: string[], warnings: string[]): Polygon[] {
    const polygons: Polygon[] = [];

    contours.forEach((contour, index) => {
      try {
        const points: Point[] = contour.map(([x, y]) => ({ x, y }));
        
        // Determine type based on hierarchy if available
        let type: 'external' | 'internal' = 'external';
        if (hierarchy && hierarchy[index]) {
          const [next, prev, firstChild, parent] = hierarchy[index];
          // If has parent, it's likely internal
          type = parent >= 0 ? 'internal' : 'external';
        }

        const polygon: Polygon = {
          id: uuidv4(),
          points,
          type
        };

        polygons.push(polygon);
      } catch (error) {
        errors.push(`Contour ${index + 1}: ${error instanceof Error ? error.message : String(error)}`);
      }
    });

    return polygons;
  }

  // ============================================================================
  // COCO FORMAT IMPORT
  // ============================================================================

  private async importFromCOCOData(data: COCOFormat, options: ImportOptions): Promise<ImportResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const polygons: Polygon[] = [];

    // Create category lookup
    const categoryLookup = new Map<number, string>();
    if (data.categories) {
      data.categories.forEach(cat => {
        categoryLookup.set(cat.id, cat.name);
      });
    }

    for (const annotation of data.annotations) {
      try {
        const categoryName = categoryLookup.get(annotation.category_id);
        
        if (Array.isArray(annotation.segmentation)) {
          // Polygon format
          annotation.segmentation.forEach((seg, segIndex) => {
            if (seg.length < 6) { // Need at least 3 points (6 coordinates)
              warnings.push(`Annotation ${annotation.id}, segment ${segIndex + 1}: Not enough points for polygon`);
              return;
            }

            const points: Point[] = [];
            for (let i = 0; i < seg.length; i += 2) {
              points.push({ x: seg[i], y: seg[i + 1] });
            }

            const polygon: Polygon = {
              id: `coco_${annotation.id}_${segIndex}`,
              points,
              type: 'external', // COCO doesn't specify internal/external
              class: categoryName
            };

            polygons.push(polygon);
          });
        } else if (annotation.segmentation && typeof annotation.segmentation === 'object' && 'counts' in annotation.segmentation) {
          // RLE format - convert to polygon
          try {
            const rlePolygons = await this.convertRLEToPolygons(annotation.segmentation, options.imageWidth, options.imageHeight);
            rlePolygons.forEach((poly, polyIndex) => {
              poly.id = `coco_rle_${annotation.id}_${polyIndex}`;
              poly.class = categoryName;
            });
            polygons.push(...rlePolygons);
          } catch (rleError) {
            errors.push(`Failed to convert RLE for annotation ${annotation.id}: ${rleError instanceof Error ? rleError.message : String(rleError)}`);
          }
        }
      } catch (error) {
        errors.push(`Failed to process annotation ${annotation.id}: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Validate polygons if requested
    if (options.validatePolygons && polygons.length > 0) {
      const validationResult = validatePolygons(polygons);
      if (!validationResult.isValid) {
        warnings.push(...validationResult.errors);
      }
    }

    return {
      success: errors.length === 0,
      polygons,
      errors,
      warnings
    };
  }

  // ============================================================================
  // CSV FORMAT IMPORT
  // ============================================================================

  private async importFromCSV(file: File, options: ImportOptions): Promise<ImportResult> {
    try {
      const text = await this.readFileAsText(file);
      return await this.importFromCSVText(text, options);
    } catch (error) {
      return {
        success: false,
        polygons: [],
        errors: [`Failed to read CSV file: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }

  private async importFromCSVText(csvText: string, options: ImportOptions): Promise<ImportResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const polygons: Polygon[] = [];

    try {
      const lines = csvText.split('\n').map(line => line.trim()).filter(line => line);
      if (lines.length === 0) {
        errors.push('Empty CSV file');
        return { success: false, polygons: [], errors, warnings };
      }

      // Parse header
      const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
      const requiredColumns = ['x', 'y'];
      const missingColumns = requiredColumns.filter(col => !headers.includes(col));
      
      if (missingColumns.length > 0) {
        errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
        return { success: false, polygons: [], errors, warnings };
      }

      // Find column indices
      const xIndex = headers.indexOf('x');
      const yIndex = headers.indexOf('y');
      const polygonIdIndex = headers.indexOf('polygon_id');
      const typeIndex = headers.indexOf('type');

      // Parse data rows
      const rows: CSVRow[] = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',').map(v => v.trim());
        
        if (values.length < Math.max(xIndex, yIndex) + 1) {
          warnings.push(`Row ${i + 1}: Not enough columns`);
          continue;
        }

        const x = parseFloat(values[xIndex]);
        const y = parseFloat(values[yIndex]);
        
        if (isNaN(x) || isNaN(y)) {
          warnings.push(`Row ${i + 1}: Invalid coordinates`);
          continue;
        }

        rows.push({
          x,
          y,
          polygon_id: polygonIdIndex >= 0 ? values[polygonIdIndex] : undefined,
          type: typeIndex >= 0 && ['external', 'internal'].includes(values[typeIndex]) 
            ? values[typeIndex] as 'external' | 'internal' 
            : undefined
        });
      }

      // Group by polygon_id or create single polygon
      const polygonGroups = new Map<string, CSVRow[]>();
      let defaultPolygonId = 'polygon_1';

      rows.forEach((row, index) => {
        const polygonId = row.polygon_id || defaultPolygonId;
        if (!polygonGroups.has(polygonId)) {
          polygonGroups.set(polygonId, []);
        }
        polygonGroups.get(polygonId)!.push(row);
      });

      // Convert groups to polygons
      polygonGroups.forEach((points, polygonId) => {
        if (points.length < 3) {
          warnings.push(`Polygon ${polygonId}: Not enough points (${points.length})`);
          return;
        }

        const polygon: Polygon = {
          id: polygonId === defaultPolygonId ? uuidv4() : polygonId,
          points: points.map(p => ({ x: p.x, y: p.y })),
          type: points[0].type || 'external'
        };

        polygons.push(polygon);
      });

      // Validate polygons if requested
      if (options.validatePolygons && polygons.length > 0) {
        const validationResult = validatePolygons(polygons);
        if (!validationResult.isValid) {
          warnings.push(...validationResult.errors);
        }
      }

    } catch (error) {
      errors.push(`Failed to parse CSV: ${error instanceof Error ? error.message : String(error)}`);
    }

    return {
      success: errors.length === 0,
      polygons,
      errors,
      warnings
    };
  }

  // ============================================================================
  // BINARY MASK IMPORT
  // ============================================================================

  private async importFromBinaryMask(file: File, options: ImportOptions): Promise<ImportResult> {
    try {
      // This will be processed by the backend since it requires heavy image processing
      const formData = new FormData();
      formData.append('file', file);
      formData.append('imageWidth', options.imageWidth.toString());
      formData.append('imageHeight', options.imageHeight.toString());

      const response = await fetch('/api/segmentation/import/mask', {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error(`Backend processing failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        return {
          success: false,
          polygons: [],
          errors: result.errors || ['Unknown backend error'],
          warnings: result.warnings || []
        };
      }

      // Convert backend polygons to our format
      const polygons: Polygon[] = result.polygons.map((poly: any) => ({
        id: poly.id || uuidv4(),
        points: poly.points,
        type: poly.type || 'external',
        class: poly.class,
        color: poly.color
      }));

      // Validate polygons if requested
      if (options.validatePolygons && polygons.length > 0) {
        const validationResult = validatePolygons(polygons);
        if (!validationResult.isValid) {
          return {
            success: false,
            polygons: [],
            errors: validationResult.errors,
            warnings: result.warnings || []
          };
        }
      }

      return {
        success: true,
        polygons,
        errors: [],
        warnings: result.warnings || []
      };

    } catch (error) {
      return {
        success: false,
        polygons: [],
        errors: [`Failed to process binary mask: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = (e) => reject(new Error('Failed to read file'));
      reader.readAsText(file);
    });
  }

  /**
   * Convert RLE (Run-Length Encoding) to polygons
   * This is a simplified implementation - for production, consider using a proper library
   */
  private async convertRLEToPolygons(rle: { counts: number[]; size: [number, number] }, imageWidth: number, imageHeight: number): Promise<Polygon[]> {
    // For now, return empty array and add to backend processing queue
    // This would require implementing marching squares algorithm or similar
    throw new Error('RLE format not yet supported - please convert to polygon format first');
  }

  /**
   * Merge imported polygons with existing ones based on options
   */
  mergePolygons(existing: Polygon[], imported: Polygon[], options: ImportOptions): Polygon[] {
    if (options.replaceExisting) {
      return imported;
    } else if (options.mergeWithExisting) {
      // Avoid ID conflicts by regenerating IDs for imported polygons if needed
      const existingIds = new Set(existing.map(p => p.id));
      const mergedImported = imported.map(poly => ({
        ...poly,
        id: existingIds.has(poly.id) ? uuidv4() : poly.id
      }));
      return [...existing, ...mergedImported];
    } else {
      return imported;
    }
  }
}

// Export singleton instance
export const segmentationImportService = SegmentationImportService.getInstance();