import apiClient from './client';
import { API_PATHS } from '@spheroseg/shared/constants/apiPaths';

/**
 * Type-safe API endpoints for SpheroSeg
 * Provides a clean interface for all API calls with proper typing
 */

// Generic metadata type
export type GenericMetadata = Record<string, unknown>;

// User preferences type
export type UserPreferences = Record<string, string | number | boolean>;

// Search result type
export interface SearchResult {
  id: string;
  type?: string;
  title?: string;
  description?: string;
  relevance?: number;
  [key: string]: unknown;
}

// Task result type
export interface TaskResult {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress?: number;
  result?: unknown;
  error?: string;
}

// Notification item type
export interface NotificationItem {
  id: string;
  type?: string;
  title?: string;
  message?: string;
  timestamp?: string;
  read?: boolean;
  [key: string]: unknown;
}

// Statistics type
export interface StatisticsData {
  [key: string]: string | number | boolean | null | undefined;
}

// Types for API responses
export interface User {
  id: string;
  email: string;
  username: string;
  firstName?: string;
  lastName?: string;
  organization?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface Project {
  id: string;
  title: string;
  description?: string;
  userId: string;
  public: boolean;
  imageCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Image {
  id: string;
  projectId: string;
  filename: string;
  originalName: string;
  size: number;
  width: number;
  height: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  metadata?: GenericMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface Segment {
  id: string;
  imageId: string;
  polygon: Array<{ x: number; y: number }>;
  type: string;
  confidence?: number;
  metadata?: GenericMetadata;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  organization?: string;
}

export interface CreateProjectRequest {
  title: string;
  description?: string;
  public?: boolean;
}

export interface UpdateProjectRequest {
  title?: string;
  description?: string;
  public?: boolean;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface SearchParams extends PaginationParams {
  q?: string;
}

// API Endpoints
export const api = {
  // Authentication
  auth: {
    login: (data: LoginRequest) => apiClient.post<AuthTokens>(API_PATHS.AUTH.LOGIN, data),

    register: (data: RegisterRequest) => apiClient.post<AuthTokens>(API_PATHS.AUTH.REGISTER, data),

    logout: () => apiClient.post(API_PATHS.AUTH.LOGOUT),

    refresh: (refreshToken: string) =>
      apiClient.post<AuthTokens>(API_PATHS.AUTH.REFRESH, { refreshToken }, { skipAuth: true }),

    forgotPassword: (email: string) => apiClient.post(API_PATHS.AUTH.FORGOT_PASSWORD, { email }),

    resetPassword: (token: string, password: string) => apiClient.post(API_PATHS.AUTH.RESET_PASSWORD, { token, password }),

    verifyEmail: (token: string) => apiClient.post(API_PATHS.AUTH.VERIFY_EMAIL, { token }),
  },

  // Users
  users: {
    me: () => apiClient.get<User>(API_PATHS.USERS.ME),

    update: (data: Partial<User>) => apiClient.patch<User>(API_PATHS.USERS.ME, data),

    updatePassword: (currentPassword: string, newPassword: string) =>
      apiClient.patch(API_PATHS.USERS.PASSWORD, { currentPassword, newPassword }),

    updatePreferences: (preferences: UserPreferences) => apiClient.patch(API_PATHS.USERS.PREFERENCES, preferences),

    delete: (password: string) => apiClient.delete(API_PATHS.USERS.ME, { data: { password } }),

    uploadAvatar: (file: File) => apiClient.upload<{ url: string }>(API_PATHS.USERS.AVATAR, file, { fieldName: 'avatar' }),

    deleteAvatar: () => apiClient.delete(API_PATHS.USERS.AVATAR),
  },

  // Projects
  projects: {
    list: (params?: SearchParams) => apiClient.get<{ data: Project[]; total: number }>(API_PATHS.PROJECTS.BASE, { params: params as any }),

    get: (id: string) => apiClient.get<Project>(API_PATHS.PROJECTS.BY_ID(id)),

    create: (data: CreateProjectRequest) => apiClient.post<Project>(API_PATHS.PROJECTS.BASE, data),

    update: (id: string, data: UpdateProjectRequest) => apiClient.patch<Project>(API_PATHS.PROJECTS.BY_ID(id), data),

    delete: (id: string) => apiClient.delete(API_PATHS.PROJECTS.BY_ID(id)),

    duplicate: (id: string, options?: { includeImages?: boolean; includeSegmentations?: boolean }) =>
      apiClient.post<{ taskId: string }>(`${API_PATHS.PROJECTS.BY_ID(id)}/duplicate`, options),

    export: (id: string, format: 'json' | 'csv' | 'zip') =>
      apiClient.get(API_PATHS.PROJECTS.EXPORT(id), {
        params: { format },
        headers: { Accept: 'application/octet-stream' },
      }),

    share: (id: string, email: string, permission: 'view' | 'edit') =>
      apiClient.post(API_PATHS.PROJECTS.SHARE(id), { email, permission }),

    unshare: (id: string, userId: string) => apiClient.delete(`${API_PATHS.PROJECTS.SHARE(id)}/${userId}`),
  },

  // Images
  images: {
    list: (projectId: string, params?: SearchParams) =>
      apiClient.get<{ data: Image[]; total: number }>(`/projects/${projectId}/images`, { params: params as any }),

    get: (id: string) => apiClient.get<Image>(`/images/${id}`),

    upload: (projectId: string, files: File[], options?: { autoSegment?: boolean }) => {
      const formData = new FormData();
      files.forEach((file) => formData.append('images', file));
      if (options?.autoSegment) {
        formData.append('autoSegment', 'true');
      }
      return apiClient.upload<{ images: Image[] }>(`/projects/${projectId}/images`, formData);
    },

    update: (id: string, data: Partial<Image>) => apiClient.patch<Image>(`/images/${id}`, data),

    delete: (id: string) => apiClient.delete(`/images/${id}`),

    deleteMany: (ids: string[]) => apiClient.post('/images/delete-many', { ids }),

    segment: (id: string) => apiClient.post<{ taskId: string }>(`/images/${id}/segment`),

    segmentMany: (ids: string[]) => apiClient.post<{ taskId: string }>('/images/segment-many', { ids }),
  },

  // Segmentation
  segmentation: {
    getSegments: (imageId: string) => apiClient.get<Segment[]>(`/segmentation/segments/${imageId}`),

    createSegment: (imageId: string, polygon: Array<{ x: number; y: number }>, type?: string) =>
      apiClient.post<Segment>('/segmentation/segments', { imageId, polygon, type }),

    updateSegment: (id: string, data: Partial<Segment>) =>
      apiClient.patch<Segment>(`/segmentation/segments/${id}`, data),

    deleteSegment: (id: string) => apiClient.delete(`/segmentation/segments/${id}`),

    process: (imageId: string, options?: { model?: string; threshold?: number }) =>
      apiClient.post<{ taskId: string }>(`/segmentation/process/${imageId}`, options),

    export: (imageId: string, format: 'json' | 'mask' | 'coco' | 'yolo') =>
      apiClient.post('/segmentation/export', { imageId, format }),

    // Image-specific segmentation endpoints
    get: (imageId: string) => apiClient.get(`/images/${imageId}/segmentation`),

    save: (imageId: string, data: unknown) => apiClient.put(`/images/${imageId}/segmentation`, data),

    delete: (imageId: string) => apiClient.delete(`/images/${imageId}/segmentation`),

    getStatus: (imageId: string) => apiClient.get(`/images/${imageId}/segmentation/status`),

    trigger: (imageId: string, data?: { project_id?: string; [key: string]: unknown }) =>
      apiClient.post(`/segmentation/${imageId}/resegment`, data),

    getQueueStatus: () => apiClient.get('/segmentation/queue'),
  },

  // Metadata
  metadata: {
    update: (id: string, metadata: GenericMetadata) => apiClient.patch(`/metadata/${id}`, metadata),

    search: (query: string, type?: 'image' | 'project' | 'segment') =>
      apiClient.get<SearchResult[]>('/metadata/search', { params: { q: query, type } }),

    batchUpdate: (updates: Array<{ id: string; metadata: GenericMetadata }>) =>
      apiClient.patch('/metadata/batch', { updates }),

    enrich: (ids: string[], provider: 'openai' | 'custom') => apiClient.post('/metadata/enrich', { ids, provider }),

    statistics: (projectId?: string) =>
      apiClient.get<StatisticsData>('/metadata/statistics', { params: { projectId } }),
  },

  // Notifications
  notifications: {
    sendEmail: (to: string, subject: string, body: string) =>
      apiClient.post('/notifications/email', { to, subject, body }),

    subscribe: (subscription: PushSubscription) => apiClient.post('/notifications/subscribe', subscription),

    unsubscribe: (endpoint: string) => apiClient.post('/notifications/unsubscribe', { endpoint }),

    getHistory: (params?: PaginationParams) =>
      apiClient.get<{ data: NotificationItem[]; total: number }>('/notifications/history', { params: params as any }),
  },

  // Tasks (for async operations)
  tasks: {
    get: (taskId: string) => apiClient.get<TaskResult>(`/tasks/${taskId}`),

    cancel: (taskId: string) => apiClient.post(`/tasks/${taskId}/cancel`),
  },

  // Statistics
  stats: {
    overview: () =>
      apiClient.get<{
        totalProjects: number;
        totalImages: number;
        totalSegmentations: number;
        storageUsed: number;
      }>('/stats/overview'),

    project: (projectId: string) => apiClient.get<StatisticsData>(`/stats/projects/${projectId}`),

    user: () => apiClient.get<StatisticsData>('/stats/user'),
  },
};

// Export individual namespaces for convenience
export const authApi = api.auth;
export const usersApi = api.users;
export const projectsApi = api.projects;
export const imagesApi = api.images;
export const segmentationApi = api.segmentation;
export const metadataApi = api.metadata;
export const notificationsApi = api.notifications;
export const tasksApi = api.tasks;
export const statsApi = api.stats;

export default api;
