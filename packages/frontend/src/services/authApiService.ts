import apiClient, { type ApiError } from '@/services/api/client';
import logger from '@/utils/logging/unifiedLogger';
import { API_PATHS } from '@spheroseg/shared/constants/apiPaths';

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
  confirm_password: string;
}

export interface DeleteAccountRequest {
  username: string;
  password: string;
}

export interface AuthApiResponse {
  message: string;
}

class AuthApiService {
  private baseUrl = '/auth';

  async sendVerificationEmail(email: string): Promise<any> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/send-verification-email`, { email });
      return response.data;
    } catch (_error) {
      const apiError = _error as ApiError;
      logger.error('Error sending verification email:', {
        message: apiError.message,
        code: apiError.code,
        status: apiError.status,
      });
      throw _error;
    }
  }

  async verifyEmail(token: string): Promise<any> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/verify-email`, { params: { token } });
      return response.data;
    } catch (_error) {
      const apiError = _error as ApiError;
      logger.error('Error verifying email:', {
        message: apiError.message,
        code: apiError.code,
        status: apiError.status,
      });
      throw _error;
    }
  }

  async changePassword(data: ChangePasswordRequest): Promise<AuthApiResponse> {
    try {
      // Use the correct backend endpoint: /users/me/password
      const response = await apiClient.put<AuthApiResponse>('/users/me/password', data);

      // The new API client already handles standardized responses
      // and throws on error, so if we get here, it's successful
      return response.data;
    } catch (_error) {
      const apiError = _error as ApiError;
      logger.error('Error changing password:', {
        message: apiError.message,
        code: apiError.code,
        status: apiError.status,
      });
      throw _error;
    }
  }

  async deleteAccount(data: DeleteAccountRequest): Promise<AuthApiResponse> {
    try {
      // Use the correct backend endpoint: /users/me
      const response = await apiClient.delete<AuthApiResponse>(API_PATHS.USERS.ME, { data });

      // The new API client already handles standardized responses
      // and throws on error, so if we get here, it's successful
      return response.data;
    } catch (_error) {
      const apiError = _error as ApiError;
      logger.error('Error deleting account:', {
        message: apiError.message,
        code: apiError.code,
        status: apiError.status,
      });
      throw _error;
    }
  }
}

export default new AuthApiService();
