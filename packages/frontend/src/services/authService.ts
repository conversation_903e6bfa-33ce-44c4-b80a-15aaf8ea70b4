/**
 * Unified Authentication Service
 *
 * This service consolidates all authentication functionality into a single,
 * comprehensive API for managing user authentication, tokens, and sessions.
 */

import { handleError, AppError, ErrorType } from '@/utils/error/unifiedErrorHandler';
import cacheService, { CacheLayer } from '@/services/unifiedCacheService';
import apiClient from '@/services/api/client';
import { API_PATHS as apiPaths } from '@spheroseg/shared/constants/apiPaths';

// Lazy load logger to avoid circular dependency
let logger: any = null;
const getLogger = () => {
  if (!logger) {
    // Use console as fallback until logger is loaded
    logger = {
      error: (...args: any[]) => console.error('[UnifiedAuthService]', ...args),
      warn: (...args: any[]) => console.warn('[UnifiedAuthService]', ...args),
      info: (...args: any[]) => console.info('[UnifiedAuthService]', ...args),
      debug: (...args: any[]) => console.debug('[UnifiedAuthService]', ...args),
    };
    
    // Async load the real logger
    import('@/utils/logging/unifiedLogger').then(module => {
      logger = module.createLogger('UnifiedAuthService');
    });
  }
  return logger;
};

// ===========================
// Types and Interfaces
// ===========================

export interface User {
  id: string;
  email: string;
  username: string;
  role: 'admin' | 'user' | 'guest';
  avatar?: string;
  preferences?: Record<string, unknown>;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  username: string;
  confirmPassword?: string;
}

export interface AuthConfig {
  tokenStorageKey: string;
  refreshTokenStorageKey: string;
  userStorageKey: string;
  persistentLoginKey: string;
  tokenRefreshThreshold: number; // Minutes before expiry to refresh
  maxRetries: number;
  cookieDomain?: string;
  cookiePath?: string;
  cookieSecure?: boolean;
  cookieSameSite?: 'strict' | 'lax' | 'none';
}

export interface AuthEventPayload {
  type: 'login' | 'logout' | 'refresh' | 'expire' | 'error';
  user?: User;
  error?: Error;
}

// ===========================
// Default Configuration
// ===========================

const DEFAULT_CONFIG: AuthConfig = {
  tokenStorageKey: 'spheroseg_access_token',
  refreshTokenStorageKey: 'spheroseg_refresh_token',
  userStorageKey: 'spheroseg_user',
  persistentLoginKey: 'login_persistent',
  tokenRefreshThreshold: 5, // 5 minutes
  maxRetries: 3,
  cookiePath: '/',
  cookieSecure: process.env.NODE_ENV === 'production',
  cookieSameSite: 'lax',
};

// ===========================
// Service Class
// ===========================

class UnifiedAuthService {
  private config: AuthConfig = DEFAULT_CONFIG;
  private refreshPromise: Promise<AuthTokens> | null = null;
  private tokenRefreshTimer: NodeJS.Timeout | null = null;
  private eventListeners: Map<string, Set<(payload: AuthEventPayload) => void>> = new Map();
  private requestQueue: Array<{
    resolve: (value: unknown) => void;
    reject: (error: unknown) => void;
  }> = [];

  constructor() {
    this.setupStorageSync();
    this.restoreSession();
  }

  /**
   * Configure the auth service
   */
  public configure(config: Partial<AuthConfig>): void {
    this.config = { ...this.config, ...config };
    getLogger().info('Auth service configured', config);
  }

  /**
   * Login user with credentials
   */
  public async login(credentials: LoginCredentials): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      getLogger().info('Attempting login', { email: credentials.email });

      const response = await apiClient.post(apiPaths.AUTH.LOGIN, {
        email: credentials.email,
        password: credentials.password,
      });

      const { user, access_token, refresh_token, expires_in } = response.data as any;

      const tokens: AuthTokens = {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresIn: expires_in || 3600,
        tokenType: 'Bearer',
      };

      // Store tokens and user
      await this.storeAuthData(user, tokens, credentials.rememberMe);

      // Setup token refresh
      this.scheduleTokenRefresh(tokens.expiresIn);

      // Emit login event
      this.emitAuthEvent({
        type: 'login',
        user,
      });

      getLogger().info('Login successful', { userId: user.id });

      return { user, tokens };
    } catch (_error) {
      getLogger().error('Login failed', _error);
      throw handleError(_error, {
        context: 'login' as any,
      });
    }
  }

  /**
   * Register new user
   */
  public async register(credentials: RegisterCredentials): Promise<{ user: User; tokens: AuthTokens }> {
    try {
      getLogger().info('Attempting registration', { email: credentials.email });

      // Validate passwords match
      if (credentials.confirmPassword && credentials.password !== credentials.confirmPassword) {
        throw new AppError('Passwords do not match', ErrorType.VALIDATION);
      }

      const response = await apiClient.post(apiPaths.AUTH.REGISTER, {
        email: credentials.email,
        password: credentials.password,
        username: credentials.username,
      });

      const { user, access_token, refresh_token, expires_in } = response.data as any;

      const tokens: AuthTokens = {
        accessToken: access_token,
        refreshToken: refresh_token,
        expiresIn: expires_in || 3600,
        tokenType: 'Bearer',
      };

      // Store tokens and user
      await this.storeAuthData(user, tokens, true);

      // Setup token refresh
      this.scheduleTokenRefresh(tokens.expiresIn);

      // Emit login event
      this.emitAuthEvent({
        type: 'login',
        user,
      });

      getLogger().info('Registration successful', { userId: user.id });

      return { user, tokens };
    } catch (_error) {
      getLogger().error('Registration failed', _error);
      throw handleError(_error, {
        context: 'register' as any,
      });
    }
  }

  /**
   * Logout user
   */
  public async logout(): Promise<void> {
    try {
      getLogger().info('Attempting logout');

      // Get current user for event
      const user = this.getCurrentUser();

      // Call logout endpoint (optional - some backends track sessions)
      try {
        await apiClient.post(apiPaths.AUTH.LOGOUT);
      } catch (_error) {
        // Ignore logout endpoint errors - we'll clear local data anyway
        getLogger().warn('Logout endpoint failed, clearing local data', _error);
      }

      // Clear all auth data
      await this.clearAuthData();

      // Cancel token refresh
      this.cancelTokenRefresh();

      // Clear cache
      await cacheService.deleteByTag('user-data');

      // Emit logout event
      this.emitAuthEvent({
        type: 'logout',
        user: user || undefined,
      });

      getLogger().info('Logout successful');
    } catch (_error) {
      getLogger().error('Logout failed', _error);
      throw handleError(_error, {
        context: 'logout' as any,
      });
    }
  }

  /**
   * Refresh authentication tokens
   */
  public async refreshTokens(): Promise<AuthTokens> {
    // If refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();

    try {
      const tokens = await this.refreshPromise;
      this.refreshPromise = null;

      // Process queued requests
      this.processRequestQueue(true);

      return tokens;
    } catch (_error) {
      this.refreshPromise = null;

      // Process queued requests with _error
      this.processRequestQueue(false);

      throw _error;
    }
  }

  /**
   * Get current access token
   */
  public getAccessToken(): string | null {
    // Try multiple storage locations
    const token =
      localStorage.getItem(this.config.tokenStorageKey) ||
      sessionStorage.getItem(this.config.tokenStorageKey) ||
      this.getCookie('auth_token');

    if (token && this.isTokenValid(token)) {
      return token;
    }

    return null;
  }

  /**
   * Get current refresh token
   */
  public getRefreshToken(): string | null {
    return (
      localStorage.getItem(this.config.refreshTokenStorageKey) ||
      sessionStorage.getItem(this.config.refreshTokenStorageKey) ||
      this.getCookie('refresh_token')
    );
  }

  /**
   * Get current user
   */
  public getCurrentUser(): User | null {
    try {
      const userStr =
        localStorage.getItem(this.config.userStorageKey) || sessionStorage.getItem(this.config.userStorageKey);

      if (userStr) {
        return JSON.parse(userStr);
      }
    } catch (_error) {
      getLogger().error('Failed to parse user data', _error);
    }

    return null;
  }

  /**
   * Check if user is authenticated
   */
  public isAuthenticated(): boolean {
    return !!this.getAccessToken() && !!this.getCurrentUser();
  }

  /**
   * Add authentication event listener
   */
  public addEventListener(event: string, callback: (payload: AuthEventPayload) => void): () => void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }

    this.eventListeners.get(event)!.add(callback);

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(event)?.delete(callback);
    };
  }

  /**
   * Request password reset
   */
  public async requestPasswordReset(email: string): Promise<void> {
    try {
      getLogger().info('Requesting password reset', { email });

      await apiClient.post(apiPaths.AUTH.FORGOT_PASSWORD, { email });

      getLogger().info('Password reset requested successfully');
    } catch (_error) {
      getLogger().error('Password reset request failed', _error);
      throw handleError(_error, {
        context: 'requestPasswordReset' as any,
      });
    }
  }

  /**
   * Reset password with token
   */
  public async resetPassword(token: string, newPassword: string): Promise<void> {
    try {
      getLogger().info('Resetting password');

      await apiClient.post(apiPaths.AUTH.RESET_PASSWORD, {
        token,
        password: newPassword,
      });

      getLogger().info('Password reset successful');
    } catch (_error) {
      getLogger().error('Password reset failed', _error);
      throw handleError(_error, {
        context: 'resetPassword' as any,
      });
    }
  }

  /**
   * Update user profile
   */
  public async updateProfile(updates: Partial<User>): Promise<User> {
    try {
      getLogger().info('Updating user profile', { userId: updates.id });

      const response = await apiClient.patch(apiPaths.USERS.ME, updates);
      const updatedUser = response.data as any;

      // Update stored user
      const currentUser = this.getCurrentUser();
      if (currentUser) {
        const mergedUser = { ...currentUser, ...updatedUser };
        this.storeUser(mergedUser);
      }

      getLogger().info('Profile updated successfully');

      return updatedUser;
    } catch (_error) {
      getLogger().error('Profile update failed', _error);
      throw handleError(_error, {
        context: 'updateProfile' as any,
      });
    }
  }

  /**
   * Queue request for retry after token refresh
   */
  public queueRequest(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({ resolve, reject });
    });
  }

  // ===========================
  // Private Helper Methods
  // ===========================

  private async performTokenRefresh(): Promise<AuthTokens> {
    try {
      const refreshToken = this.getRefreshToken();

      if (!refreshToken) {
        throw new AppError('No refresh token available', ErrorType.AUTHENTICATION);
      }

      getLogger().info('Refreshing tokens');

      const response = await apiClient.post(apiPaths.AUTH.REFRESH, {
        refresh_token: refreshToken,
      });

      const { access_token, refresh_token: newRefreshToken, expires_in } = response.data as any;

      const tokens: AuthTokens = {
        accessToken: access_token,
        refreshToken: newRefreshToken || refreshToken,
        expiresIn: expires_in || 3600,
        tokenType: 'Bearer',
      };

      // Store new tokens
      this.storeTokens(tokens);

      // Reschedule token refresh
      this.scheduleTokenRefresh(tokens.expiresIn);

      // Emit refresh event
      this.emitAuthEvent({
        type: 'refresh',
        user: this.getCurrentUser() || undefined,
      });

      getLogger().info('Token refresh successful');

      return tokens;
    } catch (_error) {
      getLogger().error('Token refresh failed', _error);

      // Clear auth data on refresh failure
      await this.clearAuthData();

      // Emit expire event
      this.emitAuthEvent({
        type: 'expire',
        error: _error as Error,
      });

      throw handleError(_error, {
        context: 'refreshTokens' as any,
      });
    }
  }

  private async storeAuthData(user: User, tokens: AuthTokens, rememberMe?: boolean): Promise<void> {
    const storage = rememberMe ? localStorage : sessionStorage;

    // Store tokens
    storage.setItem(this.config.tokenStorageKey, tokens.accessToken);
    storage.setItem(this.config.refreshTokenStorageKey, tokens.refreshToken);

    // Store user
    storage.setItem(this.config.userStorageKey, JSON.stringify(user));

    // Set persistent login flag
    if (rememberMe) {
      localStorage.setItem(this.config.persistentLoginKey, 'true');
    }

    // Also set cookies as fallback
    if (this.config.cookieDomain) {
      this.setCookie('auth_token', tokens.accessToken, {
        expires: new Date(Date.now() + tokens.expiresIn * 1000),
        domain: this.config.cookieDomain,
        path: this.config.cookiePath,
        secure: this.config.cookieSecure,
        sameSite: this.config.cookieSameSite,
      });
    }

    // Cache user data
    await cacheService.set(`user:${user.id}`, user, {
      ttl: tokens.expiresIn * 1000,
      layer: [CacheLayer.MEMORY],
      tags: ['user-data', `user-${user.id}`],
    });
  }

  private storeTokens(tokens: AuthTokens): void {
    const isPersistent = localStorage.getItem(this.config.persistentLoginKey) === 'true';
    const storage = isPersistent ? localStorage : sessionStorage;

    storage.setItem(this.config.tokenStorageKey, tokens.accessToken);
    storage.setItem(this.config.refreshTokenStorageKey, tokens.refreshToken);
  }

  private storeUser(user: User): void {
    const isPersistent = localStorage.getItem(this.config.persistentLoginKey) === 'true';
    const storage = isPersistent ? localStorage : sessionStorage;

    storage.setItem(this.config.userStorageKey, JSON.stringify(user));
  }

  private async clearAuthData(): Promise<void> {
    // Clear from all storages
    [localStorage, sessionStorage].forEach((storage) => {
      storage.removeItem(this.config.tokenStorageKey);
      storage.removeItem(this.config.refreshTokenStorageKey);
      storage.removeItem(this.config.userStorageKey);
    });

    localStorage.removeItem(this.config.persistentLoginKey);

    // Clear cookies
    this.deleteCookie('auth_token');
    this.deleteCookie('refresh_token');
  }

  private scheduleTokenRefresh(expiresIn: number): void {
    this.cancelTokenRefresh();

    // Schedule refresh before token expires
    const refreshTime = (expiresIn - this.config.tokenRefreshThreshold * 60) * 1000;

    if (refreshTime > 0) {
      this.tokenRefreshTimer = setTimeout(() => {
        this.refreshTokens().catch((error) => {
          getLogger().error('Scheduled token refresh failed', error);
        });
      }, refreshTime);
    }
  }

  private cancelTokenRefresh(): void {
    if (this.tokenRefreshTimer) {
      clearTimeout(this.tokenRefreshTimer);
      this.tokenRefreshTimer = null;
    }
  }

  private isTokenValid(token: string): boolean {
    try {
      // Decode JWT without verification (for client-side check)
      const parts = token.split('.');
      if (parts.length !== 3) return false;

      const payload = JSON.parse(atob(parts[1]));
      const exp = payload.exp;

      if (!exp) return true; // No expiration

      // Check if token is expired
      return Date.now() < exp * 1000;
    } catch (_error) {
      return false;
    }
  }

  private processRequestQueue(success: boolean): void {
    const queue = [...this.requestQueue];
    this.requestQueue = [];

    queue.forEach(({ resolve, reject }) => {
      if (success) {
        resolve(undefined);
      } else {
        reject(new AppError('Token refresh failed', ErrorType.AUTHENTICATION));
      }
    });
  }

  private setupStorageSync(): void {
    // Sync auth state across tabs
    window.addEventListener('storage', (event) => {
      if (event.key === this.config.tokenStorageKey || event.key === this.config.userStorageKey) {
        // Auth state changed in another tab
        if (!event.newValue) {
          // User logged out in another tab
          this.emitAuthEvent({
            type: 'logout',
          });
        } else {
          // User logged in or token refreshed in another tab
          const user = this.getCurrentUser();
          if (user) {
            this.emitAuthEvent({
              type: 'login',
              user,
            });
          }
        }
      }
    });
  }

  private restoreSession(): void {
    // Check if we have valid session
    if (this.isAuthenticated()) {
      const token = this.getAccessToken();
      if (token) {
        // Calculate remaining time and schedule refresh
        try {
          const parts = token.split('.');
          const payload = JSON.parse(atob(parts[1]));
          const exp = payload.exp;

          if (exp) {
            const expiresIn = Math.max(0, exp - Date.now() / 1000);
            this.scheduleTokenRefresh(expiresIn);
          }
        } catch (_error) {
          getLogger().error('Failed to parse token for refresh scheduling', _error);
        }
      }
    }
  }

  private emitAuthEvent(payload: AuthEventPayload): void {
    const listeners = this.eventListeners.get('authStateChange') || new Set();
    listeners.forEach((callback) => {
      try {
        callback(payload);
      } catch (_error) {
        getLogger().error('Auth event listener _error', _error);
      }
    });

    // Also emit as DOM event for non-React components
    window.dispatchEvent(new CustomEvent('auth-state-change', { detail: payload }));
  }

  private setCookie(name: string, value: string, options: Record<string, unknown> = {}): void {
    let cookie = `${name}=${encodeURIComponent(value)}`;

    if (options.expires) {
      cookie += `; expires=${(options.expires as Date).toUTCString()}`;
    }
    if (options.path) {
      cookie += `; path=${options.path}`;
    }
    if (options.domain) {
      cookie += `; domain=${options.domain}`;
    }
    if (options.secure) {
      cookie += '; secure';
    }
    if (options.sameSite) {
      cookie += `; samesite=${options.sameSite}`;
    }

    document.cookie = cookie;
  }

  private getCookie(name: string): string | null {
    const match = document.cookie.match(new RegExp(`(^| )${name}=([^;]+)`));
    return match ? decodeURIComponent(match[2]) : null;
  }

  private deleteCookie(name: string): void {
    this.setCookie(name, '', {
      expires: new Date(0),
      path: this.config.cookiePath,
      domain: this.config.cookieDomain,
    });
  }

  /**
   * Remove all authentication tokens
   * (Wrapper for clearAuthData for backward compatibility)
   */
  public async removeTokens(): Promise<void> {
    await this.clearAuthData();
  }

  /**
   * Check if a token is valid (not expired)
   * @param token - JWT token to validate
   * @param checkExpiry - Whether to check token expiry (default: true)
   */
  public isValidToken(token: string, checkExpiry: boolean = true): boolean {
    if (!token) return false;
    
    if (!checkExpiry) return true;
    
    try {
      // Parse JWT payload
      const parts = token.split('.');
      if (parts.length !== 3) return false;
      
      const payload = JSON.parse(atob(parts[1]));
      
      // Check expiry
      if (payload.exp) {
        const now = Date.now() / 1000;
        return payload.exp > now;
      }
      
      return true;
    } catch (error) {
      getLogger().error('Error validating token:', error);
      return false;
    }
  }
}

// ===========================
// Singleton Instance
// ===========================

const authService = new UnifiedAuthService();

// ===========================
// Named Exports for backward compatibility
// ===========================

export const getAccessToken = (validate?: boolean, autoRemove?: boolean) => {
  const token = authService.getAccessToken();
  
  if (validate && token) {
    const isValid = authService.isValidToken(token);
    if (!isValid && autoRemove) {
      authService.removeTokens();
      return null;
    }
    return isValid ? token : null;
  }
  
  return token;
};

export const removeTokens = () => authService.removeTokens();
export const isValidToken = (token: string, checkExpiry?: boolean) => authService.isValidToken(token, checkExpiry);
export const login = (credentials: LoginCredentials) => authService.login(credentials);
export const logout = () => authService.logout();
export const register = (credentials: RegisterCredentials) => authService.register(credentials);
export const getCurrentUser = () => authService.getCurrentUser();
export const isAuthenticated = () => authService.isAuthenticated();
export const getRefreshToken = () => authService.getRefreshToken();

// Additional exports for backward compatibility
export const isAccessTokenExpired = () => {
  const token = authService.getAccessToken();
  if (!token) return true;
  return !authService.isValidToken(token);
};

export const refreshAccessToken = () => authService.refreshTokens();
export const setTokens = (tokens: { accessToken: string; refreshToken: string }) => {
  // Store tokens using the internal methods
  if (typeof window !== 'undefined') {
    localStorage.setItem('spheroseg_access_token', tokens.accessToken);
    localStorage.setItem('spheroseg_refresh_token', tokens.refreshToken);
  }
};

// Route management exports
export const saveCurrentRoute = (route: string) => {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('spheroseg_last_route', route);
  }
};

export const getLastRoute = () => {
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('spheroseg_last_route');
  }
  return null;
};

export const clearLastRoute = () => {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('spheroseg_last_route');
  }
};

export const shouldPersistSession = () => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('spheroseg_persist_session') === 'true';
  }
  return false;
};

// ===========================
// Export
// ===========================

export const requestPasswordReset = (email: string) => authService.requestPasswordReset(email);

export default authService;
