/**
 * Database Migration Service
 * 
 * This service handles the migration of data from the four separate IndexedDB databases
 * to the new unified database structure.
 * 
 * Migrates from:
 * - spheroseg-images -> unified.images
 * - spheroseg_secure_storage -> unified.secureData
 * - spheroseg_settings -> unified.settings
 * - spheroseg_cache -> unified.cache
 */

import logger from '@/utils/logging/unifiedLogger';
import { unifiedIndexedDB } from './unifiedIndexedDBService';

interface MigrationStatus {
  completed: boolean;
  timestamp: number;
  migratedCounts: {
    images: number;
    secureData: number;
    settings: number;
    cache: number;
  };
  errors: string[];
}

class DatabaseMigrationService {
  private migrationKey = 'spheroseg_db_migration_status';

  /**
   * Check if migration has already been completed
   */
  async getMigrationStatus(): Promise<MigrationStatus | null> {
    try {
      const status = localStorage.getItem(this.migrationKey);
      return status ? JSON.parse(status) : null;
    } catch (error) {
      logger.error('Failed to get migration status:', error);
      return null;
    }
  }

  /**
   * Save migration status
   */
  private async saveMigrationStatus(status: MigrationStatus): Promise<void> {
    try {
      localStorage.setItem(this.migrationKey, JSON.stringify(status));
    } catch (error) {
      logger.error('Failed to save migration status:', error);
    }
  }

  /**
   * Run the complete migration process
   */
  async migrate(): Promise<MigrationStatus> {
    logger.info('Starting database migration to unified IndexedDB');

    const status: MigrationStatus = {
      completed: false,
      timestamp: Date.now(),
      migratedCounts: {
        images: 0,
        secureData: 0,
        settings: 0,
        cache: 0,
      },
      errors: [],
    };

    try {
      // Check if migration already completed
      const existingStatus = await this.getMigrationStatus();
      if (existingStatus?.completed) {
        logger.info('Migration already completed, skipping');
        return existingStatus;
      }

      // Migrate each database
      status.migratedCounts.images = await this.migrateImagesDatabase(status.errors);
      status.migratedCounts.secureData = await this.migrateSecureDataDatabase(status.errors);
      status.migratedCounts.settings = await this.migrateSettingsDatabase(status.errors);
      status.migratedCounts.cache = await this.migrateCacheDatabase(status.errors);

      // Mark migration as completed if no critical errors
      status.completed = status.errors.length === 0;
      status.timestamp = Date.now();

      await this.saveMigrationStatus(status);

      if (status.completed) {
        logger.info('Database migration completed successfully', status.migratedCounts);
        // Clean up old databases after successful migration
        await this.cleanupOldDatabases();
      } else {
        logger.warn('Database migration completed with errors:', status.errors);
      }

      return status;
    } catch (error) {
      logger.error('Migration process failed:', error);
      status.errors.push(`Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.saveMigrationStatus(status);
      return status;
    }
  }

  /**
   * Migrate images database (spheroseg-images)
   */
  private async migrateImagesDatabase(errors: string[]): Promise<number> {
    try {
      logger.info('Migrating images database...');
      
      const db = await this.openOldDatabase('spheroseg-images', 1);
      if (!db) {
        logger.info('No images database found to migrate');
        return 0;
      }

      const transaction = db.transaction(['images'], 'readonly');
      const store = transaction.objectStore('images');
      const getAllRequest = store.getAll();

      return new Promise((resolve) => {
        getAllRequest.onsuccess = async () => {
          const images = getAllRequest.result;
          let migratedCount = 0;

          for (const image of images) {
            try {
              await unifiedIndexedDB.storeImageBlob(
                image.id,
                image.projectId,
                image.blob
              );
              migratedCount++;
            } catch (error) {
              const errorMsg = `Failed to migrate image ${image.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
              logger.error(errorMsg);
              errors.push(errorMsg);
            }
          }

          db.close();
          logger.info(`Migrated ${migratedCount}/${images.length} images`);
          resolve(migratedCount);
        };

        getAllRequest.onerror = () => {
          const errorMsg = `Failed to read images database: ${getAllRequest.error}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
          db.close();
          resolve(0);
        };
      });
    } catch (error) {
      const errorMsg = `Images database migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMsg);
      errors.push(errorMsg);
      return 0;
    }
  }

  /**
   * Migrate secure storage database (spheroseg_secure_storage)
   */
  private async migrateSecureDataDatabase(errors: string[]): Promise<number> {
    try {
      logger.info('Migrating secure storage database...');
      
      const db = await this.openOldDatabase('spheroseg_secure_storage', 1);
      if (!db) {
        logger.info('No secure storage database found to migrate');
        return 0;
      }

      const transaction = db.transaction(['encrypted_data'], 'readonly');
      const store = transaction.objectStore('encrypted_data');
      const getAllRequest = store.getAll();

      return new Promise((resolve) => {
        getAllRequest.onsuccess = async () => {
          const items = getAllRequest.result;
          let migratedCount = 0;

          for (const item of items) {
            try {
              // Migrate the encrypted item as-is (already encrypted and base64 encoded)
              const decryptedValue = await this.decryptOldSecureItem(item);
              if (decryptedValue !== null) {
                await unifiedIndexedDB.setSecureItem(item.key, decryptedValue);
                migratedCount++;
              }
            } catch (error) {
              const errorMsg = `Failed to migrate secure item ${item.key}: ${error instanceof Error ? error.message : 'Unknown error'}`;
              logger.error(errorMsg);
              errors.push(errorMsg);
            }
          }

          db.close();
          logger.info(`Migrated ${migratedCount}/${items.length} secure items`);
          resolve(migratedCount);
        };

        getAllRequest.onerror = () => {
          const errorMsg = `Failed to read secure storage database: ${getAllRequest.error}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
          db.close();
          resolve(0);
        };
      });
    } catch (error) {
      const errorMsg = `Secure storage migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMsg);
      errors.push(errorMsg);
      return 0;
    }
  }

  /**
   * Migrate settings database (spheroseg_settings)
   */
  private async migrateSettingsDatabase(errors: string[]): Promise<number> {
    try {
      logger.info('Migrating settings database...');
      
      const db = await this.openOldDatabase('spheroseg_settings', 1);
      if (!db) {
        logger.info('No settings database found to migrate');
        return 0;
      }

      const transaction = db.transaction(['settings'], 'readonly');
      const store = transaction.objectStore('settings');
      const getAllRequest = store.getAll();

      return new Promise((resolve) => {
        getAllRequest.onsuccess = async () => {
          const items = getAllRequest.result;
          let migratedCount = 0;

          for (const item of items) {
            try {
              await unifiedIndexedDB.setSetting(item.key, item.value);
              migratedCount++;
            } catch (error) {
              const errorMsg = `Failed to migrate setting ${item.key}: ${error instanceof Error ? error.message : 'Unknown error'}`;
              logger.error(errorMsg);
              errors.push(errorMsg);
            }
          }

          db.close();
          logger.info(`Migrated ${migratedCount}/${items.length} settings`);
          resolve(migratedCount);
        };

        getAllRequest.onerror = () => {
          const errorMsg = `Failed to read settings database: ${getAllRequest.error}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
          db.close();
          resolve(0);
        };
      });
    } catch (error) {
      const errorMsg = `Settings migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMsg);
      errors.push(errorMsg);
      return 0;
    }
  }

  /**
   * Migrate cache database (spheroseg_cache)
   */
  private async migrateCacheDatabase(errors: string[]): Promise<number> {
    try {
      logger.info('Migrating cache database...');
      
      const db = await this.openOldDatabase('spheroseg_cache', 1);
      if (!db) {
        logger.info('No cache database found to migrate');
        return 0;
      }

      const transaction = db.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const getAllRequest = store.getAll();

      return new Promise((resolve) => {
        getAllRequest.onsuccess = async () => {
          const items = getAllRequest.result;
          let migratedCount = 0;

          for (const item of items) {
            try {
              // Only migrate non-expired cache entries
              if (Date.now() < item.expiresAt) {
                await unifiedIndexedDB.setCacheEntry(item.key, item);
                migratedCount++;
              }
            } catch (error) {
              const errorMsg = `Failed to migrate cache item ${item.key}: ${error instanceof Error ? error.message : 'Unknown error'}`;
              logger.error(errorMsg);
              errors.push(errorMsg);
            }
          }

          db.close();
          logger.info(`Migrated ${migratedCount}/${items.length} cache items (expired items skipped)`);
          resolve(migratedCount);
        };

        getAllRequest.onerror = () => {
          const errorMsg = `Failed to read cache database: ${getAllRequest.error}`;
          logger.error(errorMsg);
          errors.push(errorMsg);
          db.close();
          resolve(0);
        };
      });
    } catch (error) {
      const errorMsg = `Cache migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      logger.error(errorMsg);
      errors.push(errorMsg);
      return 0;
    }
  }

  /**
   * Open an old database for reading
   */
  private async openOldDatabase(name: string, version: number): Promise<IDBDatabase | null> {
    return new Promise((resolve) => {
      try {
        const request = indexedDB.open(name, version);
        
        request.onsuccess = () => {
          resolve(request.result);
        };
        
        request.onerror = () => {
          logger.warn(`Old database ${name} not found or cannot be opened`);
          resolve(null);
        };

        request.onblocked = () => {
          logger.warn(`Old database ${name} is blocked`);
          resolve(null);
        };
      } catch (error) {
        logger.warn(`Failed to open old database ${name}:`, error);
        resolve(null);
      }
    });
  }

  /**
   * Decrypt an item from the old secure storage
   * Note: This is a simplified version - in reality, we'd need to maintain 
   * compatibility with the old encryption system
   */
  private async decryptOldSecureItem(item: any): Promise<any> {
    try {
      // For migration, we'll try to decrypt using the same method as the old system
      // If decryption fails, we'll log it and return null
      
      if (!item.encrypted || !item.value) {
        return JSON.parse(item.value || '{}');
      }

      // Decode base64
      const combined = Uint8Array.from(atob(item.value), (c) => c.charCodeAt(0));
      
      // Extract IV and encrypted data (first 12 bytes are IV)
      const iv = combined.slice(0, 12);
      const encrypted = combined.slice(12);

      // For the migration, we'll need to either:
      // 1. Keep the encrypted data as-is and let the new system re-encrypt it
      // 2. Or store it in a way that the new system can handle
      
      // For now, we'll store the raw encrypted data and let the unified service handle it
      return {
        _migrated: true,
        _encrypted: true,
        _value: item.value
      };
    } catch (error) {
      logger.warn(`Failed to decrypt old secure item ${item.key}:`, error);
      return null;
    }
  }

  /**
   * Clean up old databases after successful migration
   */
  private async cleanupOldDatabases(): Promise<void> {
    const oldDatabases = [
      'spheroseg-images',
      'spheroseg_secure_storage', 
      'spheroseg_settings',
      'spheroseg_cache'
    ];

    logger.info('Cleaning up old databases...');

    for (const dbName of oldDatabases) {
      try {
        await new Promise<void>((resolve, reject) => {
          const deleteRequest = indexedDB.deleteDatabase(dbName);
          
          deleteRequest.onsuccess = () => {
            logger.debug(`Deleted old database: ${dbName}`);
            resolve();
          };
          
          deleteRequest.onerror = () => {
            logger.warn(`Failed to delete old database ${dbName}:`, deleteRequest.error);
            resolve(); // Don't fail the cleanup process
          };

          deleteRequest.onblocked = () => {
            logger.warn(`Deletion of old database ${dbName} was blocked`);
            resolve(); // Don't fail the cleanup process
          };

          // Timeout after 5 seconds
          setTimeout(() => {
            logger.warn(`Timeout deleting old database ${dbName}`);
            resolve();
          }, 5000);
        });
      } catch (error) {
        logger.warn(`Error during cleanup of ${dbName}:`, error);
      }
    }

    logger.info('Old databases cleanup completed');
  }

  /**
   * Force re-migration (for debugging or recovery)
   */
  async forceMigration(): Promise<MigrationStatus> {
    logger.info('Forcing database re-migration');
    
    // Clear migration status
    localStorage.removeItem(this.migrationKey);
    
    // Run migration
    return this.migrate();
  }

  /**
   * Rollback migration (restore old databases if possible)
   * Note: This is a placeholder for potential rollback functionality
   */
  async rollbackMigration(): Promise<boolean> {
    logger.warn('Migration rollback requested - this feature is not implemented');
    logger.warn('To rollback, you would need to restore from a backup or clear all data');
    return false;
  }
}

// Export singleton instance
export const migrationService = new DatabaseMigrationService();
export default migrationService;