import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import type { Project, ProjectImage, ImageStatus } from "@/types";
import apiClient from "@/services/api/client";
import axios from "axios";
import config from "@/config";
import { getProjectImages } from "@/api/projectImages";

// The mapApiImageToProjectImage function is now imported from @/api/projectImages

// Define the shape of the data received via WebSocket
interface SegmentationUpdateData {
  imageId: string;
  status: ImageStatus;
  resultPath?: string | null; // Expect client-relative path from the service
  error?: string;
}

export const useProjectData = (projectId: string | undefined) => {
  const navigate = useNavigate();
  const { user, token } = useAuth();
  const [project, setProject] = useState<Project | null>(null);
  const [images, setImages] = useState<ProjectImage[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  // Import API configuration from config
  const apiBaseUrl = config.apiBaseUrl;

  // Helper function to clean project ID (ensure it has the correct format for API calls)
  const cleanProjectId = useCallback((id: string): string => {
    // Skip if id is not valid
    if (!id) return id;

    // Remove "project-" prefix if it exists
    if (id.startsWith("project-")) {
      const cleanedId = id.substring(8);

      return cleanedId;
    }

    // Return the ID as is if it doesn't have the prefix
    return id;
  }, []);

  const fetchProjectData = useCallback(async () => {
    if (!projectId || !user?.id) {
      setError("Project ID or user information is missing.");
      setLoading(false);
      return;
    }

    // Clean the projectId to ensure we use just the UUID
    const cleanedProjectId = cleanProjectId(projectId);

    setLoading(true);
    setError(null);

    try {
      // First, attempt to check if this is a valid project

      // Try different API endpoints for project data
      let projectResponse;

      // Define all possible endpoints to try - always use /api prefix
      const projectEndpoints = ["/projects/" + cleanedProjectId];

      // If the ID has a prefix, also try without it
      if (cleanedProjectId.startsWith("project-")) {
        const idWithoutPrefix = cleanedProjectId.substring(8);
        projectEndpoints.push("/projects/" + idWithoutPrefix);
      }

      // Try each endpoint until one works
      let lastProjectError;
      for (const endpoint of projectEndpoints) {
        try {
          projectResponse = await apiClient.get<Project>(endpoint, {
            deduplicate: true, // Use API client's built-in deduplication
          });

          break; // Exit the loop if successful
        } catch (endpointError) {
          lastProjectError = endpointError;
          if (
            axios.isAxiosError(endpointError) &&
            endpointError.response?.status === 404
          ) {
            continue;
          } else {
            continue;
          }
        }
      }

      // If all endpoints failed, throw the last error
      if (!projectResponse) {
        throw lastProjectError;
      }

      // Set project data in state
      setProject(projectResponse.data);

      try {
        // Add a separate try-catch for images to handle 404 gracefully

        // Use our new API module to get project images
        const projectImages = await getProjectImages(cleanedProjectId);

        if (projectImages.length === 0) {
          setImages([]);

          // Don't show a toast for no images - the UI will show an empty state
          // This avoids duplicate messages
        } else {
          setImages(projectImages);
        }
      } catch (imageErr) {
        // Set empty images array
        setImages([]);

        toast.error("Could not load project images. Please try again later.", {
          duration: 5000,
          id: "image-fetch-error-notice",
        });
      }
    } catch (err: unknown) {
      setProject(null);
      setImages([]);

      // Store the original URL for debug info
      const requestUrl = axios.isAxiosError(err)
        ? err.config?.url || "unknown URL"
        : "unknown URL";

      let errorMessage = "Failed to load project data";
      if (axios.isAxiosError(err) && err.response) {
        const status = err.response.status;
        const responseData = err.response.data;

        errorMessage = responseData?.message || errorMessage;

        if (status === 404 || status === 403) {
          errorMessage =
            responseData?.message || "Project not found or access denied.";

          // Check if the project ID is a timestamp and it's in the future
          if (/^\d+$/.test(cleanedProjectId)) {
            const idAsNumber = parseInt(cleanedProjectId, 10);
            const now = Date.now();
            if (idAsNumber > now) {
              errorMessage = "Invalid project ID (future timestamp detected).";
            }
          }

          // Always show toast for permission errors
          toast.error(errorMessage, {
            duration: 5000,
            id: "project-error-" + cleanedProjectId,
          });

          // Store the failed ID in local storage to help with debugging
          try {
            localStorage.setItem("spheroseg_last_failed_id", projectId || "");
            localStorage.setItem(
              "spheroseg_last_failed_time",
              new Date().toISOString(),
            );
          } catch (_e) {
            // Ignore storage errors
          }

          // DISABLED: Navigation on 404 to prevent logout
          if (status === 404) {
            // Don't navigate away, just show the error message
            // This prevents the user from being logged out
          }
          return;
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [projectId, user?.id, navigate, cleanProjectId]);

  useEffect(() => {
    // Only fetch data if we have a valid projectId
    if (projectId) {
      fetchProjectData();
    } else {
      setLoading(false);
    }
  }, [fetchProjectData, projectId]);

  // Function to handle image status updates (defined before the effect that uses it)
  const updateImageStatus = useCallback(
    (
      imageId: string,
      status: ImageStatus,
      resultPath?: string | null,
      error?: string,
    ) => {
      // Immediately update all caches to prevent stale data issues
      const projectIdToUse = project?.id || projectId;
      if (projectIdToUse) {
        import("@/api/projectImages").then(({ updateImageStatusInCache }) => {
          updateImageStatusInCache(projectIdToUse, imageId, status, resultPath)
            .then(() => {})
            .catch((err) => {});
        });
      }

      setImages((prevImages) => {
        const updatedImages = prevImages.map((image) => {
          if (image.id === imageId) {
            const updatedImage = {
              ...image,
              segmentationStatus: status,
            };

            // Update segmentation result path if provided
            if (resultPath !== undefined) {
              updatedImage.segmentationResultPath = resultPath;
            }

            // Update error message if provided and status is failed
            if (status === "failed" && error) {
              updatedImage.error = error;
            }

            return updatedImage;
          }
          return image;
        });

        return updatedImages;
      });
    },
    [project?.id, projectId],
  );

  const refreshData = useCallback(() => {
    fetchProjectData();

    // When refreshing data, also check for any ongoing segmentations
    if (projectId) {
      const cleanedProjectId = cleanProjectId(projectId);

      // Try different API endpoints to get current segmentation status
      const endpoints = [
        "/segmentation/queue-status/" + cleanedProjectId,
        "/segmentation/queue-status/" + cleanedProjectId,
        "/segmentation/queue-status/" + cleanedProjectId,
      ];

      // Try each endpoint until one works
      (async () => {
        for (const endpoint of endpoints) {
          try {
            const response = await apiClient.get(endpoint);

            if (response.data) {
              // If we have images with processing status, update local state
              const images = (response.data as any).images || {};
              if (images.processing_count > 0) {
                // Fetch the specific list of images from the API again
                await fetchProjectData();
              }
              break;
            }
          } catch (_error) {
            // Continue to the next endpoint
          }
        }
      })();
    }
  }, [fetchProjectData, projectId, cleanProjectId]);

  // Listen for image-deleted events
  useEffect(() => {
    if (!projectId) {
      return;
    }

    // Clean the projectId to ensure we use just the UUID
    const cleanedProjectId = cleanProjectId(projectId);

    // Define the handler for image-deleted events
    const handleImageDeleted = (event: Event) => {
      const customEvent = event as CustomEvent<{
        imageId: string;
        projectId: string;
      }>;
      const { imageId, projectId: eventProjectId } = customEvent.detail;

      // Only handle events for this project
      if (eventProjectId === cleanedProjectId) {
        // Update the images state
        setImages((prevImages) =>
          prevImages.filter((img) => img.id !== imageId),
        );
      }
    };

    // Add event listeners
    window.addEventListener("image-deleted", handleImageDeleted);

    // Clean up
    return () => {
      window.removeEventListener("image-deleted", handleImageDeleted);
    };
  }, [projectId, cleanProjectId]);

  // Second event listener for image-status-update
  useEffect(() => {
    if (!projectId) {
      return;
    }

    // Define the handler for image-status-update events
    const handleImageStatusUpdate = (event: Event) => {
      const customEvent = event as CustomEvent<{
        imageId: string;
        status: ImageStatus;
        forceQueueUpdate?: boolean;
        error?: string;
        resultPath?: string | null;
      }>;
      const { imageId, status, error, resultPath } = customEvent.detail;

      // Update the image status directly
      updateImageStatus(imageId, status, resultPath, error);

      // Only refresh data for completed status to avoid infinite loops
      if (status === "completed") {
        // Delay refresh to ensure cache updates are processed
        setTimeout(() => {
          refreshData();
        }, 1000);
      }
    };

    // Add event listener
    window.addEventListener("image-status-update", handleImageStatusUpdate);

    // Clean up
    return () => {
      window.removeEventListener(
        "image-status-update",
        handleImageStatusUpdate,
      );
    };
  }, [projectId, updateImageStatus, refreshData]);

  // WebSocket connection and event handling
  useEffect(() => {
    if (!projectId) {
      return;
    }

    // Clean the projectId to ensure we use just the UUID
    const cleanedProjectId = cleanProjectId(projectId);

    // DISABLED: WebSocket connections to prevent API spam
    return;

    // WebSocket functionality has been disabled to prevent API spam
    // The original WebSocket code has been removed
    // WebSocket code was here but has been removed to prevent API spam
  }, [projectId, cleanProjectId]);

  return {
    project,
    projectTitle: project?.title || "",
    images,
    loading,
    error,
    refreshData,
    updateImageStatus,
    setImages,
  };
};
