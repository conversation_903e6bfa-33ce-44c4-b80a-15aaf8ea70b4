/**
 * Unified Error Handler for Frontend
 * 
 * Integrates with the shared unified error system and provides frontend-specific
 * error handling functionality like toast notifications and error reporting.
 */

import { toast } from 'sonner';
import axios from 'axios';
import {
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  ErrorDetails,
  UnifiedErrorInfo,
  ErrorResponse,
  createErrorInfo,
  mapStatusToErrorType,
  getErrorSeverity,
  ERROR_MESSAGES,
  isErrorResponse,
  fromError,
  fromHttpError
} from '@spheroseg/shared/src/utils/unifiedErrorSystem';

// Import error monitoring service if available
let errorMonitoringService: any = null;
try {
  import('@/services/errorMonitoringService').then(module => {
    errorMonitoringService = module.errorMonitoringService;
  }).catch(() => {
    // Service not available, continue without it
  });
} catch {
  // Service not available, continue without it
}

// Lazy load logger to avoid circular dependency
let logger: any = null;
const getLogger = () => {
  if (!logger) {
    // Use console as fallback until logger is loaded
    logger = {
      error: (...args: any[]) => console.error('[UnifiedErrorHandler]', ...args),
      warn: (...args: any[]) => console.warn('[UnifiedErrorHandler]', ...args),
      info: (...args: any[]) => console.info('[UnifiedErrorHandler]', ...args),
      debug: (...args: any[]) => console.debug('[UnifiedErrorHandler]', ...args),
    };
    
    // Async load the real logger
    import('@/utils/logging/unifiedLogger').then(module => {
      logger = module.default;
    }).catch(() => {
      // Keep console fallback if logger is not available
    });
  }
  return logger;
};

// ===========================
// Frontend-Specific Error Classes
// ===========================

export class FrontendError extends Error {
  public readonly type: ErrorType;
  public readonly severity: ErrorSeverity;
  public readonly code: ErrorCode;
  public readonly statusCode?: number;
  public readonly details?: ErrorDetails[];
  public readonly timestamp: string;
  public readonly context?: Record<string, unknown>;

  constructor(
    type: ErrorType,
    message: string,
    options: {
      code?: ErrorCode;
      statusCode?: number;
      details?: ErrorDetails[];
      context?: Record<string, unknown>;
    } = {}
  ) {
    super(message);
    this.name = 'FrontendError';
    this.type = type;
    this.severity = getErrorSeverity(type);
    this.code = options.code || this._getDefaultCode(type);
    this.statusCode = options.statusCode;
    this.details = options.details;
    this.timestamp = new Date().toISOString();
    this.context = options.context;
    Error.captureStackTrace(this, FrontendError);
  }

  private _getDefaultCode(type: ErrorType): ErrorCode {
    switch (type) {
      case ErrorType.VALIDATION: return ErrorCode.VALIDATION_ERROR;
      case ErrorType.AUTHENTICATION: return ErrorCode.AUTHENTICATION_REQUIRED;
      case ErrorType.AUTHORIZATION: return ErrorCode.INSUFFICIENT_PERMISSIONS;
      case ErrorType.NOT_FOUND: return ErrorCode.RESOURCE_NOT_FOUND;
      case ErrorType.NETWORK: return ErrorCode.NETWORK_ERROR;
      case ErrorType.TIMEOUT: return ErrorCode.TIMEOUT_ERROR;
      case ErrorType.IMAGE_OPERATION: return ErrorCode.IMAGE_PROCESSING_ERROR;
      case ErrorType.PROJECT_OPERATION: return ErrorCode.PROJECT_ACCESS_ERROR;
      case ErrorType.SEGMENTATION_OPERATION: return ErrorCode.SEGMENTATION_ERROR;
      default: return ErrorCode.INTERNAL_SERVER_ERROR;
    }
  }

  toUnifiedErrorInfo(): UnifiedErrorInfo {
    return createErrorInfo(this.type, this.message, {
      code: this.code,
      statusCode: this.statusCode,
      details: this.details,
      context: this.context,
      stack: this.stack
    });
  }
}

// ===========================
// Error Detection and Conversion
// ===========================

/**
 * Convert various error types to UnifiedErrorInfo
 */
export function normalizeError(error: unknown, context?: Record<string, unknown>): UnifiedErrorInfo {
  // Already a UnifiedErrorInfo
  if (error && typeof error === 'object' && 'type' in error && 'severity' in error) {
    return error as UnifiedErrorInfo;
  }

  // FrontendError
  if (error instanceof FrontendError) {
    return error.toUnifiedErrorInfo();
  }

  // Axios error
  if (axios.isAxiosError(error)) {
    const response = error.response;
    
    // Check if response contains a unified error format
    if (response?.data && isErrorResponse(response.data)) {
      const errorData = response.data as ErrorResponse;
      return createErrorInfo(
        mapStatusToErrorType(response.status),
        errorData.message,
        {
          code: errorData.error,
          statusCode: response.status,
          details: errorData.details,
          context: { ...context, requestId: errorData.requestId }
        }
      );
    }

    // Standard HTTP error
    const message = response?.data?.message || response?.data?.error || error.message;
    return fromHttpError(
      response?.status || 500,
      message || ERROR_MESSAGES.NETWORK_ERROR,
      undefined,
      context
    );
  }

  // Standard Error
  if (error instanceof Error) {
    return fromError(error, ErrorType.UNKNOWN, context);
  }

  // String error
  if (typeof error === 'string') {
    return createErrorInfo(ErrorType.UNKNOWN, error, { context });
  }

  // Unknown error type
  return createErrorInfo(ErrorType.UNKNOWN, ERROR_MESSAGES.UNKNOWN_ERROR, { 
    context: { ...context, originalError: error }
  });
}

// ===========================
// Toast Notification System
// ===========================

const toastIds = new Map<string, string>();
const accessDeniedTimestamps = new Map<string, number>();
const ACCESS_DENIED_SUPPRESSION_WINDOW = 30000; // 30 seconds

/**
 * Check if a notification should be suppressed based on recent access denied errors
 */
function shouldSuppressNotification(errorInfo: UnifiedErrorInfo): boolean {
  const now = Date.now();

  // Clean up old timestamps
  for (const [key, timestamp] of accessDeniedTimestamps.entries()) {
    if (now - timestamp > ACCESS_DENIED_SUPPRESSION_WINDOW) {
      accessDeniedTimestamps.delete(key);
    }
  }

  // Check if this is an access denied error
  const isAccessDenied =
    errorInfo.type === ErrorType.AUTHORIZATION ||
    (errorInfo.type === ErrorType.INTERNAL_SERVER && errorInfo.message.toLowerCase().includes('permission')) ||
    errorInfo.message.toLowerCase().includes('access denied') ||
    errorInfo.message.toLowerCase().includes('do not have permission');

  if (isAccessDenied) {
    const contextKey = extractContextKey(errorInfo);
    accessDeniedTimestamps.set(contextKey, now);
    getLogger().debug('Tracked access denied error', { contextKey, message: errorInfo.message });
    return false; // Don't suppress the access denied error itself
  }

  // Check if this is a secondary error that should be suppressed
  const isSecondaryError =
    errorInfo.type === ErrorType.NOT_FOUND ||
    (errorInfo.severity === ErrorSeverity.WARNING && 
     errorInfo.context && 
     Object.keys(errorInfo.context).some(key => key.includes('segmentation')));

  if (isSecondaryError && accessDeniedTimestamps.size > 0) {
    const contextKey = extractContextKey(errorInfo);
    
    for (const [key, timestamp] of accessDeniedTimestamps.entries()) {
      if (now - timestamp <= ACCESS_DENIED_SUPPRESSION_WINDOW) {
        if (areContextsRelated(key, contextKey)) {
          getLogger().debug('Suppressing secondary notification', {
            suppressedError: errorInfo.message,
            relatedAccessDeniedKey: key,
            contextKey
          });
          return true;
        }
      }
    }
  }

  return false;
}

function extractContextKey(errorInfo: UnifiedErrorInfo): string {
  const message = errorInfo.message.toLowerCase();
  const context = JSON.stringify(errorInfo.context || {}).toLowerCase();

  if (message.includes('image') || context.includes('image')) {
    return 'image-operation';
  } else if (message.includes('project') || context.includes('project')) {
    return 'project-operation';
  } else if (message.includes('segmentation') || context.includes('segmentation')) {
    return 'segmentation-operation';
  }

  return errorInfo.type;
}

function areContextsRelated(key1: string, key2: string): boolean {
  if (key1 === key2) return true;

  const relatedContexts = [
    ['image-operation', 'segmentation-operation'],
    ['project-operation', 'image-operation'],
  ];

  return relatedContexts.some(([a, b]) => 
    (key1 === a && key2 === b) || (key1 === b && key2 === a)
  );
}

/**
 * Show error toast with deduplication
 */
function showErrorToast(errorInfo: UnifiedErrorInfo): void {
  getLogger().debug('showErrorToast called', {
    type: errorInfo.type,
    severity: errorInfo.severity,
    message: errorInfo.message,
  });

  // Check if we should suppress this notification
  if (shouldSuppressNotification(errorInfo)) {
    getLogger().debug('Toast suppressed by shouldSuppressNotification');
    return;
  }

  const toastKey = `${errorInfo.type}-${errorInfo.code}`;

  // Check if similar toast is already shown
  if (toastIds.has(toastKey)) {
    getLogger().debug('Toast suppressed - already shown', { toastKey });
    return;
  }

  let duration = 4000; // Default duration

  // Adjust duration based on severity
  switch (errorInfo.severity) {
    case ErrorSeverity.INFO:
      duration = 3000;
      break;
    case ErrorSeverity.WARNING:
      duration = 3500;
      break;
    case ErrorSeverity.ERROR:
      duration = 4000;
      break;
    case ErrorSeverity.CRITICAL:
      duration = 5000;
      break;
  }

  // Show toast based on severity
  const toastFn =
    errorInfo.severity === ErrorSeverity.INFO
      ? toast.info
      : errorInfo.severity === ErrorSeverity.WARNING
        ? toast.warning
        : toast.error;

  const toastId = toastFn(errorInfo.message, {
    duration,
    id: toastKey,
  });

  // Track toast ID
  toastIds.set(toastKey, toastId as string);

  // Remove from tracking after duration
  setTimeout(() => {
    toastIds.delete(toastKey);
  }, duration);
}

// ===========================
// Main Error Handler
// ===========================

export interface HandleErrorOptions {
  showToast?: boolean;
  logError?: boolean;
  context?: Record<string, unknown>;
  customMessage?: string;
}

/**
 * Main unified error handler for frontend
 */
export function handleError(
  error: unknown,
  options: HandleErrorOptions = {}
): UnifiedErrorInfo {
  const { showToast = true, logError = true, context, customMessage } = options;

  // Normalize error to unified format
  const errorInfo = normalizeError(error, context);

  // Override message if custom message provided
  if (customMessage) {
    errorInfo.message = customMessage;
  }

  // Log error based on severity
  if (logError) {
    const logData = {
      type: errorInfo.type,
      code: errorInfo.code,
      message: errorInfo.message,
      statusCode: errorInfo.statusCode,
      details: errorInfo.details,
      context: errorInfo.context,
      stack: errorInfo.stack,
    };

    switch (errorInfo.severity) {
      case ErrorSeverity.INFO:
        getLogger().info('Error handled', logData);
        break;
      case ErrorSeverity.WARNING:
        getLogger().warn('Warning handled', logData);
        break;
      case ErrorSeverity.ERROR:
        getLogger().error('Error handled', logData);
        break;
      case ErrorSeverity.CRITICAL:
        getLogger().error('Critical error handled', logData);
        break;
    }
  }

  // Show toast notification
  if (showToast) {
    showErrorToast(errorInfo);
  }

  // Dispatch custom event for global error handling
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('app:error', { detail: errorInfo }));
  }

  // Report error to monitoring service
  try {
    if (errorMonitoringService) {
      errorMonitoringService.reportError(errorInfo);
    }
  } catch (monitoringError) {
    getLogger().warn('Failed to report error to monitoring service', { monitoringError });
  }

  return errorInfo;
}

// ===========================
// Utility Functions
// ===========================

/**
 * Safe async function wrapper
 */
export async function safeAsync<T>(
  fn: () => Promise<T>,
  options?: HandleErrorOptions
): Promise<{ data?: T; error?: UnifiedErrorInfo }> {
  try {
    const data = await fn();
    return { data };
  } catch (error) {
    const errorInfo = handleError(error, options);
    return { error: errorInfo };
  }
}

/**
 * Try-catch wrapper for async functions
 */
export async function tryCatch<T>(
  fn: () => Promise<T>, 
  defaultValue?: T
): Promise<T | undefined> {
  try {
    return await fn();
  } catch (error) {
    handleError(error, { showToast: false });
    return defaultValue;
  }
}

/**
 * Format validation errors for display
 */
export function formatValidationErrors(errors: Record<string, string[]>): string {
  return Object.entries(errors)
    .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
    .join('\n');
}

/**
 * Check if error is of specific type
 */
export function isErrorType(error: unknown, type: ErrorType): boolean {
  const errorInfo = normalizeError(error);
  return errorInfo.type === type;
}

/**
 * Check if error is authentication related
 */
export function isAuthError(error: unknown): boolean {
  const errorInfo = normalizeError(error);
  return errorInfo.type === ErrorType.AUTHENTICATION || errorInfo.type === ErrorType.AUTHORIZATION;
}

/**
 * Clear access denied suppression cache
 */
export function clearAccessDeniedSuppression(): void {
  accessDeniedTimestamps.clear();
}

// ===========================
// Re-exports from shared system
// ===========================

export {
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  ErrorDetails,
  UnifiedErrorInfo,
  ErrorResponse,
  ERROR_MESSAGES
} from '@spheroseg/shared/src/utils/unifiedErrorSystem';

// ===========================
// Default Export
// ===========================

export default {
  handleError,
  safeAsync,
  tryCatch,
  normalizeError,
  formatValidationErrors,
  isErrorType,
  isAuthError,
  clearAccessDeniedSuppression,
  FrontendError,
  
  // Re-export shared components
  ErrorType,
  ErrorSeverity,
  ErrorCode,
  ERROR_MESSAGES
};