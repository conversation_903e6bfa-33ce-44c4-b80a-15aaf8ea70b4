import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to flatten nested object
function flattenObject(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? prefix + '.' + k : k;
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k], pre));
    } else {
      acc[pre] = obj[k];
    }
    return acc;
  }, {});
}

// Load translations
const enPath = path.join(__dirname, '..', 'translations', 'en.ts');
const zhPath = path.join(__dirname, '..', 'translations', 'zh.ts');

const enContent = fs.readFileSync(enPath, 'utf-8');
const zhContent = fs.readFileSync(zhPath, 'utf-8');

// Extract default export
const enMatch = enContent.match(/export\s+default\s+(\{[\s\S]*\});?\s*$/);
const zhMatch = zhContent.match(/export\s+default\s+(\{[\s\S]*\});?\s*$/);

if (!enMatch || !zhMatch) {
  console.error('Could not find translations in files');
  process.exit(1);
}

// Parse translations safely without using Function() or eval()
function safeParseJSObject(objString) {
  try {
    // First try to parse as JSON (won't work for JS objects with unquoted keys)
    return JSON.parse(objString);
  } catch {
    // Convert JS object literal to valid JSON
    const jsonString = objString
      .trim()
      // Remove any trailing comma before closing brace/bracket
      .replace(/,(\s*[}\]])/g, '$1')
      // Replace single quotes with double quotes
      .replace(/'/g, '"')
      // Add quotes around unquoted keys (basic pattern)
      .replace(/([{,]\s*)([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:/g, '$1"$2":')
      // Fix double-quoted keys that were already quoted
      .replace(/""([a-zA-Z_$][a-zA-Z0-9_$]*)"":/g, '"$1":');
    
    try {
      return JSON.parse(jsonString);
    } catch (e) {
      console.error('Failed to parse translation object:', e.message);
      console.error('Original string:', objString.substring(0, 200) + '...');
      console.error('Processed string:', jsonString.substring(0, 200) + '...');
      throw new Error(`Unable to safely parse translation object: ${e.message}`);
    }
  }
}

const enTranslations = safeParseJSObject(enMatch[1]);
const zhTranslations = safeParseJSObject(zhMatch[1]);

// Flatten objects
const enFlat = flattenObject(enTranslations);
const zhFlat = flattenObject(zhTranslations);

// Find missing keys
const enKeys = Object.keys(enFlat);
const zhKeys = Object.keys(zhFlat);

const missing = enKeys.filter((key) => !zhKeys.includes(key));

console.log(`Found ${missing.length} missing keys in zh.ts\n`);

// Group by section
const sections = {};
missing.forEach((key) => {
  const section = key.split('.')[0];
  if (!sections[section]) sections[section] = [];
  sections[section].push(key);
});

Object.entries(sections).forEach(([section, keys]) => {
  console.log(`\n${section} (${keys.length} keys):`);
  keys.slice(0, 10).forEach((key) => console.log(`  - ${key}`));
  if (keys.length > 10) console.log(`  ... and ${keys.length - 10} more`);
});

// Output all missing keys for reference
console.log('\n\nAll missing keys with values:\n');
missing.forEach((key) => {
  const enValue = enFlat[key];
  console.log(`${key}: "${enValue}"`);
});
