{
  // Frontend uses its own TypeScript config due to Docker container path differences
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "jsxImportSource": "react",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@shared/*": ["../shared/src/*"],
      "@spheroseg/shared": ["../shared/src"],
      "@spheroseg/types": ["../types/src"]
    },
    "types": ["vitest/globals", "node"],
    "skipLibCheck": true,
    // Gradual strict mode migration - enable one by one
    "strict": false,
    "noImplicitAny": true
  },
  "include": ["src"],
  "exclude": ["node_modules", "dist", "**/*.test.tsx", "**/*.spec.tsx"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
