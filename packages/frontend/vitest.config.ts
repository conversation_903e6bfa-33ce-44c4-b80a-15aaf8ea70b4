import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts', './src/test-setup.ts'],
    include: [
      'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'src/**/__tests__/**/*.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      '.turbo',
      'coverage',
      '**/*.d.ts',
      '**/e2e/**',
      '**/*.e2e.spec.ts',
      '**/*.playwright.spec.ts',
      '**/*.visual.spec.ts'
    ],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov', 'json', 'json-summary', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        '**/*.d.ts',
        '**/*.test.{ts,tsx,js,jsx}',
        '**/*.spec.{ts,tsx,js,jsx}',
        '**/__tests__/**',
        '**/test-utils/**',
        'src/setupTests.ts',
        'src/test-setup.ts',
        'src/vite-env.d.ts',
        'src/main.tsx',
        '**/*.config.{js,ts}',
        '**/index.ts' // Exclude barrel exports
      ],
      thresholds: {
        branches: 75,
        functions: 80, 
        lines: 85,
        statements: 85
      }
    },
    // Mock configuration
    mockReset: true,
    clearMocks: true,
    restoreMocks: true,
    // Reporter configuration
    reporters: ['default', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/index.html'
    },
    // Test environment configuration
    env: {
      VITE_APP_VERSION: '1.0.0',
      VITE_API_URL: 'http://localhost:5001',
      VITE_API_BASE_URL: '/api',
      VITE_ENABLE_REGISTRATION: 'true',
      VITE_ENABLE_GOOGLE_AUTH: 'false',
      VITE_ENABLE_GITHUB_AUTH: 'true',
      VITE_ENABLE_EXPERIMENTAL: 'false',
      VITE_MAINTENANCE_MODE: 'false',
      VITE_ANALYTICS_ENABLED: 'true',
      VITE_GA_ID: 'UA-123456-1',
      VITE_SENTRY_DSN: 'https://<EMAIL>/123',
      VITE_ENABLE_LOGGING: 'true',
      VITE_LOG_LEVEL: 'debug',
    },
    // CSS configuration
    css: {
      modules: {
        classNameStrategy: 'non-scoped'
      }
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@shared': path.resolve(__dirname, '../shared/src'),
      '@spheroseg/shared': path.resolve(__dirname, '../shared/src'),
      '@spheroseg/types': path.resolve(__dirname, '../types/src')
    }
  }
});