# SpheroSeg TODO List

## 🔴 CRITICAL - Production Issues (Immediate)

### 🚨 ARCHITECTURE CLEANUP - MESSY CODE ELIMINATION (NEW - HIGHEST PRIORITY)

#### Server Implementation Chaos (IMMEDIATE - 2 hours)
- [ ] **Delete 3 of 4 server implementations** - Keep only `server-production-modular.ts`
  - [ ] Remove `packages/backend/src/server.ts` (legacy)
  - [ ] Remove `packages/backend/src/index.ts` (duplicate entry point)
  - [ ] Remove `packages/backend/src/server-production-simple.ts` (1,344 lines monolith)
  - [ ] Update Docker configuration to use modular server only
  - [ ] Update package.json scripts to point to correct server

#### Middleware Duplicity Hell (IMMEDIATE - 4 hours)
- [ ] **Consolidate 5 performance middleware into 1** (38,000+ lines → ~5,000 lines)
  - [ ] Remove `performanceMiddleware.ts` (4,873 lines)
  - [ ] Remove `performanceMonitoring.ts` (15,819 lines)
  - [ ] Remove `performanceMonitoringMiddleware.ts` (1,370 lines)
  - [ ] Remove `performanceTracking.ts` (4,331 lines)
  - [ ] Keep only `performance.ts` (12,616 lines) and refactor
  - [ ] Update all imports to use single middleware

#### Email Service Stub Elimination (CRITICAL - 8 hours)
- [ ] **Replace 3 stub email services with 1 working implementation**
  - [ ] Remove `emailService.ts` (stub)
  - [ ] Remove `unifiedEmailService.ts` (stub)
  - [ ] Remove `emailServiceV2.ts` (stub)
  - [ ] Implement real SMTP email service
  - [ ] Test password reset emails
  - [ ] Test project sharing emails
  - [ ] Test registration verification emails

### Infrastructure & Deployment
- [x] ~~Fix Prisma Client Error - Resolve query engine not found~~ ✅ DONE (2025-08-09)
- [x] ~~Fix TypeScript build errors~~ ✅ DONE (2025-08-09)
- [x] ~~Implement HTTPS~~ ✅ DONE (2025-08-09)
- [x] ~~Fix Backend Health Checks~~ ✅ DONE (2025-08-09)
- [x] ~~Optimize Database Connection Pool~~ ✅ DONE (2025-08-09)
- [x] ~~Fix JWT key loading for tests~~ ✅ DONE (2025-08-09)
- [x] ~~Create CI/CD Pipeline~~ ✅ DONE (2025-08-09)
- [x] ~~Create deployment documentation~~ ✅ DONE (2025-08-09)
- [ ] **Memory Optimization** - Increase backend heap from 768MB if needed

### Testing Infrastructure
- [x] ~~Fix backend tests deprecated query() method~~ ✅ DONE (2025-08-09)
- [x] ~~Create frontend unit tests~~ ✅ DONE (2025-08-09)
- [x] ~~Create E2E tests with Playwright~~ ✅ DONE (2025-08-09)
- [x] ~~Create API integration tests~~ ✅ DONE (2025-08-09)

## 🟠 HIGH PRIORITY - Code Cleanup & Simplification (Week 1)

### Frontend Duplicity Cleanup (2 hours)
- [x] **Component Duplicity Elimination** ✅ DONE (2025-08-09)
  - [x] ~~Remove duplicate StatsOverview.tsx~~ ✅ Removed
  - [x] ~~Verified ErrorBoundary/AppErrorBoundary are complementary~~ ✅ Correct architecture
  - [x] ~~Clean components directory~~ ✅ SSOT achieved

### Frontend SSOT Consolidation (8 hours) - VERIFIED DUPLICATIONS
- [ ] **Duplicate Request De-duplication** (P0 - Critical)
  - [ ] Remove `requestDeduplicator.ts` usage in segmentation
  - [ ] Use API client's built-in deduplication everywhere
  - [ ] Delete `/utils/requestDeduplicator.ts` after migration

- [ ] **Multiple Debounce/Throttle Implementations** (P1 - High)
  - [ ] Consolidate 4 implementations into 1 canonical
  - [ ] Keep `/utils/debounce.ts` as the single source
  - [ ] Update `/utils/performance.ts` to import from debounce
  - [ ] Update `/hooks/useDebounce.ts` to use unified utility
  - [ ] Remove duplicate from `/utils/performanceOptimizations.ts`

- [ ] **Segmentation Module Mixing Concerns** (P1 - High)
  - [ ] Extract URL normalization (`processImageUrl`) to utility
  - [ ] Move to `/utils/urlNormalization.ts`
  - [ ] Keep API hooks focused on data fetching only
  - [ ] Use typed endpoints from `/services/api/endpoints.ts`

- [ ] **Polygon Utils Placeholder** (P2 - Medium)
  - [ ] Replace placeholder in `useOptimizedSegmentation.ts`
  - [ ] Import from `@spheroseg/shared/utils/polygonUtils`

- [ ] **Logging Inconsistencies** (P2 - Medium)
  - [ ] Standardize on `@/utils/logging/unifiedLogger` imports
  - [ ] Update all files using `@/lib/logger`
  - [ ] Use consistent `getLogger('namespace')` pattern

- [ ] **Remove Deprecated Utilities** (P2 - Medium)
  - [ ] Find all imports of deprecated `errorUtils.ts`
  - [ ] Update to use `@/utils/error/unifiedErrorHandler`
  - [ ] Delete deprecated file after migration

### Route System Unification (3 hours)
- [ ] **Consolidate Backend Routing Systems**
  - [ ] Remove monolithic routes from server files
  - [ ] Keep only modular routes in `/routes` directory
  - [ ] Remove duplicate route definitions
  - [ ] Update all route imports

### Type Definition Consolidation (3 hours)
- [ ] **Eliminate Type Duplications**
  - [ ] Move all shared types to `packages/types/src/`
  - [ ] Remove duplicate types from `packages/backend/src/types/`
  - [ ] Remove duplicate types from `packages/frontend/src/types/`
  - [ ] Update all imports to use shared types

### Testing System Streamlining (4 hours)
- [ ] **Consolidate Test Utilities**
  - [ ] Decide between Jest vs Vitest (remove one)
  - [ ] Consolidate test utilities into single file
  - [ ] Remove duplicate mock files
  - [ ] Standardize test setup across packages

## 🟠 HIGH PRIORITY - Core Functionality (Week 1-2)

### Email Service (AFTER CLEANUP)
- [ ] **Fix email service for Request Access** (depends on email service implementation)
- [ ] **Fix email service for Password Reset** (depends on email service implementation)
- [ ] **Fix email service for Project Sharing** (depends on email service implementation)
- [ ] **Implement email verification on registration** (depends on email service implementation)

### Segmentation Features
- [ ] **Fix segmentation thumbnails generation**
- [ ] **Fix segmentation thumbnails display**
- [ ] **Implement real-time segmentation progress display**
- [ ] **Add auto-save in segmentation editor**
- [ ] **Import segmentations - JSON format**
- [ ] **Import segmentations - COCO format**
- [ ] **Import segmentations - Binary masks**
- [ ] **Import segmentations - CSV coordinates**

### UI/UX Fixes
- [ ] **Fix password reset form visibility** - Input fields hard to see
- [ ] **Fix documentation page duplicate header**
- [ ] **Fix documentation scroll navigation**
- [ ] **Keep polygon selected after adding points in editor**
- [ ] **Add legend for red/blue polygons in editor**
- [ ] **Preserve image sort order when returning to project**
- [ ] **Add documentation link after login** (not just on login page)

## 🟡 MEDIUM PRIORITY - Enhancements (Week 3-4)

### ML & Processing
- [ ] **Implement multiple ML models support**
- [ ] **Add ML model selection in Settings**
- [ ] **Configure model parameters (confidence threshold)**
- [ ] **Implement per-user segmentation queues**
- [ ] **Add resource limits and quotas**
- [ ] **Create priority queue system**
- [ ] **Batch processing improvements**

### Architecture & Code Quality
- [x] ~~**Decide: Monolithic vs Modular architecture**~~ ✅ DECIDED: Modular (cleanup in progress)
- [ ] **Complete refactor to modular architecture** (after cleanup)
- [ ] **Remove dead code** (in progress with cleanup)
- [ ] **Optimize bundle size** (<400KB target)

### Testing Infrastructure (Target: >90% Coverage)
- [ ] **Setup Jest for unit tests** (after test system streamlining)
- [ ] **Setup Playwright for E2E tests** (after test system streamlining)
- [ ] **Achieve 50% test coverage** (Milestone 1)
- [ ] **Achieve 70% test coverage** (Milestone 2)
- [ ] **Achieve 80% test coverage** (Milestone 3)
- [ ] **Achieve >90% test coverage** (Final target)
- [ ] **Setup CI/CD with test automation**
- [ ] **Add visual regression tests**
- [ ] **Implement load testing with k6**
- [ ] **Add security testing suite**
- [ ] **Setup mutation testing**

## 🟢 LOW PRIORITY - Nice to Have (Week 5+)

### Documentation
- [ ] **Update segmentation editor guide**
- [ ] **Document ML models usage**
- [ ] **Document project sharing features**
- [ ] **Create API documentation (OpenAPI/Swagger)**
- [ ] **Create developer documentation site**
- [ ] **group all documentation to /docs/**

### Localization & Legal
- [ ] **Review all localization keys** (after translation cleanup)
- [ ] **Add Terms of Use page**
- [ ] **Add citation requirement for publications**

### Advanced Features
- [ ] **Dark mode support**
- [ ] **Mobile responsive design**
- [ ] **Keyboard shortcuts**
- [ ] **Collaboration features**
- [ ] **Advanced annotation tools**
- [ ] **Model comparison features**
- [ ] **Webhook integration**
- [ ] **Export templates**

### DevOps & Monitoring
- [ ] **Setup Prometheus + Grafana**
- [ ] **Implement log aggregation (ELK)**
- [ ] **Setup automated backups**
- [ ] **Configure CDN for assets**
- [ ] **Implement service worker for offline support**

## 📊 Progress Tracking

### Completed Tasks
1. ✅ Development environment setup (2025-08-09)
2. ✅ Fixed ~300 TypeScript errors (2025-08-09)
3. ✅ Database migrations applied (2025-08-09)
4. ✅ Prisma client generated (2025-08-09)
5. ✅ Created DEVELOPMENT_SETUP.md documentation (2025-08-09)
6. ✅ Converted roadmap.md to TODO.md format (2025-08-09)
7. ✅ Fixed Prisma client import paths (2025-08-09)
8. ✅ Fixed majority of TypeScript type errors in controllers and services (2025-08-09)

### Current Sprint (Active) - ARCHITECTURE CLEANUP PHASE
- 🔄 **Server Implementation Cleanup** (Day 1 - 2 hours)
- 🔄 **Middleware Consolidation** (Day 2 - 4 hours)
- 🔄 **Email Service Implementation** (Day 3-4 - 8 hours)
- 🔄 **Frontend Duplicity Cleanup** (Day 5 - 4 hours)
- **Estimated Total**: 18 hours for dramatic simplification

### Blocked Items
- ✅ Production deployment - builds are now fixed and ready
- ⚠️ Email functionality - blocked by stub implementations (FIXING NOW)
- ⚠️ Clean architecture - blocked by duplications (FIXING NOW)

## 📈 Metrics & Goals

### Cleanup Targets (Week 1)
| Area | Current | Target | Reduction |
|------|---------|--------|-----------|
| Server Files | 4 files | 1 file | 75% |
| Middleware | 38,000+ lines | ~5,000 lines | 87% |
| Email Services | 3 stubs | 1 working | 67% + functionality |
| Translation Files | 5 files | 2 files | 60% |
| Overall Code | ~100,000 lines | ~50,000 lines | 50% |

### Test Coverage Targets
| Package | Current | Target | Deadline |
|---------|---------|--------|----------|
| Backend | ~15% | 95% | Week 8 |
| Frontend | ~10% | 90% | Week 8 |
| Shared | ~20% | 100% | Week 8 |
| Overall | ~15% | >90% | Week 8 |

### Performance Targets
- API Response: <100ms (p50), <500ms (p99)
- Frontend Load: <3s Time to Interactive
- Build Time: <5 minutes (should improve after cleanup)
- CI/CD Pipeline: <15 minutes

### Quality Metrics
- Zero console errors
- Technical debt <5% (should improve dramatically)
- Bug detection >90% before production
- 99.9% uptime

## 🔄 Daily Checklist
- [ ] Check production health status
- [ ] Review error logs
- [ ] Check test coverage trend
- [ ] Update completed tasks
- [ ] Plan next day priorities
- [ ] **NEW**: Track cleanup progress (files deleted, lines reduced)

## 📝 Notes

### Known Issues
- **CRITICAL**: 4 different server implementations causing confusion
- **CRITICAL**: 38,000+ lines of duplicate middleware code
- **CRITICAL**: All email services are non-functional stubs
- Backend container port conflicts with local development
- Docker health checks failing
- TypeScript build errors in controllers

### Cleanup Benefits Expected
- **50% code reduction** (~50,000 lines eliminated)
- **87% middleware simplification** (38,000 → 5,000 lines)
- **Functional email system** (replacing 3 stubs)
- **Clear architecture** (single server, unified routing)
- **Faster builds** (less code to compile)
- **Easier maintenance** (no more "which file is used?" questions)

### Dependencies to Update
- Check for security vulnerabilities
- Update outdated packages
- Review license compliance

### Team Communication
- Update status in daily standup
- Document blockers immediately
- Request code reviews promptly
- **NEW**: Communicate cleanup progress and any breaking changes

---

*Last Updated: 2025-08-09*
*Next Review: End of Day*
*Version: 1.1.0 - CLEANUP PHASE*
