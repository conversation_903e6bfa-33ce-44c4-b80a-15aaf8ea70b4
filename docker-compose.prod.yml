# Docker Compose for SpheroSeg Production

services:
  # Database
  db:
    image: postgres:14-alpine
    container_name: spheroseg-db-prod
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # Redis
  redis:
    image: redis:7-alpine
    container_name: spheroseg-redis-prod
    restart: always
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - spheroseg-network

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management-alpine
    container_name: spheroseg-rabbitmq-prod
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD}
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "-q", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - spheroseg-network

  # Backend
  backend:
    build:
      context: .
      dockerfile: packages/backend/Dockerfile.production
    container_name: spheroseg-backend-prod
    restart: always
    ports:
      - "5001:5001"
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@db:5432/${POSTGRES_DB}
      REDIS_URL: redis://redis:6379
      RABBITMQ_URL: amqp://${RABBITMQ_USER}:${RABBITMQ_PASSWORD}@rabbitmq:5672
      ML_API_URL: http://ml:5002
      ML_SERVICE_API_KEY: ${ML_SERVICE_API_KEY}
      JWT_SECRET: ${JWT_SECRET}
      SESSION_SECRET: ${SESSION_SECRET}
      CSRF_SECRET: ${CSRF_SECRET}
      NODE_OPTIONS: --max-old-space-size=768
      UPLOAD_DIR: /app/uploads
      DB_PASSWORD: ${POSTGRES_PASSWORD}
      # Email configuration
      EMAIL_HOST: ${EMAIL_HOST:-mail.utia.cas.cz}
      EMAIL_PORT: ${EMAIL_PORT:-25}
      EMAIL_SECURE: ${EMAIL_SECURE:-false}
      EMAIL_USER: ${EMAIL_USER:-<EMAIL>}
      EMAIL_PASS: ${EMAIL_PASS}
      EMAIL_FROM: ${EMAIL_FROM:-<EMAIL>}
      APP_URL: ${APP_URL:-https://spherosegapp.utia.cas.cz}
      CORS_ORIGIN: ${CORS_ORIGIN:-https://spherosegapp.utia.cas.cz,http://localhost:3000,http://localhost}
    volumes:
      - ./uploads:/app/uploads
      - ./scripts:/app/scripts:ro
      - ./packages/backend/scripts:/app/packages/backend/scripts:ro
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "sh /app/packages/backend/scripts/verify-prisma.sh && curl -f http://localhost:5001/api/health"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s
    networks:
      - spheroseg-network
    extra_hosts:
      - "mail.utia.cas.cz:************"

  # ML Service
  ml:
    build:
      context: ./packages/ml
      dockerfile: Dockerfile
    container_name: spheroseg-ml-prod
    restart: always
    ports:
      - "5002:5002"
    environment:
      PYTHONUNBUFFERED: 1
      MODEL_PATH: /ML/checkpoint_epoch_9.pth.tar
      DEBUG: "false"
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: ${RABBITMQ_USER}
      RABBITMQ_PASS: ${RABBITMQ_PASSWORD}
      RABBITMQ_QUEUE: segmentation_tasks
      RABBITMQ_PREFETCH_COUNT: 4
      ML_SERVICE_API_KEY: ${ML_SERVICE_API_KEY}
    volumes:
      - ./packages/ml:/ML
      - ./uploads:/ML/uploads
    depends_on:
      rabbitmq:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5002/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    deploy:
      resources:
        limits:
          memory: 4096M
        reservations:
          memory: 2048M
    networks:
      - spheroseg-network

  # Frontend (Nginx serving static files - HTTPS enabled)
  frontend:
    image: nginx:alpine
    container_name: spheroseg-frontend-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./packages/frontend/dist:/usr/share/nginx/html
      - ./packages/frontend/nginx-https.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_cache:/var/cache/nginx
    depends_on:
      backend:
        condition: service_healthy
    environment:
      - NGINX_SSL_ENABLED=true
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - spheroseg-network

volumes:
  postgres_data_prod:
  nginx_cache:

networks:
  spheroseg-network:
    driver: bridge