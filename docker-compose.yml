services:

  # Nginx for development environment
  nginx-dev:
    profiles: ["dev"]
    container_name: spheroseg-nginx-dev
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.dev.conf:/etc/nginx/conf.d/default.conf
      - ./letsencrypt/etc/letsencrypt:/etc/letsencrypt
      - ./letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt
      - ./letsencrypt/webroot:/var/www/letsencrypt
      - ./ssl/server.crt:/etc/nginx/ssl/server.crt
      - ./ssl/server.key:/etc/nginx/ssl/server.key
    command: >
      sh -c "
        mkdir -p /etc/nginx/ssl &&
        nginx -g 'daemon off;'
      "
    depends_on:
      - frontend-dev
      - backend
      - assets
    networks:
      - spheroseg-network

  # Nginx for production environment
  nginx-prod:
    profiles: ["prod"]
    container_name: spheroseg-nginx-prod
    image: nginx:alpine
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.complete.conf:/etc/nginx/nginx.conf
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - /var/www/certbot:/var/www/certbot:ro
    command: >
      sh -c "
        rm -f /etc/nginx/conf.d/default.conf &&
        nginx -g 'daemon off;'
      "
    depends_on:
      - frontend-prod
      - backend
      - assets
    networks:
      - spheroseg-network

  # Certbot for SSL certificates - dev profile
  certbot-dev:
    profiles: ["dev"]
    image: certbot/certbot
    container_name: spheroseg-certbot
    volumes:
      - ./letsencrypt/etc/letsencrypt:/etc/letsencrypt
      - ./letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt
      - ./letsencrypt/webroot:/var/www/letsencrypt
    depends_on:
      - nginx-dev
    command: "/bin/sh -c 'trap exit TERM; while :; do certbot renew --webroot -w /var/www/letsencrypt --quiet; sleep 12h & wait $${!}; done;'"
    networks:
      - spheroseg-network

  # Certbot for SSL certificates - prod profile
  certbot-prod:
    profiles: ["prod"]
    image: certbot/certbot
    container_name: spheroseg-certbot
    volumes:
      - ./letsencrypt/etc/letsencrypt:/etc/letsencrypt
      - ./letsencrypt/var/lib/letsencrypt:/var/lib/letsencrypt
      - ./letsencrypt/webroot:/var/www/letsencrypt
    depends_on:
      - nginx-prod
    command: "/bin/sh -c 'trap exit TERM; while :; do certbot renew --webroot -w /var/www/letsencrypt --quiet; sleep 12h & wait $${!}; done;'"
    networks:
      - spheroseg-network

  # Database
  db:
    profiles: ["dev", "prod"]
    image: postgres:14-alpine
    container_name: spheroseg-db
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M  # PostgreSQL memory limit
        reservations:
          memory: 256M
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-spheroseg}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./packages/backend/src/db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spheroseg"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ
  rabbitmq:
    profiles: ["dev", "prod"]
    image: rabbitmq:3-management-alpine
    container_name: spheroseg-rabbitmq
    restart: always
    ports:
      - "5672:5672" # AMQP protocol port
      - "15672:15672" # Management UI port
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER:-rabbitmq}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

  # Redis cache service
  redis:
    profiles: ["dev", "prod"]
    image: redis:7-alpine
    container_name: spheroseg-redis
    restart: always
    ports:
      - "6379:6379"
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    command: >
      redis-server
      --maxmemory 200mb
      --maxmemory-policy allkeys-lru
      --save 60 1
      --save 300 10
      --save 900 100
    volumes:
      - redis_data:/data
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API using TypeScript server
  backend:
    profiles: ["dev", "prod"]
    build:
      context: .
      dockerfile: ./packages/backend/Dockerfile.robust
      target: production
    container_name: spheroseg-backend
    restart: always
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      rabbitmq:
        condition: service_started
    ports:
      - "5001:5001"
      - "9230:9229"  # Node.js debugger port for development (mapped to avoid conflict)
    deploy:
      resources:
        limits:
          memory: 1024M  # Increased from 512M for better performance
        reservations:
          memory: 512M   # Increased from 256M
    environment:
      # Environment variables are now primarily managed in backend/src/config/index.ts
      # Only critical overrides or Docker-specific settings should be here.
      # Ensure APP_URL is set for callbacks from ML service
      - APP_URL=https://spherosegapp.utia.cas.cz # Public URL for emails
      - NODE_ENV=${NODE_ENV:-production} # Allow overriding NODE_ENV from host
      - LOG_LEVEL=${LOG_LEVEL:-info} # Allow overriding LOG_LEVEL from host
      - JWT_SECRET=${JWT_SECRET}
      - DATABASE_URL=postgresql://${DB_USER:-postgres}:${DB_PASSWORD}@${DB_HOST:-db}:${DB_PORT:-5432}/${DB_NAME:-spheroseg}
      # Memory configuration for Node.js (use 768MB of the 1024MB available)
      # Also enable garbage collection exposure for memory management
      - NODE_OPTIONS=--max-old-space-size=768 --expose-gc
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_SSL=${DB_SSL:-false}
      - SESSION_SECRET=${SESSION_SECRET}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET}
      - CSRF_SECRET=${CSRF_SECRET}
      - COOKIE_SECURE=true
      - REQUIRE_HTTPS=false
      - RABBITMQ_URL=amqp://${RABBITMQ_USER:-guest}:${RABBITMQ_PASS:-guest}@${RABBITMQ_HOST:-rabbitmq}:${RABBITMQ_PORT:-5672}
      - RABBITMQ_QUEUE=${RABBITMQ_QUEUE:-segmentation_tasks}
      - REDIS_URL=redis://${REDIS_HOST:-redis}:${REDIS_PORT:-6379}
      - ALLOWED_ORIGINS=${ALLOWED_ORIGINS:-http://localhost:3000,http://localhost:3003,http://localhost:3005,http://frontend:3000,http://frontend:80,*}
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost}
      - MAX_UPLOAD_SIZE=${MAX_UPLOAD_SIZE:-100mb}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
      - EMAIL_HOST=${EMAIL_HOST:-mail.utia.cas.cz}
      - EMAIL_PORT=${EMAIL_PORT:-25}
      - EMAIL_USER=${EMAIL_USER:-}
      - EMAIL_PASS=${EMAIL_PASS:-}
      - APP_URL=${FRONTEND_URL:-https://spherosegapp.utia.cas.cz}
      - ML_API_URL=http://ml:5002 # Add ML_API_URL for ML service communication
      - RATE_LIMIT_REQUESTS=${RATE_LIMIT_REQUESTS:-500}
      - RATE_LIMIT_WINDOW=${RATE_LIMIT_WINDOW:-60}
      - ENABLE_RATE_LIMIT=${ENABLE_RATE_LIMIT:-true}
      # Disable secret rotation temporarily
      - DISABLE_SECRET_ROTATION=${DISABLE_SECRET_ROTATION:-false}
      # API Security
      - ENABLE_API_KEYS=${ENABLE_API_KEYS:-true}
      - ML_SERVICE_API_KEY=${ML_SERVICE_API_KEY}
      # Performance settings
      - CONTAINER_MEMORY_LIMIT_MB=${CONTAINER_MEMORY_LIMIT_MB:-1024}
      - ENABLE_MANUAL_GC=${ENABLE_MANUAL_GC:-false}
      - NODE_OPTIONS=--max-old-space-size=768 --expose-gc
      - ENABLE_PERFORMANCE_MONITORING=${ENABLE_PERFORMANCE_MONITORING:-true}
      - COMPRESSION_LEVEL=${COMPRESSION_LEVEL:-6}
      - DB_POOL_MAX=${DB_POOL_MAX:-10}
      - DB_POOL_MIN=${DB_POOL_MIN:-2}
      # Cache settings
      - REDIS_CACHE_TTL=${REDIS_CACHE_TTL:-300}
      - ENABLE_REDIS_CACHE=${ENABLE_REDIS_CACHE:-true}
      - CACHE_TTL_PROJECT=${CACHE_TTL_PROJECT:-300}
      - CACHE_TTL_IMAGE_LIST=${CACHE_TTL_IMAGE_LIST:-60}
      - CACHE_TTL_USER=${CACHE_TTL_USER:-600}
      - CACHE_TTL_QUEUE_STATUS=${CACHE_TTL_QUEUE_STATUS:-5}
    volumes:
      - uploads_data:/app/uploads
      - ./packages/backend:/app/packages/backend:cached
      - ./packages/shared:/app/packages/shared:cached
      - ./packages/types:/app/packages/types:cached
      - backend_node_modules:/app/node_modules
    networks:
      - spheroseg-network
    working_dir: /app

  # ML Service
  ml:
    profiles: ["dev", "prod"]
    build:
      context: ./packages/ml
      dockerfile: Dockerfile
    container_name: spheroseg-ml
    restart: always
    depends_on:
      - rabbitmq # ML service now depends on RabbitMQ
    ports:
      - "5002:5002"  # Expose ML service port
    deploy:
      resources:
        limits:
          memory: 4096M  # ML service needs more memory for model loading on CPU
        reservations:
          memory: 2048M
    volumes:
      - ./packages/ml:/ML
      - uploads_data:/ML/uploads
    environment:
      - PYTHONUNBUFFERED=1
      - MODEL_PATH=/ML/checkpoint_epoch_9.pth.tar
      - DEBUG=false
      # RabbitMQ configuration for ML service
      - RABBITMQ_HOST=rabbitmq
      - RABBITMQ_PORT=5672
      - RABBITMQ_USER=${RABBITMQ_USER:-rabbitmq}
      - RABBITMQ_PASS=${RABBITMQ_PASS:-guest}
      - RABBITMQ_QUEUE=segmentation_tasks
      - RABBITMQ_PREFETCH_COUNT=4
      # API Key for service-to-service authentication
      - ML_SERVICE_API_KEY=${ML_SERVICE_API_KEY}
    networks:
      - spheroseg-network

  # Frontend development service with Vite server
  frontend-dev:
    profiles: ["dev"]
    build:
      context: .
      dockerfile: ./packages/frontend/Dockerfile
      target: development
    container_name: spheroseg-frontend-dev
    restart: always
    depends_on:
      - backend
      - assets
    ports:
      - "3000:3000"
    environment:
      # Use empty string for development to use Vite proxy
      - VITE_API_URL=
      - VITE_API_BASE_URL=/api
      - PORT=3000
      - VITE_NODE_ENV=development
      - HOST=0.0.0.0
      # Add standardized API path environment variables
      - VITE_API_AUTH_PREFIX=/auth
      - VITE_API_USERS_PREFIX=/users
      - VITE_API_PROXY_ENABLED=true
      - VITE_DOCKER_ENV=true
      - VITE_ASSETS_URL=http://localhost:3001
      # Development specific
      - VITE_LOG_LEVEL=debug
      - VITE_ENABLE_CONSOLE_LOGS=true
    volumes:
      - ./packages/frontend:/app:cached
      - ./packages/shared:/app/packages/shared:cached
      - ./packages/types:/app/packages/types:cached
      - frontend_node_modules:/app/node_modules
    networks:
      - spheroseg-network
    working_dir: /app

  # Frontend production service (builds and serves static files)
  frontend-prod:
    profiles: ["prod"]
    build:
      context: .
      dockerfile: ./packages/frontend/Dockerfile.prod
    container_name: spheroseg-frontend-prod
    restart: always
    depends_on:
      - backend
      - assets
    deploy:
      resources:
        limits:
          memory: 256M  # Static nginx server needs less memory
        reservations:
          memory: 128M
    ports:
      - "3000:80"
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3

  # Static assets server
  assets:
    profiles: ["dev", "prod"]
    image: nginx:alpine
    container_name: spheroseg-assets
    restart: always
    ports:
      - "3001:80"
    volumes:
      - ./packages/frontend-static:/usr/share/nginx/html
    networks:
      - spheroseg-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Database management UI
  adminer:
    profiles: ["dev"]
    image: adminer:latest
    container_name: spheroseg-adminer
    restart: always
    ports:
      - "8081:8080"
    depends_on:
      - db
    networks:
      - spheroseg-network

volumes:
  postgres_data:
  redis_data:
  uploads_data:
  frontend_uploads:
  frontend_node_modules:
  backend_node_modules:
  frontend_build:

networks:
  spheroseg-network:
    driver: bridge