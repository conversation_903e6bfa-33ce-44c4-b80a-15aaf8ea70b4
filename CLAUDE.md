# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with the SpheroSeg repository.

## Project Overview

SpheroSeg is a full-stack web application for cell segmentation in microscopy images using machine learning:
- **Frontend**: React + TypeScript + Vite with Radix UI components
- **Backend**: Express + TypeScript API server with Prisma ORM
- **ML Service**: Python Flask service using ResUNet for cell segmentation
- **Infrastructure**: Docker-based deployment with RabbitMQ, Redis, PostgreSQL

## Critical Best Practices

### 🔴 SSOT (Single Source of Truth) Principle
**NEVER duplicate code, types, or business logic!**
- Types & Interfaces → `packages/shared/types`
- Constants → `packages/shared/constants`
- Validation → `packages/shared/validation`
- Utilities → `packages/shared/utils`
- Before creating anything: Search if it exists first

### 🔴 Security First
**Current vulnerabilities to fix immediately:**
- Remove `new Function()` usage (analyze-missing-zh.js)
- Fix SQL injection risks (testDb.ts)
- Move hardcoded credentials to env variables
- Never log sensitive data (passwords, tokens, PII)
- Always validate and sanitize input

### 🔴 Error Debugging Workflow
When encountering errors, ALWAYS gather maximum context:
1. Check Docker logs: `docker-compose logs [service] --tail=100`
2. Check browser console for client-side errors
3. Verify database state if relevant
4. Look for patterns across services
5. Fix root cause, not symptoms

### 🔴 Subagent Usage (USE FREQUENTLY!)
**MAXIMIZE subagent usage to preserve context window:**

#### When to Use Subagents (ALWAYS when possible):
- File searching or code exploration
- Running multiple commands
- Debugging errors
- Testing workflows
- Repetitive fixes across multiple files
- Gathering information from logs
- Any task requiring 3+ tool calls

#### CRITICAL: Subagent Communication Rules
**Subagents have ZERO context from main conversation!** You MUST:
1. **Explain the project**: "SpheroSeg - React frontend, Express backend, PostgreSQL database"
2. **Provide exact file paths**: Never assume they know where files are
3. **Include full error messages**: Copy entire error output
4. **Specify expected output format**: Tell them exactly what to return
5. **Give step-by-step instructions**: Break down complex tasks
6. **Include relevant code snippets**: Show them what they're working with

#### Example of GOOD Subagent Instructions:
```
Project: SpheroSeg - Full-stack app with React frontend and Express backend
Current issue: Tests failing with "Cannot find module '../tokenService'"

Files to check:
- /home/<USER>/spheroseg/packages/backend/src/services/tokenService.ts
- /home/<USER>/spheroseg/packages/backend/src/__tests__/services/authService.test.ts

Task:
1. Check if tokenService.ts exists at the path above
2. If not, search for it in packages/backend/src
3. Find the correct relative import path
4. Return the corrected import statement

Expected output: The correct import line to replace the broken one
```

**USE SUBAGENTS FOR 80%+ OF YOUR WORK** - Main agent should primarily coordinate!

## Development Workflow

### Git Branch Rules
- **ALWAYS work in `dev` branch**
- Never commit directly to `main`
- Merge `dev` → `main` only for releases

### Essential Commands
```bash
# Development
docker-compose --profile dev up     # Start all services
npm run dev                         # Alternative with Turbo
npm run test                        # Run tests
npm run lint:fix                    # Fix linting
npm run format                      # Format code

# Production
docker-compose --profile prod up -d # Start production
docker-compose logs [service] -f    # View logs

# Database
npx prisma migrate dev              # Run migrations
npx prisma studio                   # Database UI
```

## Architecture Quick Reference

### Service Ports
- Frontend: `localhost:3000`
- Backend API: `localhost:5001`
- ML Service: `localhost:5002`
- PostgreSQL: `localhost:5432`
- Redis: `localhost:6379`
- RabbitMQ: `localhost:15672` (management)
- Adminer: `localhost:8081` (dev only)

### Key API Routes
- `/api/auth/*` - Authentication (JWT)
- `/api/projects/*` - Project management
- `/api/images/*` - Image operations
- `/api/health` - Health checks

### WebSocket Events
- `segmentation-update` - Task status updates
- `project-refresh` - Project data updates

## Current Issues & TODO

### Critical Security Issues
1. Function() constructor vulnerability
2. SQL injection in test helpers
3. Hardcoded credentials in docker-compose

### Test Infrastructure Issues
1. Backend tests blocked - no test database
2. Frontend tests failing - WebSocket not mocked
3. Import resolution errors
4. Coverage at 15% (target: 50%+)

### Code Quality Issues
- 92 linting errors to fix
- 100+ files need formatting
- Email service schema mismatch

## Testing Strategy

### Test Pyramid
- 70% Unit tests (fast, isolated)
- 20% Integration tests (API, database)
- 10% E2E tests (critical flows)

### Coverage Targets
- Critical paths: 80%
- Overall: 60%

## Performance Guidelines

### Backend
- Use Redis caching for frequent queries
- Implement pagination for lists
- Index database columns used in WHERE
- Connection pooling enabled

### Frontend
- Lazy load routes and components
- Virtualize long lists
- Debounce event handlers
- Optimize images (WebP, lazy loading)

## Debugging Common Issues

1. **Port conflicts**: Check ports 3000, 5001, 5002, 5432, 6379
2. **Database connection**: Ensure migrations are applied
3. **WebSocket issues**: Check CORS settings
4. **Memory issues**: Adjust heap size in docker-compose.yml
5. **ML service errors**: Verify model file exists

## Emergency Procedures

### Quick Fixes
```bash
# Backend crash loop
docker-compose restart backend

# Database connection issues
docker-compose exec db psql -U postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='spheroseg';"

# Redis memory issues
docker-compose exec redis redis-cli FLUSHDB

# Disk space issues
docker system prune -a --volumes
```

### Rollback Strategy
- Keep last 3 deployments ready
- Database migration rollbacks tested
- Feature flags for instant disable
- Automated rollback on health failures

## Documentation

- Project roadmap: `TODO.md`
- Deployment guide: `DEPLOYMENT.md`
- Development setup: `DEVELOPMENT_SETUP.md`
- Test report: `TEST_REPORT.md`