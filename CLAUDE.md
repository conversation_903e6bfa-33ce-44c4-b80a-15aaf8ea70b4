# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with the SpheroSeg repository.

## Project Overview

SpheroSeg is a full-stack web application for cell segmentation in microscopy images using machine learning:
- **Frontend**: React + TypeScript + Vite with Radix UI components
- **Backend**: Express + TypeScript API server with Prisma ORM
- **ML Service**: Python Flask service using ResUNet for cell segmentation
- **Infrastructure**: Docker-based deployment with RabbitMQ, Redis, PostgreSQL

## Critical Best Practices

### 🔴 SSOT (Single Source of Truth) Principle
**NEVER duplicate code, types, or business logic!**
- Types & Interfaces → `packages/shared/types`
- Constants → `packages/shared/constants`
- Validation → `packages/shared/validation`
- Utilities → `packages/shared/utils`
- Before creating anything: Search if it exists first

### 🔴 Security First
**Current vulnerabilities to fix immediately:**
- Remove `new Function()` usage (analyze-missing-zh.js)
- Fix SQL injection risks (testDb.ts)
- Move hardcoded credentials to env variables
- Never log sensitive data (passwords, tokens, PII)
- Always validate and sanitize input

### 🔴 Error Debugging Workflow
When encountering errors, ALWAYS gather maximum context:
1. Check Docker logs: `docker-compose logs [service] --tail=100`
2. Check browser console for client-side errors
3. Verify database state if relevant
4. Look for patterns across services
5. Fix root cause, not symptoms

### 🔴 Subagent Usage (USE FREQUENTLY!)
**MAXIMIZE subagent usage to preserve context window:**
Subagents can handle independent tasks without cluttering the main conversation. This allows you to:
- Focus on high-level coordination in main conversation
- Offload detailed work to subagents
- Preserve context for future reference

IMPORTANT: ALWAYS EXPLAIN THE PROJECT CONTEXT TO SUBAGENTS!

#### When to Use Subagents (ALWAYS when possible):
- File searching or code exploration
- Running multiple commands
- Debugging errors
- Testing workflows
- Repetitive fixes across multiple files
- Gathering information from logs
- Any task requiring 3+ tool calls

#### CRITICAL: Subagent Communication Rules
**Subagents have ZERO context from main conversation!** You MUST:
1. **Explain the project**: "SpheroSeg - React frontend, Express backend, PostgreSQL database"
2. **Provide exact file paths**: Never assume they know where files are
3. **Include full error messages**: Copy entire error output
4. **Specify expected output format**: Tell them exactly what to return
5. **Give step-by-step instructions**: Break down complex tasks
6. **Include relevant code snippets**: Show them what they're working with

#### Example of GOOD Subagent Instructions:
```
Project: SpheroSeg - Full-stack app with React frontend and Express backend
Current issue: Tests failing with "Cannot find module '../tokenService'"

Files to check:
- /home/<USER>/spheroseg/packages/backend/src/services/tokenService.ts
- /home/<USER>/spheroseg/packages/backend/src/__tests__/services/authService.test.ts

Task:
1. Check if tokenService.ts exists at the path above
2. If not, search for it in packages/backend/src
3. Find the correct relative import path
4. Return the corrected import statement

Expected output: The correct import line to replace the broken one
```

**USE SUBAGENTS FOR 80%+ OF YOUR WORK** - Main agent should primarily coordinate!

## Development Workflow

### Git Branch Rules
- **ALWAYS work in `dev` branch**
- Never commit directly to `main`
- Merge `dev` → `main` only for releases

### Essential Commands

#### Quick Start Scripts
```bash
# Setup project from scratch
npm run setup                       # Install, build, migrate

# Development workflows  
npm run quick-start                 # Build shared + start frontend & backend
npm run dev                         # Start all packages in development
npm run dev:frontend               # Start only frontend
npm run dev:backend                # Start only backend

# Quality checks
npm run full-check                  # Code check + unit/integration tests
npm run pre-commit                  # Code fix + unit tests
npm run pre-deploy                  # Full check + prod build + e2e
npm run status                      # Quick type-check across all packages
```

#### Core Development Commands
```bash
# Building
npm run build                       # Build all packages
npm run build:prod                  # Production build
npm run build:frontend             # Build only frontend
npm run build:backend              # Build only backend

# Testing
npm run test                        # Run all tests
npm run test:unit                   # Unit tests only
npm run test:integration            # Integration tests only
npm run test:coverage               # Tests with coverage
npm run test:watch                  # Watch mode

# Code Quality
npm run code:check                  # Lint + format check + type check
npm run code:fix                    # Fix lint + format issues
npm run lint                        # Lint all packages
npm run format                      # Format all packages

# Database
npm run db:migrate                  # Run migrations
npm run db:migrate:up               # Apply migrations
npm run db:migrate:down             # Rollback migrations
npm run db:migrate:status           # Check migration status

# Utilities
npm run clean                       # Clean build outputs
npm run clean:all                   # Full reset (includes node_modules)
npm run reset                       # Clean all + setup
```

#### Docker Commands
```bash
# Development
docker-compose --profile dev up     # Start all services
docker-compose logs [service] -f    # View logs

# Production  
docker-compose --profile prod up -d # Start production
```

## Architecture Quick Reference

### Service Ports
- Frontend: `localhost:3000`
- Backend API: `localhost:5001`
- ML Service: `localhost:5002`
- PostgreSQL: `localhost:5432`
- Redis: `localhost:6379`
- RabbitMQ: `localhost:15672` (management)
- Adminer: `localhost:8081` (dev only)

### Key API Routes
- `/api/auth/*` - Authentication (JWT)
- `/api/projects/*` - Project management
- `/api/images/*` - Image operations
- `/api/health` - Health checks

### WebSocket Events
- `segmentation-update` - Task status updates
- `project-refresh` - Project data updates

## Current Issues & TODO

### Critical Security Issues
1. Function() constructor vulnerability
2. SQL injection in test helpers
3. Hardcoded credentials in docker-compose

### Test Infrastructure Issues
1. Backend tests blocked - no test database
2. Frontend tests failing - WebSocket not mocked
3. Import resolution errors
4. Coverage at 15% (target: 50%+)

### Code Quality Issues
- 92 linting errors to fix
- 100+ files need formatting
- Email service schema mismatch

## Testing Strategy

### Test Pyramid
- 70% Unit tests (fast, isolated)
- 20% Integration tests (API, database)
- 10% E2E tests (critical flows)

### Coverage Targets
- Critical paths: 80%
- Overall: 60%

## Performance Guidelines

### Backend
- Use Redis caching for frequent queries
- Implement pagination for lists
- Index database columns used in WHERE
- Connection pooling enabled

### Frontend
- Lazy load routes and components
- Virtualize long lists
- Debounce event handlers
- Optimize images (WebP, lazy loading)

## Debugging Common Issues

1. **Port conflicts**: Check ports 3000, 5001, 5002, 5432, 6379
2. **Database connection**: Ensure migrations are applied
3. **WebSocket issues**: Check CORS settings
4. **Memory issues**: Adjust heap size in docker-compose.yml
5. **ML service errors**: Verify model file exists

## Emergency Procedures

### Quick Fixes
```bash
# Backend crash loop
docker-compose restart backend

# Database connection issues
docker-compose exec db psql -U postgres -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname='spheroseg';"

# Redis memory issues
docker-compose exec redis redis-cli FLUSHDB

# Disk space issues
docker system prune -a --volumes
```

### Rollback Strategy
- Keep last 3 deployments ready
- Database migration rollbacks tested
- Feature flags for instant disable
- Automated rollback on health failures

## Documentation

- Project roadmap: `TODO.md`
- Deployment guide: `DEPLOYMENT.md`
- Development setup: `DEVELOPMENT_SETUP.md`
- Test report: `TEST_REPORT.md`